// src/services/partialBudgetService.js

import api from "./api";

// --- Partial Budget Functions ---
export const getPartialBudgets = () => api.get("/planning/partial-budgets/");
export const getPartialBudgetDetail = (id) => api.get(`/planning/partial-budgets/${id}/`);
export const createPartialBudget = (data) => api.post("/planning/partial-budgets/", data);
export const updatePartialBudget = (id, data) => api.patch(`/planning/partial-budgets/${id}/`, data);
export const deletePartialBudget = (id) => api.delete(`/planning/partial-budgets/${id}/`);

// --- Partial Budget Item Functions ---
export const createPartialBudgetItem = (data) => {
  console.log("partialBudgetService: Creating item with data:", data);
  return api.post("/planning/partial-budget-items/", data);
};
export const updatePartialBudgetItem = (id, data) => api.put(`/planning/partial-budget-items/${id}/`, data);
export const deletePartialBudgetItem = (id) => api.delete(`/planning/partial-budget-items/${id}/`);




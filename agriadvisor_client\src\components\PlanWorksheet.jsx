// src/components/PlanWorksheet.jsx

import React, { useState, useMemo } from "react";
import { Tab, Tabs, Card, Table, Button, Row, Col } from "react-bootstrap";
import AssessmentForm from "./AssessmentForm";
import AnalysisTab from "./AnalysisTab";
import PlanItemModal from "./PlanItemModal";
import PigAssumptionsTab from "./specialized/PigAssumptionsTab";
import { updateEnterprisePlan, createPlanItem, updatePlanItem, deletePlanItem } from "../services/planningService";

// --- Helper Functions and Child Components ---
const formatCurrency = (number) => {
  const num = parseFloat(number || 0);
  return new Intl.NumberFormat("en-GB", { style: "currency", currency: "USD" }).format(num).replace(/,/g, " ");
};

const PlanSection = ({ title, items, onAdd, onEdit, onDelete }) => (
  <Card className="mb-3">
    <Card.Header className="d-flex justify-content-between align-items-center">
      <h5>{title}</h5>
      <Button variant="outline-primary" size="sm" onClick={onAdd}>
        + Add Item
      </Button>
    </Card.Header>
    <Card.Body>
      <Table striped bordered size="sm" responsive>
        <thead>
          <tr>
            <th>Item</th>
            <th>Category</th>
            <th>Qty</th>
            <th>Unit</th>
            <th>Price/Unit</th>
            <th className="text-end">Projected Total</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {items.map((item) => (
            <tr key={item.id}>
              <td>{item.item_name}</td>
              <td>{item.category}</td>
              <td>{item.quantity}</td>
              <td>{item.unit}</td>
              <td>{formatCurrency(item.price_per_unit)}</td>
              <td className="text-end">{formatCurrency(item.projected_amount)}</td>
              <td>
                <Button variant="outline-secondary" size="sm" className="me-1 py-0 px-1" onClick={() => onEdit(item)}>
                  <i className="bi bi-pencil-fill"></i>
                </Button>
                <Button variant="outline-danger" size="sm" className="py-0 px-1" onClick={() => onDelete(item.id)}>
                  <i className="bi bi-trash-fill"></i>
                </Button>
              </td>
            </tr>
          ))}
          {items.length === 0 && (
            <tr>
              <td colSpan="7" className="text-center">
                No items.
              </td>
            </tr>
          )}
        </tbody>
      </Table>
    </Card.Body>
  </Card>
);

const Summary = ({ totals }) => (
  <Card bg="light">
    <Card.Header as="h5">Budget Summary</Card.Header>
    <Card.Body>
      <Row>
        <Col>
          <strong>Category</strong>
        </Col>
        <Col className="text-end">
          <strong>Projected</strong>
        </Col>
        <Col className="text-end">
          <strong>Actual</strong>
        </Col>
      </Row>
      <hr className="my-2" />
      <Row>
        <Col>Total Income:</Col>
        <Col className="text-end">{formatCurrency(totals.projIncome)}</Col>
        <Col className="text-end">{formatCurrency(totals.actIncome)}</Col>
      </Row>
      <Row>
        <Col>Total Variable Costs:</Col>
        <Col className="text-end">({formatCurrency(totals.projVar)})</Col>
        <Col className="text-end">({formatCurrency(totals.actVar)})</Col>
      </Row>
      <Row>
        <Col>Total Fixed Costs:</Col>
        <Col className="text-end">({formatCurrency(totals.projFix)})</Col>
        <Col className="text-end">({formatCurrency(totals.actFix)})</Col>
      </Row>
      <hr />
      <Row className="fw-bold text-success">
        <Col>Net Profit / (Loss):</Col>
        <Col className="text-end">{formatCurrency(totals.projIncome - totals.projVar - totals.projFix)}</Col>
        <Col className="text-end">{formatCurrency(totals.actIncome - totals.actVar - totals.actFix)}</Col>
      </Row>
    </Card.Body>
  </Card>
);

// --- Main PlanWorksheet Component ---
const PlanWorksheet = ({ plan, onRefresh }) => {
  const [isSaving, setIsSaving] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [itemType, setItemType] = useState("income");

  const handleSaveAssessment = async (assessmentData) => {
    setIsSaving(true);
    try {
      await updateEnterprisePlan(plan.id, { assessment_data: assessmentData });
      onRefresh();
      alert("Assessment saved successfully!");
    } catch (error) {
      console.error("Failed to save assessment", error);
      alert("Failed to save assessment data.");
    } finally {
      setIsSaving(false);
    }
  };

  const handleShowAddItemModal = (type) => {
    setItemType(type);
    setEditingItem(null);
    setShowModal(true);
  };
  const handleShowEditItemModal = (item) => {
    setItemType(item.item_type);
    setEditingItem(item);
    setShowModal(true);
  };
  const handleCloseModal = () => setShowModal(false);

  const handleSaveItem = async (itemData) => {
    const dataToSave = { ...itemData, plan: plan.id };
    try {
      if (editingItem) {
        await updatePlanItem(editingItem.id, dataToSave);
      } else {
        await createPlanItem(dataToSave);
      }
      onRefresh();
      handleCloseModal();
    } catch (error) {
      alert("Failed to save item.");
    }
  };

  const handleDeleteItem = async (id) => {
    if (window.confirm("Delete this item?")) {
      try {
        await deletePlanItem(id);
        onRefresh();
      } catch (error) {
        alert("Failed to delete item.");
      }
    }
  };

  const incomeItems = plan.plan_items.filter((i) => i.item_type === "income");
  const variableCostItems = plan.plan_items.filter((i) => i.item_type === "variable");
  const fixedCostItems = plan.plan_items.filter((i) => i.item_type === "fixed");

  const totals = useMemo(() => {
    const calculate = (items) =>
      items.reduce(
        (acc, item) => {
          acc.proj += parseFloat(item.projected_amount || 0);
          acc.act += parseFloat(item.actual_amount || 0);
          return acc;
        },
        { proj: 0, act: 0 }
      );
    const income = calculate(incomeItems);
    const variable = calculate(variableCostItems);
    const fixed = calculate(fixedCostItems);
    return {
      projIncome: income.proj,
      actIncome: income.act,
      projVar: variable.proj,
      actVar: variable.act,
      projFix: fixed.proj,
      actFix: fixed.act,
    };
  }, [plan.plan_items]);

  return (
    <div>
      <Tabs defaultActiveKey="assessment" id="plan-worksheet-tabs" className="mb-3" justify>
        <Tab eventKey="assessment" title="1. Assessment">
          <AssessmentForm plan={plan} onSave={handleSaveAssessment} isSaving={isSaving} />
        </Tab>

        <Tab eventKey="assumptions" title="2. Assumptions">
          {plan.model_type === "generic" && (
            <Card>
              <Card.Body>This plan uses a generic budget. Financials are entered manually in the next tab.</Card.Body>
            </Card>
          )}
          {plan.model_type === "pig_production" && <PigAssumptionsTab plan={plan} onUpdate={onRefresh} />}
          {/* Add other <...AssumptionsTab /> components here as you build them */}
          {plan.model_type !== "generic" && plan.model_type !== "pig_production" && (
            <p>Assumption form for {plan.model_type.replace("_", " ")} coming soon.</p>
          )}
        </Tab>

        <Tab eventKey="financials" title="3. Financials">
          {plan.model_type === "generic" ? (
            <>
              <PlanSection
                title="Sources of Income"
                items={incomeItems}
                onAdd={() => handleShowAddItemModal("income")}
                onEdit={handleShowEditItemModal}
                onDelete={handleDeleteItem}
              />
              <PlanSection
                title="Variable Costs"
                items={variableCostItems}
                onAdd={() => handleShowAddItemModal("variable")}
                onEdit={handleShowEditItemModal}
                onDelete={handleDeleteItem}
              />
              <PlanSection
                title="Fixed Costs"
                items={fixedCostItems}
                onAdd={() => handleShowAddItemModal("fixed")}
                onEdit={handleShowEditItemModal}
                onDelete={handleDeleteItem}
              />
              <Summary totals={totals} />
            </>
          ) : (
            <Card>
              <Card.Body>
                For this model, the financial summary is automatically calculated based on the Assumptions and is
                displayed in the Analysis tab.
              </Card.Body>
            </Card>
          )}
        </Tab>

        <Tab eventKey="analysis" title="4. Analysis & KPIs">
          <AnalysisTab planId={plan.id} />
        </Tab>
      </Tabs>

      {plan.model_type === "generic" && (
        <PlanItemModal
          show={showModal}
          onHide={handleCloseModal}
          onSave={handleSaveItem}
          item={editingItem}
          itemType={itemType}
          planId={plan.id}
        />
      )}
    </div>
  );
};

export default PlanWorksheet;




// src/components/specialized/PigAssumptionsTab.jsx

import React, { useState, useEffect, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON>, Card, Row, <PERSON>, Spin<PERSON>, Alert } from "react-bootstrap";
import { getPigAssumptions, updatePigAssumptions } from "../../services/planningService";

const PigAssumptionsTab = ({ plan, onUpdate }) => {
  const [assumptions, setAssumptions] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (plan.id) {
      const fetchAssumptions = async () => {
        try {
          setLoading(true);
          const response = await getPigAssumptions(plan.id);
          setAssumptions(response.data);
        } catch (err) {
          setError("Could not load pig production assumptions.");
        } finally {
          setLoading(false);
        }
      };
      fetchAssumptions();
    }
  }, [plan.id]);

  const handleChange = (e) => {
    setAssumptions({ ...assumptions, [e.target.name]: e.target.value });
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      await updatePigAssumptions(plan.id, assumptions);
      alert("Assumptions saved!");
      onUpdate(); // This refreshes the parent, which will re-run the analysis
    } catch (err) {
      alert("Failed to save assumptions.");
    } finally {
      setIsSaving(false);
    }
  };

  // Real-time KPI calculations on the frontend
  const calculatedKPIs = useMemo(() => {
    if (!assumptions) return {};
    const pigsBorn = parseFloat(assumptions.pigs_born_alive_per_litter) || 0;
    const preWeaningMortality = parseFloat(assumptions.pre_weaning_mortality_percent) / 100 || 0;
    const littersPerYear = parseFloat(assumptions.litters_per_sow_per_year) || 0;

    const pigsWeanedPerLitter = pigsBorn * (1 - preWeaningMortality);
    const pwsy = pigsWeanedPerLitter * littersPerYear;

    return {
      pigsWeanedPerLitter: pigsWeanedPerLitter.toFixed(2),
      pwsy: pwsy.toFixed(2),
    };
  }, [assumptions]);

  if (loading)
    return (
      <div className="text-center p-5">
        <Spinner animation="border" />
      </div>
    );
  if (error) return <Alert variant="danger">{error}</Alert>;
  if (!assumptions) return <Alert variant="info">No assumption data found for this plan.</Alert>;

  return (
    <Card>
      <Card.Header as="h5">Assumptions & Herd Productivity (Pig Production)</Card.Header>
      <Card.Body>
        <Form>
          {/* --- Herd Structure & Production --- */}
          <h6>Production Parameters</h6>
          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Number of Sows</Form.Label>
                <Form.Control
                  type="number"
                  name="number_of_sows"
                  value={assumptions.number_of_sows || ''}
                  onChange={handleChange}
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Farrowing Rate (%)</Form.Label>
                <Form.Control
                  type="number"
                  step="0.1"
                  name="farrowing_rate_percent"
                  value={assumptions.farrowing_rate_percent}
                  onChange={handleChange}
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Litters per Sow per Year</Form.Label>
                <Form.Control
                  type="number"
                  step="0.1"
                  name="litters_per_sow_per_year"
                  value={assumptions.litters_per_sow_per_year}
                  onChange={handleChange}
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Pigs Born Alive per Litter</Form.Label>
                <Form.Control
                  type="number"
                  step="0.1"
                  name="pigs_born_alive_per_litter"
                  value={assumptions.pigs_born_alive_per_litter}
                  onChange={handleChange}
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Pre-Weaning Mortality (%)</Form.Label>
                <Form.Control
                  type="number"
                  step="0.1"
                  name="pre_weaning_mortality_percent"
                  value={assumptions.pre_weaning_mortality_percent}
                  onChange={handleChange}
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Post-Weaning Mortality (%)</Form.Label>
                <Form.Control
                  type="number"
                  step="0.1"
                  name="post_weaning_mortality_percent"
                  value={assumptions.post_weaning_mortality_percent}
                  onChange={handleChange}
                />
              </Form.Group>
            </Col>
          </Row>
          <hr />

          {/* --- Growth & Price --- */}
          <h6>Growth & Price Assumptions</h6>
          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Sale Weight (kg)</Form.Label>
                <Form.Control
                  type="number"
                  step="0.1"
                  name="sale_weight_kg"
                  value={assumptions.sale_weight_kg}
                  onChange={handleChange}
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Feed Conversion Ratio (FCR)</Form.Label>
                <Form.Control
                  type="number"
                  step="0.01"
                  name="feed_conversion_ratio"
                  value={assumptions.feed_conversion_ratio}
                  onChange={handleChange}
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Sale Price per kg (Live Weight)</Form.Label>
                <Form.Control
                  type="number"
                  step="0.01"
                  name="sale_price_per_kg"
                  value={assumptions.sale_price_per_kg}
                  onChange={handleChange}
                />
              </Form.Group>
            </Col>
          </Row>
          <hr />

          {/* --- Calculated KPIs --- */}
          <h6>Calculated KPIs (Real-time)</h6>
          <Row>
            <Col md={6}>
              <p>
                <strong>Pigs Weaned per Litter:</strong> {calculatedKPIs.pigsWeanedPerLitter}
              </p>
            </Col>
            <Col md={6}>
              <p>
                <strong>Pigs Weaned per Sow per Year (PWSY):</strong> {calculatedKPIs.pwsy}
              </p>
            </Col>
          </Row>

          <div className="text-end mt-3">
            <Button onClick={handleSave} disabled={isSaving}>
              {isSaving ? "Saving..." : "Save Assumptions"}
            </Button>
          </div>
        </Form>
      </Card.Body>
    </Card>
  );
};
export default PigAssumptionsTab;




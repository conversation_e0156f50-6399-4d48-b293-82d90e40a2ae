// src/layouts/ExpertLayout.jsx

import React from "react";
import { Outlet, NavLink } from "react-router-dom";
import { Navbar, Container, Nav, NavDropdown } from "react-bootstrap";
import useAuth from "../hooks/useAuth";
import AnnouncementBanner from "../components/AnnouncementBanner";

const ExpertLayout = () => {
  const { user, logout } = useAuth();

  return (
    <div>
      <Navbar bg="dark" variant="dark" expand="lg">
        <Container>
          <Navbar.Brand as={NavLink} to="/">
            <i className="bi bi-person-check-fill me-2"></i>Expert Dashboard
          </Navbar.Brand>
          <Navbar.Toggle aria-controls="expert-navbar-nav" />
          <Navbar.Collapse id="expert-navbar-nav">
            <Nav className="me-auto">

              <Nav.Link as={NavLink} to="/expert/dashboard">
                Dashboard
              </Nav.Link>

              <Nav.Link as={NavLink} to="/expert/schedule">
                My Schedule
              </Nav.Link>

              <Nav.Link as={NavLink} to="/expert/my-farmers">
                My Farmers
              </Nav.Link>

              <Nav.Link as={NavLink} to="/expert/partial-budgets">
                Partial Budgets
              </Nav.Link>

              <Nav.Link as={NavLink} to="/expert/planning">
                Enterprise Planning
              </Nav.Link>

              <Nav.Link as={NavLink} to="/expert/availability">
                My Availability
              </Nav.Link>

              <Nav.Link as={NavLink} to="/expert/action-plans">
                Action Plans
              </Nav.Link>

              <Nav.Link as={NavLink} to="/expert/payouts">
                Payouts
              </Nav.Link>
              
            </Nav>
            <Nav>
              <NavDropdown title={`Welcome, ${user?.first_name || user?.username}`} id="basic-nav-dropdown" align="end">
                <NavDropdown.Item as={NavLink} to="/expert/profile">
                  My Profile
                </NavDropdown.Item>
                <NavDropdown.Divider />
                <NavDropdown.Item onClick={logout}>Logout</NavDropdown.Item>
              </NavDropdown>
            </Nav>
          </Navbar.Collapse>
        </Container>
      </Navbar>
      <AnnouncementBanner />
      <Container fluid className="mt-4 p-4">
        <Outlet />
      </Container>
    </div>
  );
};

export default ExpertLayout;



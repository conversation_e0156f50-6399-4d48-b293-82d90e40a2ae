// src/services/knowledgeBaseService.js
import api from "./api";

// --- Admin Category Functions ---
export const getAdminCategories = async () => {
  try {
    const response = await api.get("/knowledge-base/admin/categories/");

    // Handle different response formats
    console.log("Categories API response:", response); // Debug log

    // If the response is already an array (direct response)
    if (Array.isArray(response)) {
      return response;
    }

    // If the response has a data property that's an array
    if (response.data && Array.isArray(response.data)) {
      return response.data;
    }

    // If the response has a results property (Django pagination)
    if (response.results && Array.isArray(response.results)) {
      return response.results;
    }

    // If we can't find the categories array, return empty array
    console.error("Unexpected categories response format:", response);
    return [];
  } catch (error) {
    console.error("Error fetching categories:", error);
    throw error;
  }
};

export const createCategory = async (data) => {
  try {
    const response = await api.post("/knowledge-base/admin/categories/", data);

    // Handle different response formats
    if (Array.isArray(response)) {
      return response; // If it returns the created category as array (unlikely)
    }
    return response.data; // Return the data property
  } catch (error) {
    console.error("Error creating category:", error);
    console.error("Error response:", error.response?.data);
    throw error;
  }
};

export const updateCategory = async (id, data) => {
  try {
    const response = await api.put(`/knowledge-base/admin/categories/${id}/`, data);

    if (Array.isArray(response)) {
      return response;
    }
    return response.data;
  } catch (error) {
    console.error("Error updating category:", error);
    console.error("Error response:", error.response?.data);
    throw error;
  }
};

export const deleteCategory = async (id) => {
  try {
    const response = await api.delete(`/knowledge-base/admin/categories/${id}/`);

    if (Array.isArray(response)) {
      return response;
    }
    return response.data;
  } catch (error) {
    console.error("Error deleting category:", error);
    console.error("Error response:", error.response?.data);
    throw error;
  }
};

// --- Admin Article Functions ---
export const getAdminArticles = async (params = {}) => {
  try {
    const response = await api.get("/knowledge-base/admin/articles/", { params });
    return response;
  } catch (error) {
    console.error("Error fetching admin articles:", error);
    console.error("Error response:", error.response?.data);
    throw error;
  }
};

export const getArticle = async (id) => {
  try {
    const response = await api.get(`/knowledge-base/admin/articles/${id}/`);
    return response;
  } catch (error) {
    console.error("Error fetching article:", error);
    console.error("Error response:", error.response?.data);
    throw error;
  }
};

export const createArticle = async (articleData) => {
  try {
    const response = await api.post("/knowledge-base/admin/articles/", articleData);
    return response.data;
  } catch (error) {
    console.error("Error creating article:", error);
    console.error("Error response:", error.response?.data);
    throw error;
  }
};

export const updateArticle = async (id, articleData) => {
  try {
    const response = await api.put(`/knowledge-base/admin/articles/${id}/`, articleData);
    return response.data;
  } catch (error) {
    console.error("Error updating article:", error);
    console.error("Error response:", error.response?.data);
    throw error;
  }
};

export const deleteArticle = async (id) => {
  try {
    const response = await api.delete(`/knowledge-base/admin/articles/${id}/`);
    return response.data;
  } catch (error) {
    console.error("Error deleting article:", error);
    console.error("Error response:", error.response?.data);
    throw error;
  }
};

// --- Public Article Functions (for farmers/experts) ---
export const getPublicArticles = async (params = {}) => {
  try {
    const response = await api.get("/knowledge-base/articles/", { params });
    return response.data;
  } catch (error) {
    console.error("Error fetching public articles:", error);
    throw error;
  }
};

export const getPublicArticle = async (id) => {
  try {
    const response = await api.get(`/knowledge-base/articles/${id}/`);
    return response.data;
  } catch (error) {
    console.error("Error fetching public article:", error);
    throw error;
  }
};

export const getPublicCategories = async () => {
  try {
    const response = await api.get("/knowledge-base/categories/");
    return response.data;
  } catch (error) {
    console.error("Error fetching public categories:", error);
    throw error;
  }
};

// --- Utility Functions ---
export const uploadImage = async (file) => {
  try {
    const formData = new FormData();
    formData.append("image", file);
    const response = await api.post("/knowledge-base/upload-image/", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error uploading image:", error);
    console.error("Error response:", error.response?.data);
    throw error;
  }
};

export const generateSlug = async (title) => {
  try {
    const response = await api.post("/knowledge-base/generate-slug/", { title });
    return response.data;
  } catch (error) {
    console.error("Error generating slug:", error);
    console.error("Error response:", error.response?.data);
    throw error;
  }
};

// --- Test Functions ---
export const testArticleEndpoint = async () => {
  try {
    const testData = {
      title: "Test Article " + Date.now(),
      category: 1,
      content: {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [
              {
                type: "text",
                text: "Test content for the article",
              },
            ],
          },
        ],
      },
      status: "draft",
    };

    const response = await api.post("/knowledge-base/admin/articles/", testData);
    console.log("Test successful:", response.data);
    return response.data;
  } catch (error) {
    console.error("Test failed:", error.response?.data || error.message);
    throw error;
  }
};

export const testCategoryEndpoint = async () => {
  try {
    const testData = {
      name: "Test Category " + Date.now(),
      description: "Test category description",
    };

    const response = await api.post("/knowledge-base/admin/categories/", testData);
    console.log("Category test successful:", response.data);
    return response.data;
  } catch (error) {
    console.error("Category test failed:", error.response?.data || error.message);
    throw error;
  }
};

// --- Batch Operations ---
export const bulkUpdateArticles = async (articleIds, updates) => {
  try {
    const response = await api.patch("/knowledge-base/admin/articles/bulk-update/", {
      ids: articleIds,
      updates,
    });
    return response.data;
  } catch (error) {
    console.error("Error bulk updating articles:", error);
    console.error("Error response:", error.response?.data);
    throw error;
  }
};

export const bulkDeleteArticles = async (articleIds) => {
  try {
    const response = await api.post("/knowledge-base/admin/articles/bulk-delete/", {
      ids: articleIds,
    });
    return response.data;
  } catch (error) {
    console.error("Error bulk deleting articles:", error);
    console.error("Error response:", error.response?.data);
    throw error;
  }
};

// --- Statistics ---
export const getKnowledgeBaseStats = async () => {
  try {
    const response = await api.get("/knowledge-base/admin/stats/");
    return response.data;
  } catch (error) {
    console.error("Error fetching knowledge base stats:", error);
    throw error;
  }
};

// Default export with all functions
export default {
  // Admin Category Functions
  getAdminCategories,
  createCategory,
  updateCategory,
  deleteCategory,

  // Admin Article Functions
  getAdminArticles,
  getArticle,
  createArticle,
  updateArticle,
  deleteArticle,

  // Public Functions
  getPublicArticles,
  getPublicArticle,
  getPublicCategories,

  // Utility Functions
  uploadImage,
  generateSlug,

  // Test Functions
  testArticleEndpoint,
  testCategoryEndpoint,

  // Batch Operations
  bulkUpdateArticles,
  bulkDeleteArticles,

  // Statistics
  getKnowledgeBaseStats,
};


export const testCategoriesEndpoint = async () => {
  try {
    console.log("Testing categories endpoint...");
    const response = await api.get("/knowledge-base/admin/categories/");
    console.log("Categories endpoint response:", response);

    // Check what format the response is in
    if (Array.isArray(response)) {
      console.log("Response is direct array");
      return response;
    } else if (response.data && Array.isArray(response.data)) {
      console.log("Response has data array");
      return response.data;
    } else if (response.results && Array.isArray(response.results)) {
      console.log("Response has results array (pagination)");
      return response.results;
    } else {
      console.log("Unknown response format:", response);
      return [];
    }
  } catch (error) {
    console.error("Categories endpoint test failed:", error);
    throw error;
  }
};





// // src/services/knowledgeBaseService.js

// import api from "./api";

// // --- Admin Functions ---
// export const getAdminCategories = () => api.get("/knowledge-base/admin/categories/");

// export const createCategory = (data) => api.post("/knowledge-base/admin/categories/", data);

// export const updateCategory = (id, data) => api.put(`/knowledge-base/admin/categories/${id}/`, data);

// export const deleteCategory = (id) => api.delete(`/knowledge-base/admin/categories/${id}/`);

// export const getArticle = async (id) => {
//   try {
//     const response = await api.get(`/knowledge-base/admin/articles/${id}/`);
//     return response.data;
//   } catch (error) {
//     console.error('Error fetching article:', error);
//     throw error;
//   }
// };

// export const createArticle = async (articleData) => {
//   try {
//     const response = await api.post("/knowledge-base/admin/articles/", articleData);
//     return response.data;
//   } catch (error) {
//     console.error("Error creating article:", error);
//     console.error("Error response:", error.response);
//     throw error;
//   }
// };

// export const updateArticle = async (id, articleData) => {
//   try {
//     const response = await api.put(`/knowledge-base/admin/articles/${id}/`, articleData);
//     return response.data;
//   } catch (error) {
//     console.error("Error updating article:", error);
//     console.error("Error response:", error.response);
//     throw error;
//   }
// };

// export const uploadImage = async (file) => {
//   try {
//     const formData = new FormData();
//     formData.append('image', file);
//     const response = await api.post('/knowledge-base/upload-image/', formData, {
//       headers: {
//         'Content-Type': 'multipart/form-data',
//       },
//     });
//     return response.data.url;
//   } catch (error) {
//     console.error('Error uploading image:', error);
//     throw error;
//   }
// };

// export const generateSlug = async (title) => {
//   try {
//     const response = await api.post('/knowledge-base/generate-slug/', { title });
//     return response.data;
//   } catch (error) {
//     console.error('Error generating slug:', error);
//     throw error;
//   }
// };

// // Add this function to your knowledgeBaseService.js for testing
// export const testArticleEndpoint = async () => {
//   try {
//     const testData = {
//       title: 'Test Article',
//       category: 1, // Make sure this category exists
//       content: { type: 'doc', content: [{ type: 'paragraph', content: [{ type: 'text', text: 'Test content' }] }] },
//       status: 'draft'
//     };

//     const response = await api.post('/knowledge-base/admin/articles/', testData);
//     console.log('Test successful:', response.data);
//     return response.data;
//   } catch (error) {
//     console.error('Test failed:', error.response?.data || error.message);
//     throw error;
//   }
// };

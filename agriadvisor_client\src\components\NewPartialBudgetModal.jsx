// src/components/NewPartialBudgetModal.jsx

import React, { useState, useEffect } from "react";
import { Modal, Button, Form, Spinner } from "react-bootstrap";
import { getFarmersForExpert } from "../services/farmerService";

const NewPartialBudgetModal = ({ show, onHide, onSave }) => {
  const [farmers, setFarmers] = useState([]);
  const [loading, setLoading] = useState(true);

  const [name, setName] = useState("");
  const [selectedFarmer, setSelectedFarmer] = useState("");
  const [proposedChange, setProposedChange] = useState("");

  useEffect(() => {
    if (show) {
      const fetchFarmers = async () => {
        try {
          setLoading(true);
          const r = await getFarmersForExpert();
          setFarmers(r.data);
        } catch (e) {
          console.error("Failed to fetch farmers", e);
        } finally {
          setLoading(false);
        }
      };
      fetchFarmers();
    }
  }, [show]);

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave({ name, farmer: selectedFarmer, proposed_change_desc: proposedChange });
  };

  return (
    <Modal show={show} onHide={onHide}>
      <Form onSubmit={handleSubmit}>
        <Modal.Header closeButton>
          <Modal.Title>Create New Partial Budget</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {loading ? (
            <Spinner animation="border" />
          ) : (
            <>
              <Form.Group className="mb-3">
                <Form.Label>Analysis Name</Form.Label>
                <Form.Control
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="e.g., Feed Lambs to Heavier Weights"
                  required
                />
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label>Assign to Farmer</Form.Label>
                <Form.Select value={selectedFarmer} onChange={(e) => setSelectedFarmer(e.target.value)} required>
                  <option value="">Select a farmer...</option>
                  {farmers.map((f) => (
                    <option key={f.id} value={f.id}>
                      {f.first_name} {f.last_name}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label>Proposed Change Description</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  value={proposedChange}
                  onChange={(e) => setProposedChange(e.target.value)}
                />
              </Form.Group>
            </>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={onHide}>
            Cancel
          </Button>
          <Button variant="primary" type="submit" disabled={loading}>
            Create Analysis
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
};
export default NewPartialBudgetModal;





// // src/components/NewPartialBudgetModal.jsx

// import React, { useState, useEffect } from "react";
// import { Modal, Button, Form, Spinner } from "react-bootstrap";
// import { getFarmersForExpert } from "../services/farmerService"; // Reuse the service for getting farmers

// const NewPartialBudgetModal = ({ show, onHide, onSave }) => {
//   const [farmers, setFarmers] = useState([]);
//   const [loading, setLoading] = useState(true);

//   // Form state
//   const [name, setName] = useState("");
//   const [selectedFarmer, setSelectedFarmer] = useState("");
//   const [currentSituation, setCurrentSituation] = useState("");
//   const [proposedChange, setProposedChange] = useState("");

//   useEffect(() => {
//     // Fetch the list of farmers when the modal is about to be shown
//     if (show) {
//       const fetchFarmers = async () => {
//         try {
//           setLoading(true);
//           const response = await getFarmersForExpert();
//           setFarmers(response.data);
//         } catch (error) {
//           console.error("Failed to fetch farmers for modal", error);
//         } finally {
//           setLoading(false);
//         }
//       };
//       fetchFarmers();
//     }
//   }, [show]);

//   const handleSubmit = (e) => {
//     e.preventDefault();
//     onSave({
//       name,
//       farmer: selectedFarmer,
//       current_situation_desc: currentSituation,
//       proposed_change_desc: proposedChange,
//     });
//   };

//   return (
//     <Modal show={show} onHide={onHide}>
//       <Form onSubmit={handleSubmit}>
//         <Modal.Header closeButton>
//           <Modal.Title>Create New Partial Budget Analysis</Modal.Title>
//         </Modal.Header>
//         <Modal.Body>
//           {loading ? (
//             <div className="text-center">
//               <Spinner animation="border" />
//             </div>
//           ) : (
//             <>
//               <Form.Group className="mb-3">
//                 <Form.Label>Analysis Name</Form.Label>
//                 <Form.Control
//                   type="text"
//                   value={name}
//                   onChange={(e) => setName(e.target.value)}
//                   placeholder="e.g., Irrigation System Upgrade"
//                   required
//                 />
//               </Form.Group>
//               <Form.Group className="mb-3">
//                 <Form.Label>Assign to Farmer</Form.Label>
//                 <Form.Select value={selectedFarmer} onChange={(e) => setSelectedFarmer(e.target.value)} required>
//                   <option value="">Select a farmer...</option>
//                   {farmers.map((farmer) => (
//                     <option key={farmer.id} value={farmer.id}>
//                       {farmer.first_name} {farmer.last_name} ({farmer.username})
//                     </option>
//                   ))}
//                 </Form.Select>
//               </Form.Group>
//               <Form.Group className="mb-3">
//                 <Form.Label>Current Situation (Optional)</Form.Label>
//                 <Form.Control
//                   type="text"
//                   value={currentSituation}
//                   onChange={(e) => setCurrentSituation(e.target.value)}
//                   placeholder="e.g., Using rain-fed agriculture"
//                 />
//               </Form.Group>
//               <Form.Group className="mb-3">
//                 <Form.Label>Proposed Change (Optional)</Form.Label>
//                 <Form.Control
//                   type="text"
//                   value={proposedChange}
//                   onChange={(e) => setProposedChange(e.target.value)}
//                   placeholder="e.g., Install drip irrigation"
//                 />
//               </Form.Group>
//             </>
//           )}
//         </Modal.Body>
//         <Modal.Footer>
//           <Button variant="secondary" onClick={onHide}>
//             Cancel
//           </Button>
//           <Button variant="primary" type="submit" disabled={loading}>
//             Create Analysis
//           </Button>
//         </Modal.Footer>
//       </Form>
//     </Modal>
//   );
// };
// export default NewPartialBudgetModal;

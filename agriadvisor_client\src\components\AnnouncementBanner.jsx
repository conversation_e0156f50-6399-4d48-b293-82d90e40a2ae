// src/components/AnnouncementBanner.jsx
import React, { useState, useEffect } from "react";
import { Alert, Container } from "react-bootstrap";
import api from "../services/api";

const AnnouncementBanner = () => {
  const [announcement, setAnnouncement] = useState(null);
  const [show, setShow] = useState(false);

  useEffect(() => {
    const fetchAnnouncement = async () => {
      try {
        const response = await api.get("/announcements/current/");
        const currentAnnouncement = response.data;

        if (currentAnnouncement && currentAnnouncement.id) {
          const dismissedId = localStorage.getItem("dismissed_announcement_id");
          if (String(currentAnnouncement.id) !== dismissedId) {
            setAnnouncement(currentAnnouncement);
            setShow(true);
          }
        }
      } catch (error) {
        console.error("Could not fetch announcement", error);
      }
    };
    fetchAnnouncement();
  }, []);

  const handleDismiss = () => {
    if (announcement) {
      localStorage.setItem("dismissed_announcement_id", announcement.id);
      setShow(false);
    }
  };

  if (!show || !announcement) {
    return null;
  }

  return (
    <Container fluid>
      <Alert variant="info" onClose={handleDismiss} dismissible className="mt-3">
        <Alert.Heading>{announcement.title}</Alert.Heading>
        <p>{announcement.message}</p>
      </Alert>
    </Container>
  );
};
export default AnnouncementBanner;



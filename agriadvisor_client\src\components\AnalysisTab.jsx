// src/pages/EnterprisePlanningPage.jsx

import React, { useState, useEffect, useMemo } from "react";
import { Container, Row, Col, Card, ListGroup, Button, Table, Spinner, Alert, Tab, Tabs } from "react-bootstrap";
import {
  getEnterprisePlans,
  getEnterprisePlanDetail,
  createPlanFromTemplate,
  updateEnterprisePlan,
  createPlanItem,
  updatePlanItem,
  deletePlanItem,
} from "../services/planningService";
import NewPlanModal from "../components/NewPlanModal";
import PlanItemModal from "../components/PlanItemModal";
import AssessmentForm from "../components/AssessmentForm";
import AnalysisTab from "../components/AnalysisTab"; // Assuming AnalysisTab is now a separate component

// --- Helper Functions ---
const formatCurrency = (number) => {
  const num = parseFloat(number || 0);
  return new Intl.NumberFormat("en-GB", { style: "currency", currency: "USD" }).format(num).replace(/,/g, " ");
};

// --- Main Page Component ---
const EnterprisePlanningPage = () => {
  const [plans, setPlans] = useState([]);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [loading, setLoading] = useState(true);
  const [loadingWorksheet, setLoadingWorksheet] = useState(false);
  const [showNewPlanModal, setShowNewPlanModal] = useState(false);

  const fetchPlans = async () => {
    /* ... (this function is correct) ... */
  };
  useEffect(() => {
    fetchPlans();
  }, []);

  const handleSelectPlan = async (id) => {
    setLoadingWorksheet(true);
    try {
      const response = await getEnterprisePlanDetail(id);
      setSelectedPlan(response.data);
    } catch (error) {
      console.error("Failed to fetch plan details", error);
    } finally {
      setLoadingWorksheet(false);
    }
  };

  const handleSaveNewPlan = async (planData) => {
    /* ... (this function is correct) ... */
  };

  const refreshSelectedPlan = () => {
    if (selectedPlan) handleSelectPlan(selectedPlan.id);
  };

  if (loading)
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" />
      </Container>
    );

  return (
    <Container fluid>
      <Row>
        <Col md={4} className="mb-3">
          <Card>
            <Card.Header as="h4" className="d-flex justify-content-between align-items-center">
              Enterprise Plans
              <Button size="sm" onClick={() => setShowNewPlanModal(true)}>
                + New Plan
              </Button>
            </Card.Header>
            <ListGroup variant="flush">
              {plans.map((plan) => (
                <ListGroup.Item
                  key={plan.id}
                  action
                  active={selectedPlan?.id === plan.id}
                  onClick={() => handleSelectPlan(plan.id)}
                >
                  {plan.name} <small className="text-muted">({plan.farmer_name})</small>
                </ListGroup.Item>
              ))}
            </ListGroup>
          </Card>
        </Col>
        <Col md={8}>
          {loadingWorksheet ? (
            <div className="text-center mt-5">
              <Spinner animation="border" />
            </div>
          ) : selectedPlan ? (
            <PlanWorksheet plan={selectedPlan} onRefresh={refreshSelectedPlan} />
          ) : (
            <Card>
              <Card.Body className="text-center text-muted">
                <p>Select a plan from the list.</p>
              </Card.Body>
            </Card>
          )}
        </Col>
      </Row>
      <NewPlanModal show={showNewPlanModal} onHide={() => setShowNewPlanModal(false)} onSave={handleSaveNewPlan} />
    </Container>
  );
};

// --- PlanWorksheet Component ---
// This is the main container for the tabs.
const PlanWorksheet = ({ plan, onRefresh }) => {
  const [isSaving, setIsSaving] = useState(false);
  const [showItemModal, setShowItemModal] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [itemType, setItemType] = useState("income");

  const handleSaveAssessment = async (assessmentData) => {
    /* ... (this is correct) ... */
  };

  const handleShowAddItemModal = (type) => {
    setItemType(type);
    setEditingItem(null);
    setShowItemModal(true);
  };
  const handleShowEditItemModal = (item) => {
    setItemType(item.item_type);
    setEditingItem(item);
    setShowItemModal(true);
  };
  const handleCloseItemModal = () => setShowItemModal(false);

  const handleSaveItem = async (itemData) => {
    /* ... (this is correct) ... */
  };
  const handleDeleteItem = async (id) => {
    /* ... (this is correct) ... */
  };

  // --- THIS IS THE FINANCIAL WORKSHEET LOGIC ---
  const incomeItems = plan.plan_items.filter((i) => i.item_type === "income");
  const variableCostItems = plan.plan_items.filter((i) => i.item_type === "variable");
  const fixedCostItems = plan.plan_items.filter((i) => i.item_type === "fixed");

  return (
    <div>
      <Tabs defaultActiveKey="assessment" id="plan-worksheet-tabs" className="mb-3" justify>
        <Tab eventKey="assessment" title="1. Assessment">
          <AssessmentForm plan={plan} onSave={handleSaveAssessment} isSaving={isSaving} />
        </Tab>
        <Tab eventKey="assumptions" title="2. Assumptions">
          {/* We will build the specialized assumption tabs here later */}
          <Card>
            <Card.Body>The specialized assumptions form will go here.</Card.Body>
          </Card>
        </Tab>
        <Tab eventKey="financials" title="3. Financial Worksheet">
          {/* --- THE FINANCIAL WORKSHEET IS NOW HERE --- */}
          <PlanSection
            title="Sources of Income"
            items={incomeItems}
            onAdd={() => handleShowAddItemModal("income")}
            onEdit={handleShowEditItemModal}
            onDelete={handleDeleteItem}
          />
          <PlanSection
            title="Variable Costs"
            items={variableCostItems}
            onAdd={() => handleShowAddItemModal("variable")}
            onEdit={handleShowEditItemModal}
            onDelete={handleDeleteItem}
          />
          <PlanSection
            title="Fixed Costs"
            items={fixedCostItems}
            onAdd={() => handleShowAddItemModal("fixed")}
            onEdit={handleShowEditItemModal}
            onDelete={handleDeleteItem}
          />
        </Tab>
        <Tab eventKey="analysis" title="4. P&L and KPIs">
          <AnalysisTab planId={plan.id} />
        </Tab>
      </Tabs>
      <PlanItemModal
        show={showItemModal}
        onHide={handleCloseItemModal}
        onSave={handleSaveItem}
        item={editingItem}
        itemType={itemType}
        planId={plan.id}
      />
    </div>
  );
};

// --- PlanSection Component (For the Financial Worksheet) ---
const PlanSection = ({ title, items, onAdd, onEdit, onDelete }) => (
  <Card className="mb-3">
    <Card.Header className="d-flex justify-content-between align-items-center">
      <h5>{title}</h5>
      <Button variant="outline-primary" size="sm" onClick={onAdd}>
        + Add Item
      </Button>
    </Card.Header>
    <Card.Body>
      <Table striped bordered size="sm" responsive>
        <thead>
          <tr>
            <th>Item</th>
            <th>Category</th>
            <th>Qty</th>
            <th>Unit</th>
            <th>Price/Unit</th>
            <th className="text-end">Projected Total</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {items.map((item) => (
            <tr key={item.id}>
              <td>{item.item_name}</td>
              <td>{item.category}</td>
              <td>{item.quantity}</td>
              <td>{item.unit}</td>
              <td>{formatCurrency(item.price_per_unit)}</td>
              <td className="text-end">{formatCurrency(item.projected_amount)}</td>
              <td>
                <Button variant="outline-secondary" size="sm" className="me-1 py-0 px-1" onClick={() => onEdit(item)}>
                  <i className="bi bi-pencil-fill"></i>
                </Button>
                <Button variant="outline-danger" size="sm" className="py-0 px-1" onClick={() => onDelete(item.id)}>
                  <i className="bi bi-trash-fill"></i>
                </Button>
              </td>
            </tr>
          ))}
          {items.length === 0 && (
            <tr>
              <td colSpan="7" className="text-center">
                No items.
              </td>
            </tr>
          )}
        </tbody>
      </Table>
    </Card.Body>
  </Card>
);

export default EnterprisePlanningPage;



// src/layouts/AdminLayout.jsx

import React from "react";
import { Outlet, useLocation } from "react-router-dom";
import Sidebar from "../components/Sidebar";
import Header from "../components/Header";

const layoutStyle = {
  display: "flex",
  height: "100vh",
  backgroundColor: "#f8f9fa",
};

const mainContentWrapperStyle = {
  flexGrow: 1,
  overflowY: "auto",
  display: "flex",
  flexDirection: "column",
};

const contentAreaStyle = {
  padding: "0 20px 20px 20px",
  flexGrow: 1,
};

const getPageTitle = (pathname) => {
  const segments = pathname.split("/").filter(Boolean); // filter(Boolean) removes empty strings
  const name = segments.pop(); // Get the last part
  if (!name) return "Dashboard";
  return name.charAt(0).toUpperCase() + name.slice(1).replace("-", " ");
};

const AdminLayout = () => {
  const location = useLocation();
  const pageTitle = getPageTitle(location.pathname);

  return (
    <div style={layoutStyle}>
      <Sidebar />
      <div style={mainContentWrapperStyle}>
        <Header pageTitle={pageTitle} />
        <main style={contentAreaStyle}>
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;

# api/serializers.py

from rest_framework import serializers
from .models import (
    GlobalIngredient, FarmerIngredient, NutrientRequirement, 
    Formulation, FormulationIngredient
)

# A reusable Mixin to get all nutrient fields from the abstract model
class NutrientProfileSerializerMixin(serializers.ModelSerializer):
    class Meta:
        # This will be used by other serializers
        fields = [
            'dry_matter_as_fed', 'crude_protein', 'crude_fiber', 'ndf', 'adf',
            'lignin', 'ether_extract', 'ash', 'insoluble_ash', 'starch_pol',
            'starch_enz', 'total_sugars', 'gross_energy', 'lysine', 'methionine',
            'cysteine', 'threonine', 'tryptophan', 'arginine', 'histidine',
            'isoleucine', 'leucine', 'valine', 'oleic_acid', 'linoleic_acid',
            'linolenic_acid', 'palmitic_acid', 'stearic_acid', 'ca', 'p', 'k',
            'na', 'cl', 'mg', 's', 'mn', 'zn', 'cu', 'fe', 'se'
        ]

class GlobalIngredientSerializer(NutrientProfileSerializerMixin):
    class Meta(NutrientProfileSerializerMixin.Meta):
        model = GlobalIngredient
        fields = ['id', 'name', 'description'] + NutrientProfileSerializerMixin.Meta.fields

class FarmerIngredientSerializer(NutrientProfileSerializerMixin):
    class Meta(NutrientProfileSerializerMixin.Meta):
        model = FarmerIngredient
        fields = ['id', 'name', 'description', 'farmer_id'] + NutrientProfileSerializerMixin.Meta.fields
        read_only_fields = ['tenant_id'] # Tenant is managed internally

class FormulationIngredientSerializer(NutrientProfileSerializerMixin):
    class Meta(NutrientProfileSerializerMixin.Meta):
        model = FormulationIngredient
        fields = ['id', 'ingredient_name', 'ingredient_source_type', 'amount_kg'] + NutrientProfileSerializerMixin.Meta.fields

class FormulationSerializer(serializers.ModelSerializer):
    ingredients = FormulationIngredientSerializer(many=True, read_only=True)

    class Meta:
        model = Formulation
        fields = [
            'id', 'name', 'farmer_id', 'expert_id', 
            'based_on_requirement', 'created_at', 'ingredients'
        ]
        read_only_fields = ['tenant_id', 'expert_id']

class NutrientRequirementSerializer(serializers.ModelSerializer):
    class Meta:
        model = NutrientRequirement
        fields = '__all__'



# Generated by Django 5.2.5 on 2025-09-03 18:37

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('planning', '0005_alter_enterprisetemplate_model_type_partialbudget_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name='partialbudget',
            name='current_situation_desc',
        ),
        migrations.AddField(
            model_name='partialbudget',
            name='notes',
            field=models.TextField(blank=True, help_text='General notes or conclusions for the report'),
        ),
        migrations.AddField(
            model_name='partialbudgetitem',
            name='number',
            field=models.DecimalField(decimal_places=2, default=1.0, max_digits=12),
        ),
        migrations.AddField(
            model_name='partialbudgetitem',
            name='price',
            field=models.DecimalField(decimal_places=2, default=0.0, max_digits=12),
        ),
        migrations.AlterField(
            model_name='partialbudget',
            name='expert',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='managed_partial_budgets', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='partialbudget',
            name='farmer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='partial_budgets', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='partialbudget',
            name='name',
            field=models.CharField(help_text='e.g., Feed lambs to heavier weights analysis', max_length=255),
        ),
        migrations.AlterField(
            model_name='partialbudget',
            name='proposed_change_desc',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='partialbudgetitem',
            name='amount',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='e.g., Weight, Quantity per unit', max_digits=12),
        ),
    ]

// src/pages/NewBookingPage.jsx
import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { Container, <PERSON>, <PERSON><PERSON>, Card, Spinner, <PERSON><PERSON>, <PERSON>, Col } from "react-bootstrap";
import { getFarmerExperts } from "../services/expertService";
import { checkAvailability } from "../services/availabilityService";
import { createPaymentIntent } from "../services/paymentService";
import PaymentModal from "../components/PaymentModal";

const NewBookingPage = () => {
  const { state } = useLocation();
  const navigate = useNavigate();
  const { service } = state || {};

  const [experts, setExperts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  // Form state
  const [selectedExpert, setSelectedExpert] = useState("");
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [availableSlots, setAvailableSlots] = useState([]);
  const [loadingSlots, setLoadingSlots] = useState(false);
  const [selectedSlot, setSelectedSlot] = useState("");

  // Payment flow state
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [clientSecret, setClientSecret] = useState(null);
  const [bookingDetails, setBookingDetails] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    const fetchExperts = async () => {
      try {
        console.log("NewBookingPage: Fetching farmer experts...");
        const response = await getFarmerExperts();
        console.log("NewBookingPage: Experts response", response);
        const data = response.data || response;
        console.log("NewBookingPage: Experts data", data);
        setExperts(Array.isArray(data) ? data : []);
      } catch (err) {
        console.error("NewBookingPage: Error fetching experts", err);
        setError("Could not load available experts.");
      } finally {
        setLoading(false);
      }
    };
    fetchExperts();
  }, []);

  useEffect(() => {
    if (selectedExpert && service) {
      const fetchSlots = async () => {
        setLoadingSlots(true);
        setSelectedSlot("");
        try {
          const params = {
            expert_id: selectedExpert,
            service_id: service.id,
            year: currentMonth.getFullYear(),
            month: currentMonth.getMonth() + 1,
          };
          const response = await checkAvailability(params);
          setAvailableSlots(response.data);
        } catch (err) {
          setAvailableSlots([]);
        } finally {
          setLoadingSlots(false);
        }
      };
      fetchSlots();
    }
  }, [selectedExpert, currentMonth, service]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log("NewBookingPage: Form submitted");
    console.log("NewBookingPage: Selected expert:", selectedExpert);
    console.log("NewBookingPage: Selected slot:", selectedSlot);
    console.log("NewBookingPage: Service:", service);

    setIsProcessing(true);
    setError("");

    const details = {
      serviceName: service.name,
      date: new Date(selectedSlot).toLocaleDateString(),
      time: new Date(selectedSlot).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
      amount: parseFloat(service.price),
    };
    console.log("NewBookingPage: Booking details:", details);
    setBookingDetails(details);

    try {
      const paymentData = {
        service_id: service.id,
        expert_id: selectedExpert,
        booking_time: new Date(selectedSlot).toISOString(),
      };
      console.log("NewBookingPage: Creating payment intent with data:", paymentData);

      const response = await createPaymentIntent(paymentData);
      console.log("NewBookingPage: Payment intent response:", response);

      const intentData = response.data || response;
      console.log("NewBookingPage: Intent data:", intentData);

      setClientSecret(intentData.clientSecret);
      setShowPaymentModal(true);
    } catch (err) {
      console.error("NewBookingPage: Payment intent error:", err);
      console.error("NewBookingPage: Error response:", err.response?.data);
      setError("Could not initiate payment. The expert may not have payments enabled.");
    } finally {
      setIsProcessing(false);
    }
  };

  // --- THIS IS THE NEW, SEAMLESS REDIRECT ---
  const handlePaymentSuccess = () => {
    setShowPaymentModal(false);
    alert("Payment successful! Please complete the required pre-consultation form.");

    // Use the bookingId we saved in state to redirect directly to the form
    navigate(`/booking/${bookingDetails.id}/pre-consultation-form`);
  };

  if (!service)
    return (
      <Container className="mt-4">
        <Alert variant="warning">
          No service selected. Please <a href="/browse-services">go back</a>.
        </Alert>
      </Container>
    );

  const groupSlotsByDay = () =>
    availableSlots.reduce((acc, slot) => {
      const day = new Date(slot).toDateString();
      if (!acc[day]) acc[day] = [];
      acc[day].push(slot);
      return acc;
    }, {});

  const groupedSlots = groupSlotsByDay();

  return (
    <Container className="mt-4">
      <Card>
        <Card.Header as="h4">Book: {service.name}</Card.Header>
        <Card.Body>
          <p>{service.description}</p>
          <hr />
          {loading ? (
            <div className="text-center">
              <Spinner animation="border" />
            </div>
          ) : (
            <Form onSubmit={handleSubmit}>
              <Form.Group className="mb-3">
                <Form.Label>
                  <strong>1. Select an Expert</strong>
                </Form.Label>
                <Form.Select value={selectedExpert} onChange={(e) => setSelectedExpert(e.target.value)} required>
                  <option value="">Choose an expert...</option>
                  {experts.map((expert) => (
                    <option key={expert.id} value={expert.id}>
                      {expert.first_name} {expert.last_name} ({expert.profile?.specialty}) - ★ {expert.average_rating} (
                      {expert.review_count} reviews)
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>
              {selectedExpert && (
                <Form.Group className="mb-3">
                  <Form.Label>
                    <strong>2. Select an Available Slot</strong>
                  </Form.Label>
                  {loadingSlots ? (
                    <Spinner animation="border" size="sm" />
                  ) : Object.keys(groupedSlots).length > 0 ? (
                    Object.entries(groupedSlots).map(([day, slots]) => (
                      <div key={day} className="mb-3">
                        <strong>{day}</strong>
                        <div className="d-flex flex-wrap mt-2">
                          {slots.map((slot) => (
                            <Button
                              key={slot}
                              variant={selectedSlot === slot ? "success" : "outline-primary"}
                              className="m-1"
                              onClick={() => setSelectedSlot(slot)}
                            >
                              {new Date(slot).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                            </Button>
                          ))}
                        </div>
                      </div>
                    ))
                  ) : (
                    <Alert variant="secondary" className="mt-2">
                      No available slots for this expert. Try changing the month or selecting another expert.
                    </Alert>
                  )}
                </Form.Group>
              )}
              {error && (
                <Alert variant="danger" className="mt-3">
                  {error}
                </Alert>
              )}
              <div className="d-grid mt-4">
                <Button variant="primary" type="submit" disabled={!selectedSlot || isProcessing}>
                  {isProcessing ? "Initiating..." : `Proceed to Payment ($${parseFloat(service.price).toFixed(2)})`}
                </Button>
              </div>
            </Form>
          )}
        </Card.Body>
      </Card>
      {clientSecret && bookingDetails && (
        <PaymentModal
          show={showPaymentModal}
          onHide={() => setShowPaymentModal(false)}
          clientSecret={clientSecret}
          bookingDetails={bookingDetails}
          onPaymentSuccess={handlePaymentSuccess}
        />
      )}
    </Container>
  );
};
export default NewBookingPage;



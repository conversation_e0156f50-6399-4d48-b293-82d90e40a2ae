// src/services/announcementService.js

import api from "./api";

export const getAnnouncements = () => api.get("/announcements/admin/");
export const createAnnouncement = (data) => api.post("/announcements/admin/", data);
export const updateAnnouncement = (id, data) => api.put(`/announcements/admin/${id}/`, data);
export const deleteAnnouncement = (id) => api.delete(`/announcements/admin/${id}/`);



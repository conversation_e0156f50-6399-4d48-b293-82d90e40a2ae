{"version": 3, "sources": ["../src/index.ts"], "sourcesContent": ["import { TableRow } from '@tiptap/extension-table'\n\nexport type { TableRowOptions } from '@tiptap/extension-table'\nexport { TableRow } from '@tiptap/extension-table'\n\nexport default TableRow\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,6BAAyB;AAGzB,IAAAA,0BAAyB;AAEzB,IAAO,gBAAQ;", "names": ["import_extension_table"]}
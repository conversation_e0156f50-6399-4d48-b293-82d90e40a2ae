// src/components/CheckoutForm.jsx
import React, { useState } from "react";
import { useStripe, useElements, CardElement } from "@stripe/react-stripe-js";
import { Button, Alert } from "react-bootstrap";

const CheckoutForm = ({ clientSecret, onPaymentSuccess, amount }) => {
  const stripe = useStripe();
  const elements = useElements();
  const [error, setError] = useState(null);
  const [processing, setProcessing] = useState(false);

  const handleSubmit = async (event) => {
    event.preventDefault();
    setProcessing(true);

    if (!stripe || !elements) {
      setProcessing(false);
      return;
    }

    const { error, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
      payment_method: {
        card: elements.getElement(CardElement),
      },
    });

    if (error) {
      setError(error.message);
      setProcessing(false);
    } else if (paymentIntent.status === "succeeded") {
      onPaymentSuccess();
    }
  };

  const cardElementOptions = {
    style: {
      base: { fontSize: "16px", color: "#424770", "::placeholder": { color: "#aab7c4" } },
      invalid: { color: "#9e2146" },
    },
  };

  return (
    <form onSubmit={handleSubmit}>
      {error && (
        <Alert variant="danger" className="mb-3">
          {error}
        </Alert>
      )}
      <CardElement options={cardElementOptions} className="mb-4 p-2 border rounded" />
      <div className="d-grid">
        <Button type="submit" variant="success" disabled={!stripe || processing}>
          {processing ? "Processing..." : `Pay $${amount.toFixed(2)}`}
        </Button>
      </div>
    </form>
  );
};
export default CheckoutForm;

# announcements/models.py

from django.db import models
from accounts.models import Tenant

class Announcement(models.Model):
    ROLE_CHOICES = (('farmer', 'Farmers Only'), ('expert', 'Experts Only'), ('all', 'All Users'))
    title = models.CharField(max_length=255)
    message = models.TextField()
    target_role = models.CharField(max_length=10, choices=ROLE_CHOICES)
    is_active = models.BooleanField(default=False, help_text="Only one announcement per role can be active at a time.")
    tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE, related_name="announcements")
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Announcement for {self.get_target_role_display()} in {self.tenant.name}"

    def save(self, *args, **kwargs):
        if self.is_active:
            Announcement.objects.filter(tenant=self.tenant, target_role=self.target_role, is_active=True).update(is_active=False)
        super().save(*args, **kwargs)





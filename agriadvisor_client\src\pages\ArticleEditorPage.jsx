// src/pages/ArticleEditorPage.jsx
import React, { useState, useEffect, useCallback } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Container, Form, <PERSON><PERSON>, Card, Spinner, <PERSON><PERSON>, <PERSON>, Col, Badge, InputGroup, Modal } from "react-bootstrap";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import {
  getAdminCategories,
  getArticle,
  createArticle,
  updateArticle,
  generateSlug,
} from "../services/knowledgeBaseService";

// Enhanced toolbar for the editor
const EditorToolbar = ({ editor }) => {
  if (!editor) return null;

  const setLink = useCallback(() => {
    const previousUrl = editor.getAttributes("link").href;
    const url = window.prompt("URL", previousUrl);

    if (url === null) return;
    if (url === "") {
      editor.chain().focus().extendMarkRange("link").unsetLink().run();
      return;
    }

    editor.chain().focus().extendMarkRange("link").setLink({ href: url }).run();
  }, [editor]);

  const ToolbarButton = ({ onClick, active, title, icon, variant = "outline-secondary", disabled = false }) => (
    <Button
      onClick={onClick}
      variant={active ? "primary" : variant}
      size="sm"
      title={title}
      className="me-1 mb-1"
      disabled={disabled}
    >
      <i className={`fas fa-${icon}`}></i>
    </Button>
  );

  return (
    <Card className="mb-3">
      <Card.Body className="p-2 d-flex flex-wrap">
        <ToolbarButton
          onClick={() => editor.chain().focus().toggleBold().run()}
          active={editor.isActive("bold")}
          title="Bold"
          icon="bold"
        />
        <ToolbarButton
          onClick={() => editor.chain().focus().toggleItalic().run()}
          active={editor.isActive("italic")}
          title="Italic"
          icon="italic"
        />
        <ToolbarButton
          onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
          active={editor.isActive("heading", { level: 2 })}
          title="Heading 2"
          icon="heading"
        />
        <ToolbarButton
          onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
          active={editor.isActive("heading", { level: 3 })}
          title="Heading 3"
          icon="heading"
        />
        <ToolbarButton
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          active={editor.isActive("bulletList")}
          title="Bullet List"
          icon="list-ul"
        />
        <ToolbarButton
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
          active={editor.isActive("orderedList")}
          title="Numbered List"
          icon="list-ol"
        />
        <ToolbarButton onClick={setLink} active={editor.isActive("link")} title="Add Link" icon="link" />
        <ToolbarButton
          onClick={() => editor.chain().focus().unsetLink().run()}
          title="Remove Link"
          icon="unlink"
          disabled={!editor.isActive("link")}
        />
        <ToolbarButton onClick={() => editor.chain().focus().undo().run()} title="Undo" icon="undo" />
        <ToolbarButton onClick={() => editor.chain().focus().redo().run()} title="Redo" icon="redo" />
      </Card.Body>
    </Card>
  );
};



const ArticleEditorPage = () => {
  const { articleId } = useParams();
  const navigate = useNavigate();
  const isNew = articleId === "new";

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [categories, setCategories] = useState([]);
  const [tags, setTags] = useState("");
  const [slug, setSlug] = useState("");
  const [generateSlugAuto, setGenerateSlugAuto] = useState(true);
  const [showPreview, setShowPreview] = useState(false);

  const [formData, setFormData] = useState({
    title: "",
    category: "",
    status: "draft",
    is_featured: false,
    meta_description: "",
  });

  const editor = useEditor({
    extensions: [StarterKit],
    content: "",
    editorProps: {
      attributes: {
        class: "prose prose-lg max-w-none focus:outline-none",
      },
    },
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError("");
        const categoriesData = await getAdminCategories();

        // The service function returns the categories array directly
        if (Array.isArray(categoriesData)) {
          setCategories(categoriesData);
        } else {
          setCategories([]);
          console.error("Invalid categories response:", categoriesData);
          setError("Failed to load categories. Invalid response from server.");
        }

        if (!isNew) {
          try {
            const articleRes = await getArticle(articleId);
            const article = articleRes.data;

            setFormData({
              title: article.title,
              category: article.category,
              status: article.status,
              is_featured: article.is_featured,
              meta_description: article.meta_description || "",
            });
            setTags(article.tags?.join(", ") || "");
            setSlug(article.slug || "");
            setGenerateSlugAuto(false);

            if (editor && article.content) {
              editor.commands.setContent(article.content);
            }
          } catch (articleError) {
            console.error("Error loading article:", articleError);
            setError("Failed to load article data. Please try again.");
          }
        }
      } catch (err) {
        console.error("Load error:", err.response?.data || err.message);
        setError("Failed to load categories. Please try again.");
        setCategories([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [articleId, editor, isNew]);

  const showAlert = (message, type = "error") => {
    if (type === "success") {
      setSuccess(message);
      setError("");
      setTimeout(() => setSuccess(""), 3000);
    } else {
      setError(message);
      setSuccess("");
    }
  };

  const handleGenerateSlug = async () => {
    if (!formData.title) {
      showAlert("Please enter a title first", "error");
      return;
    }

    try {
      const response = await generateSlug(formData.title);
      setSlug(response.data.slug);
      showAlert("Slug generated successfully", "success");
    } catch (err) {
      showAlert("Failed to generate slug", "error");
    }
  };



  const handleSave = async (publish = false) => {
    if (!formData.title.trim()) {
      showAlert("Title is required", "error");
      return;
    }

    if (!formData.category) {
      showAlert("Category is required", "error");
      return;
    }

    if (!editor || !editor.getText().trim()) {
      showAlert("Content is required", "error");
      return;
    }

    setSaving(true);
    setError("");
    setSuccess("");

    try {
      const content = editor.getJSON();

      const articleData = {
        title: formData.title.trim(),
        category: parseInt(formData.category),
        content: content,
        status: publish ? "published" : formData.status,
        is_featured: formData.is_featured,
        meta_description: formData.meta_description?.trim() || "",
        ...(tags.trim() && {
          tags: tags
            .split(",")
            .map((tag) => tag.trim())
            .filter((tag) => tag),
        }),
        ...(slug.trim() && !generateSlugAuto && { slug: slug.trim() }),
      };

      console.log("Sending article data:", articleData);

      let result;
      if (isNew) {
        result = await createArticle(articleData);
        showAlert("Article created successfully!", "success");
      } else {
        result = await updateArticle(articleId, articleData);
        showAlert("Article updated successfully!", "success");
      }

      console.log("API response:", result);

      setTimeout(() => {
        navigate("/admin/kb-articles");
      }, 1500);
    } catch (err) {
      console.error("Save error:", err);
      console.error("Error response:", err.response?.data);

      let errorMsg = `Failed to ${isNew ? "create" : "update"} article. Please try again.`;

      if (err.response?.data) {
        if (err.response.data.detail) {
          errorMsg = err.response.data.detail;
        } else if (err.response.data.message) {
          errorMsg = err.response.data.message;
        } else if (err.response.data.non_field_errors) {
          errorMsg = err.response.data.non_field_errors.join(", ");
        } else {
          const fieldErrors = Object.entries(err.response.data)
            .map(([field, errors]) => {
              const fieldName = field.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
              const errorList = Array.isArray(errors) ? errors.join(", ") : String(errors);
              return `${fieldName}: ${errorList}`;
            })
            .join("; ");

          if (fieldErrors) {
            errorMsg = fieldErrors;
          }
        }
      } else if (err.message) {
        errorMsg = err.message;
      }

      showAlert(errorMsg, "error");
    } finally {
      setSaving(false);
    }
  };

  const handleTitleChange = (value) => {
    setFormData((prev) => ({ ...prev, title: value }));
    if (generateSlugAuto && value) {
      const generatedSlug = value
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, "-")
        .replace(/(^-|-$)+/g, "");
      setSlug(generatedSlug);
    }
  };

  if (loading) {
    return (
      <Container fluid className="d-flex justify-content-center align-items-center" style={{ minHeight: "400px" }}>
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
      </Container>
    );
  }

  return (
    <Container fluid className="py-4">
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h3 className="mb-1">{isNew ? "Create New Article" : "Edit Article"}</h3>
              <p className="text-muted mb-0">
                {isNew ? "Create a new knowledge base article" : "Edit existing article"}
              </p>
            </div>
            <div className="d-flex align-items-center gap-2">
              <Button
                variant="outline-secondary"
                className="me-2"
                onClick={() => setShowPreview(true)}
                disabled={saving}
              >
                <i className="fas fa-eye me-2"></i>Preview
              </Button>
              <Button
                variant="outline-primary"
                className="me-2"
                onClick={() => handleSave(false)}
                disabled={saving || categories.length === 0}
              >
                {saving ? <Spinner animation="border" size="sm" /> : <i className="fas fa-save me-2"></i>}
                Save Draft
              </Button>
              <Button variant="primary" onClick={() => handleSave(true)} disabled={saving || categories.length === 0}>
                {saving ? <Spinner animation="border" size="sm" /> : <i className="fas fa-paper-plane me-2"></i>}
                Publish
              </Button>
            </div>
          </div>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" className="mb-3" onClose={() => setError("")} dismissible>
          <i className="fas fa-exclamation-circle me-2"></i>
          {error}

        </Alert>
      )}

      {success && (
        <Alert variant="success" className="mb-3" onClose={() => setSuccess("")} dismissible>
          <i className="fas fa-check-circle me-2"></i>
          {success}
        </Alert>
      )}



      <Row>
        <Col lg={8}>
          <Card className="mb-4">
            <Card.Body>
              <Form.Group className="mb-3">
                <Form.Label>Title *</Form.Label>
                <Form.Control
                  type="text"
                  value={formData.title}
                  onChange={(e) => handleTitleChange(e.target.value)}
                  placeholder="Enter article title"
                  className="fs-4"
                />
              </Form.Group>

              <Form.Label>Content *</Form.Label>
              <EditorToolbar editor={editor} />

              {editor && (
                <div className="border rounded p-3" style={{ minHeight: "400px" }}>
                  <EditorContent editor={editor} />
                </div>
              )}

              {editor && (
                <div className="text-muted small mt-2">
                  Characters: {editor.getText().length} • Words:{" "}
                  {
                    editor
                      .getText()
                      .split(/\s+/)
                      .filter((word) => word.length > 0).length
                  }
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>

        <Col lg={4}>
          <Card className="mb-4">
            <Card.Header>
              <h6 className="mb-0">Article Settings</h6>
            </Card.Header>
            <Card.Body>
              <Form.Group className="mb-3">
                <Form.Label>Category *</Form.Label>
                <Form.Select
                  value={formData.category}
                  onChange={(e) => setFormData((prev) => ({ ...prev, category: e.target.value }))}
                  disabled={categories.length === 0}
                >
                  <option value="">Select a category...</option>
                  {categories.length > 0 ? (
                    categories.map((c) => (
                      <option key={c.id} value={c.id}>
                        {c.name}
                      </option>
                    ))
                  ) : (
                    <option value="" disabled>
                      No categories available
                    </option>
                  )}
                </Form.Select>
                {categories.length === 0 && (
                  <Form.Text className="text-muted">
                    <i className="fas fa-info-circle me-1"></i>
                    Categories are required. Please create categories first in the{" "}
                    <a href="/admin/kb-categories" className="text-decoration-none">
                      Categories section
                    </a>
                    .
                  </Form.Text>
                )}
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Tags</Form.Label>
                <Form.Control
                  type="text"
                  value={tags}
                  onChange={(e) => setTags(e.target.value)}
                  placeholder="Comma-separated tags (e.g., farming, crops, tips)"
                />
                <Form.Text className="text-muted">Separate tags with commas</Form.Text>
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Slug</Form.Label>
                <InputGroup>
                  <Form.Control
                    type="text"
                    value={slug}
                    onChange={(e) => setSlug(e.target.value)}
                    placeholder="article-url-slug"
                  />
                  <Button variant="outline-secondary" onClick={handleGenerateSlug}>
                    <i className="fas fa-magic"></i>
                  </Button>
                </InputGroup>
                <Form.Text className="text-muted">URL-friendly version of the title</Form.Text>
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Status</Form.Label>
                <Form.Select
                  value={formData.status}
                  onChange={(e) => setFormData((prev) => ({ ...prev, status: e.target.value }))}
                >
                  <option value="draft">Draft</option>
                  <option value="published">Published</option>
                  <option value="archived">Archived</option>
                </Form.Select>
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Check
                  type="checkbox"
                  label="Featured Article"
                  checked={formData.is_featured}
                  onChange={(e) => setFormData((prev) => ({ ...prev, is_featured: e.target.checked }))}
                />
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Check
                  type="checkbox"
                  label="Auto-generate slug from title"
                  checked={generateSlugAuto}
                  onChange={(e) => setGenerateSlugAuto(e.target.checked)}
                />
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Meta Description</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  value={formData.meta_description}
                  onChange={(e) => setFormData((prev) => ({ ...prev, meta_description: e.target.value }))}
                  placeholder="Brief description for SEO (optional)"
                  maxLength={160}
                />
                <Form.Text className="text-muted">{formData.meta_description.length}/160 characters</Form.Text>
              </Form.Group>
            </Card.Body>
          </Card>

          <Card>
            <Card.Header>
              <h6 className="mb-0">Quick Actions</h6>
            </Card.Header>
            <Card.Body>
              <div className="d-grid gap-2">
                <Button
                  variant="outline-primary"
                  onClick={() => handleSave(false)}
                  disabled={saving || categories.length === 0}
                >
                  <i className="fas fa-save me-2"></i>Save Draft
                </Button>
                <Button variant="primary" onClick={() => handleSave(true)} disabled={saving || categories.length === 0}>
                  <i className="fas fa-paper-plane me-2"></i>Publish Now
                </Button>
                <Button variant="outline-secondary" onClick={() => navigate("/admin/kb-articles")} disabled={saving}>
                  <i className="fas fa-times me-2"></i>Cancel
                </Button>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Preview Modal */}
      <Modal show={showPreview} onHide={() => setShowPreview(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Article Preview</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {editor && (
            <div className="prose prose-lg max-w-none">
              <h1>{formData.title}</h1>
              <div dangerouslySetInnerHTML={{ __html: editor.getHTML() }} />
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowPreview(false)}>
            Close Preview
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default ArticleEditorPage;

// src/pages/PayoutsPage.jsx
import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Spinner, Alert } from "react-bootstrap";
import { getMyProfile } from "../services/authService";
import { createConnectAccount } from "../services/stripeService";

const PayoutsPage = () => {
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const data = await getMyProfile();
        setProfile(data);
      } catch (err) {
        setError("Failed to load profile data.");
      } finally {
        setLoading(false);
      }
    };
    fetchProfile();
  }, []);

  const handleConnectStripe = async () => {
    try {
      const response = await createConnectAccount();
      window.location.href = response.onboarding_url;
    } catch (err) {
      setError("Could not initiate Stripe connection. Please try again.");
    }
  };

  if (loading)
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" />
      </Container>
    );
  if (error)
    return (
      <Container className="mt-4">
        <Alert variant="danger">{error}</Alert>
      </Container>
    );

  return (
    <Container>
      <Card>
        <Card.Header as="h4">Payouts Management</Card.Header>
        <Card.Body>
          <Card.Title>Stripe Connection Status</Card.Title>
          {profile?.profile?.stripe_account_id ? (
            <div>
              <Alert variant="success">Your account is connected to Stripe!</Alert>
              <p>
                <strong>Stripe Account ID:</strong> {profile.profile.stripe_account_id}
              </p>
            </div>
          ) : (
            <div>
              <Alert variant="warning">You must connect to Stripe to receive payments.</Alert>
              <Button variant="primary" onClick={handleConnectStripe}>
                Connect with Stripe
              </Button>
            </div>
          )}
        </Card.Body>
      </Card>
    </Container>
  );
};
export default PayoutsPage;

# availability/utils.py

from datetime import datetime, timedelta
import calendar

def calculate_available_slots(expert, year, month, duration_minutes):
    rules = expert.availability_rules.all()
    exceptions = expert.availability_exceptions.filter(date__year=year, date__month=month)
    existing_bookings = expert.expert_bookings.filter(booking_time__year=year, booking_time__month=month, status='confirmed')
    rules_dict = {rule.day_of_week: [] for rule in rules}
    for rule in rules:
        rules_dict[rule.day_of_week].append((rule.start_time, rule.end_time))
    exceptions_dict = {exc.date: exc for exc in exceptions}
    booked_slots_set = {b.booking_time for b in existing_bookings}
    available_slots = []
    num_days = calendar.monthrange(year, month)[1]
    for day in range(1, num_days + 1):
        current_date = datetime(year, month, day).date()
        if current_date in exceptions_dict:
            exception = exceptions_dict[current_date]
            if not exception.is_available:
                continue
            else:
                day_start = datetime.combine(current_date, exception.start_time)
                day_end = datetime.combine(current_date, exception.end_time)
        else:
            weekday = current_date.weekday()
            if weekday not in rules_dict:
                continue
            try:
                start_t, end_t = rules_dict[weekday][0]
                day_start = datetime.combine(current_date, start_t)
                day_end = datetime.combine(current_date, end_t)
            except IndexError:
                continue
        potential_slot = day_start
        while potential_slot + timedelta(minutes=duration_minutes) <= day_end:
            if potential_slot not in booked_slots_set:
                if potential_slot > datetime.now():
                    available_slots.append(potential_slot)
            potential_slot += timedelta(minutes=duration_minutes)
    return available_slots




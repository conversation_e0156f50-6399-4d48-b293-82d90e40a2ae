import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Row, Col, Card } from 'react-bootstrap';
import { createService } from '../services/serviceService';
import { createExpert } from '../services/expertService';
import { getAdminDashboardStats } from '../services/dashboardService';

const DebugPage = () => {
  const [result, setResult] = useState('');
  const [loading, setLoading] = useState(false);

  const testServiceCreation = async () => {
    setLoading(true);
    setResult('Testing service creation...');
    
    try {
      console.log('DebugPage: Testing service creation');
      
      const serviceData = {
        name: "Debug Test Service",
        description: "Testing service creation from frontend",
        price: "75.00",
        currency: "USD",
        duration_minutes: 90
      };
      
      const response = await createService(serviceData);
      console.log('DebugPage: Service creation response', response);
      
      setResult(`✅ Service Creation Success!\nResponse: ${JSON.stringify(response.data, null, 2)}`);
      
    } catch (error) {
      console.error('DebugPage: Service creation error', error);
      setResult(`❌ Service Creation Error: ${error.message}\nDetails: ${JSON.stringify(error.response?.data, null, 2)}`);
    } finally {
      setLoading(false);
    }
  };

  const testExpertCreation = async () => {
    setLoading(true);
    setResult('Testing expert creation...');
    
    try {
      console.log('DebugPage: Testing expert creation');
      
      const expertData = {
        full_name: "Debug Test Expert",
        email: `debug.expert.${Date.now()}@test.com`,
        phone_number: "555-0199",
        profile: {
          specialty: "Frontend Debugging",
          bio: "Expert in debugging frontend applications"
        }
      };
      
      const response = await createExpert(expertData);
      console.log('DebugPage: Expert creation response', response);
      
      setResult(`✅ Expert Creation Success!\nResponse: ${JSON.stringify(response.data, null, 2)}`);
      
    } catch (error) {
      console.error('DebugPage: Expert creation error', error);
      setResult(`❌ Expert Creation Error: ${error.message}\nDetails: ${JSON.stringify(error.response?.data, null, 2)}`);
    } finally {
      setLoading(false);
    }
  };

  const testDashboardStats = async () => {
    setLoading(true);
    setResult('Testing dashboard stats...');

    try {
      console.log('DebugPage: Testing dashboard stats');

      const response = await getAdminDashboardStats();
      console.log('DebugPage: Dashboard stats response', response);

      setResult(`✅ Dashboard Stats Success!\nResponse: ${JSON.stringify(response.data, null, 2)}`);

    } catch (error) {
      console.error('DebugPage: Dashboard stats error', error);
      setResult(`❌ Dashboard Stats Error: ${error.message}\nDetails: ${JSON.stringify(error.response?.data, null, 2)}`);
    } finally {
      setLoading(false);
    }
  };

  const testAuthentication = async () => {
    setLoading(true);
    setResult('Testing authentication...');

    try {
      const token = localStorage.getItem('access_token');
      const refreshToken = localStorage.getItem('refresh_token');

      setResult(`✅ Authentication Status:
Access Token: ${token ? 'Present' : 'Missing'}
Refresh Token: ${refreshToken ? 'Present' : 'Missing'}
Token Preview: ${token ? token.substring(0, 50) + '...' : 'N/A'}`);

    } catch (error) {
      setResult(`❌ Authentication Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container style={{ padding: '20px' }}>
      <h1>Services & Experts Debug Page</h1>
      <p>This page helps debug the save functionality for services and experts.</p>
      
      <Row className="mb-4">
        <Col md={3}>
          <Card>
            <Card.Body>
              <Card.Title>Authentication Test</Card.Title>
              <Card.Text>Check if user is properly authenticated</Card.Text>
              <Button onClick={testAuthentication} disabled={loading} variant="info">
                Test Auth
              </Button>
            </Card.Body>
          </Card>
        </Col>

        <Col md={3}>
          <Card>
            <Card.Body>
              <Card.Title>Dashboard Stats Test</Card.Title>
              <Card.Text>Test dashboard stats API endpoint</Card.Text>
              <Button onClick={testDashboardStats} disabled={loading} variant="secondary">
                Test Stats
              </Button>
            </Card.Body>
          </Card>
        </Col>

        <Col md={3}>
          <Card>
            <Card.Body>
              <Card.Title>Service Creation Test</Card.Title>
              <Card.Text>Test creating a new service directly</Card.Text>
              <Button onClick={testServiceCreation} disabled={loading} variant="primary">
                Test Service
              </Button>
            </Card.Body>
          </Card>
        </Col>

        <Col md={3}>
          <Card>
            <Card.Body>
              <Card.Title>Expert Creation Test</Card.Title>
              <Card.Text>Test creating a new expert directly</Card.Text>
              <Button onClick={testExpertCreation} disabled={loading} variant="success">
                Test Expert
              </Button>
            </Card.Body>
          </Card>
        </Col>
      </Row>
      
      {result && (
        <Alert variant="info">
          <h5>Test Results:</h5>
          <pre style={{ whiteSpace: 'pre-wrap', margin: 0, fontSize: '12px' }}>
            {result}
          </pre>
        </Alert>
      )}
      
      <div style={{ marginTop: '20px' }}>
        <h3>Instructions:</h3>
        <ol>
          <li>Open browser console (F12) to see detailed logs</li>
          <li>Test authentication first to ensure you're logged in</li>
          <li>Test service creation to verify services API</li>
          <li>Test expert creation to verify experts API</li>
          <li>Check console for detailed error information</li>
        </ol>
        
        <h4>Expected Results:</h4>
        <ul>
          <li>✅ Authentication should show tokens are present</li>
          <li>✅ Service creation should return HTTP 201 with service data</li>
          <li>✅ Expert creation should return HTTP 201 with expert data</li>
        </ul>
      </div>
    </Container>
  );
};

export default DebugPage;

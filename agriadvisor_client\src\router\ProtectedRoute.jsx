// src/router/ProtectedRoute.jsx

import React from "react";
import { Navigate, Outlet } from "react-router-dom";
import useAuth from "../hooks/useAuth";

const ProtectedRoute = ({ allowedRoles }) => {
  const { user, loading } = useAuth();

  // While the context is loading the user from localStorage, show a message.
  if (loading) {
    return <div>Authenticating...</div>;
  }

  // If loading is done, check if the user is logged in AND their role is allowed.
  return user && allowedRoles?.includes(user.role) ? (
    <Outlet /> // If authorized, render the nested component (e.g., a Layout)
  ) : (
    <Navigate to="/login" replace /> // Otherwise, redirect to the login page
  );
};

export default ProtectedRoute;


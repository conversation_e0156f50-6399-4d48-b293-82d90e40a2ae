# api/views.py

from rest_framework import viewsets, permissions
from rest_framework.views import APIView
from rest_framework.response import Response
from .models import (
    GlobalIngredient, FarmerIngredient, NutrientRequirement, Formulation
)
from .serializers import (
    GlobalIngredientSerializer, FarmerIngredientSerializer,
    NutrientRequirementSerializer, FormulationSerializer
)

# --- CRUD ViewSets for Library Management ---

class GlobalIngredientViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing the Global Ingredient library.
    (Should be restricted to Superadmins in a real-world scenario).
    """
    queryset = GlobalIngredient.objects.all()
    serializer_class = GlobalIngredientSerializer
    # permission_classes = [permissions.IsAdminUser] # Example permission

class FarmerIngredientViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing a Farmer's specific ingredient sub-library.
    """
    serializer_class = FarmerIngredientSerializer
    # permission_classes = [permissions.IsAuthenticated] # Example permission

    def get_queryset(self):
        # Filter by farmer_id from the query params, e.g., /api/farmer-ingredients/?farmer_id=1
        queryset = FarmerIngredient.objects.all()
        farmer_id = self.request.query_params.get('farmer_id')
        if farmer_id is not None:
            queryset = queryset.filter(farmer_id=farmer_id)
        return queryset

class NutrientRequirementViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing Nutrient Requirement templates.
    """
    queryset = NutrientRequirement.objects.all()
    serializer_class = NutrientRequirementSerializer
    # permission_classes = [permissions.IsAuthenticated] # Example permission

class FormulationViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing Formulations.
    """
    queryset = Formulation.objects.all()
    serializer_class = FormulationSerializer
    # permission_classes = [permissions.IsAuthenticated] # Example permission

# --- Custom View for the Calculator ---

class AvailableIngredientsView(APIView):
    """
    A read-only endpoint that provides a combined list of Global and Farmer-specific
    ingredients for the formulation calculator.
    """
    # permission_classes = [permissions.IsAuthenticated] # Example permission

    def get(self, request, *args, **kwargs):
        farmer_id = request.query_params.get('farmer_id')
        if not farmer_id:
            return Response({"error": "farmer_id query parameter is required."}, status=400)

        # 1. Fetch Global Ingredients
        global_ingredients = GlobalIngredient.objects.all()
        global_serializer = GlobalIngredientSerializer(global_ingredients, many=True)
        
        # Add a 'source' field to each global ingredient
        global_data = []
        for item in global_serializer.data:
            item['source'] = 'Global'
            global_data.append(item)

        # 2. Fetch Farmer-Specific Ingredients
        farmer_ingredients = FarmerIngredient.objects.filter(farmer_id=farmer_id)
        farmer_serializer = FarmerIngredientSerializer(farmer_ingredients, many=True)
        
        # Add a 'source' field to each farmer ingredient
        farmer_data = []
        for item in farmer_serializer.data:
            item['source'] = 'Farmer'
            farmer_data.append(item)

        # 3. Combine the lists
        combined_list = global_data + farmer_data
        
        return Response(combined_list)

# We will add the Calculation and Optimization views later

    
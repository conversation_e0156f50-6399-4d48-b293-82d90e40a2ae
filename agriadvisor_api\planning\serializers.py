# planning/serializers.py

from rest_framework import serializers
from .models import (
    EnterprisePlan, PlanItem, EnterpriseTemplate, PlanAssumption,
    # Import ALL assumption models
    PigProductionAssumptions, EggProductionAssumptions, DairyLivestockAssumptions,
    BeefLivestockAssumptions, SheepProductionAssumptions, ForageProductionAssumptions,
    HorticultureCropAssumptions, FieldCropAssumptions, #<-- Added FieldCrop
    TilapiaProductionAssumptions, ConservancyAssumptions, PlantationCropAssumptions, #<-- Added the rest
    IrrigationAssumptions, PartialBudget, PartialBudgetItem
)
from accounts.models import CustomUser

# --- Assumption Serializers (One for each specialized model) ---

class PlanAssumptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = PlanAssumption
        exclude = ['id', 'plan']
        
        
class PigProductionAssumptionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = PigProductionAssumptions
        exclude = ['id', 'plan']

class EggProductionAssumptionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = EggProductionAssumptions
        exclude = ['id', 'plan']

class DairyLivestockAssumptionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = DairyLivestockAssumptions
        exclude = ['id', 'plan']

class BeefLivestockAssumptionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = BeefLivestockAssumptions
        exclude = ['id', 'plan']
        
class SheepProductionAssumptionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = SheepProductionAssumptions
        exclude = ['id', 'plan']

class ForageProductionAssumptionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = ForageProductionAssumptions
        exclude = ['id', 'plan']

class HorticultureCropAssumptionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = HorticultureCropAssumptions
        exclude = ['id', 'plan']

# --- NEWLY ADDED SERIALIZERS ---

class FieldCropAssumptionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = FieldCropAssumptions
        exclude = ['id', 'plan']

class TilapiaProductionAssumptionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = TilapiaProductionAssumptions
        exclude = ['id', 'plan']

class ConservancyAssumptionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = ConservancyAssumptions
        exclude = ['id', 'plan']

class PlantationCropAssumptionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = PlantationCropAssumptions
        exclude = ['id', 'plan']
        
class IrrigationAssumptionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = IrrigationAssumptions
        exclude = ['id', 'plan']


# --- Core Serializers ---

class EnterpriseTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = EnterpriseTemplate
        fields = ['id', 'name', 'description', 'icon', 'model_type']

class PlanItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = PlanItem
        fields = [
            'id', 'item_type', 'category', 'item_name', 
            'quantity', 'price_per_unit', 'projected_amount', 'actual_amount'
        ]

class EnterprisePlanSerializer(serializers.ModelSerializer):
    """
    A comprehensive serializer for an Enterprise Plan that includes all
    possible nested assumption models.
    """
    plan_items = PlanItemSerializer(many=True, read_only=True)
    template_name = serializers.CharField(source='based_on_template.name', read_only=True)
    model_type = serializers.CharField(source='based_on_template.model_type', read_only=True)
    farmer_name = serializers.CharField(source='farmer.get_full_name', read_only=True)

    # Nest all possible assumption models. They will only be included if they exist.
    pig_assumptions = PigProductionAssumptionsSerializer(read_only=True)
    egg_assumptions = EggProductionAssumptionsSerializer(read_only=True)
    dairy_assumptions = DairyLivestockAssumptionsSerializer(read_only=True)
    beef_assumptions = BeefLivestockAssumptionsSerializer(read_only=True)
    sheep_assumptions = SheepProductionAssumptionsSerializer(read_only=True)
    forage_assumptions = ForageProductionAssumptionsSerializer(read_only=True)
    horticulture_assumptions = HorticultureCropAssumptionsSerializer(read_only=True)
    irrigation_assumptions = IrrigationAssumptionsSerializer(read_only=True)
    
    # --- ADD THE MISSING NESTED SERIALIZERS ---
    field_crop_assumptions = FieldCropAssumptionsSerializer(read_only=True)
    tilapia_assumptions = TilapiaProductionAssumptionsSerializer(read_only=True)
    conservancy_assumptions = ConservancyAssumptionsSerializer(read_only=True)
    plantation_assumptions = PlantationCropAssumptionsSerializer(read_only=True)
    # ---

    class Meta:
        model = EnterprisePlan
        fields = [
            'id', 'name', 'farmer', 'based_on_template', 'created_at', 'assessment_data',
            'template_name', 'model_type', 'farmer_name', 'plan_items',
            # Include all assumption fields in the main serialization
            'pig_assumptions', 'egg_assumptions', 'dairy_assumptions', 'beef_assumptions',
            'sheep_assumptions', 'forage_assumptions', 'horticulture_assumptions',
            'irrigation_assumptions',
            'field_crop_assumptions', 'tilapia_assumptions', 'conservancy_assumptions',
            'plantation_assumptions',
        ]
        read_only_fields = ['expert', 'tenant']



class PartialBudgetItemSerializer(serializers.ModelSerializer):
    # 'total' is a read-only field calculated by the model's @property
    total = serializers.DecimalField(max_digits=14, decimal_places=2, read_only=True)

    class Meta:
        model = PartialBudgetItem
        # These are the fields the frontend will send and receive for each line item
        fields = [
            'id', 'budget', 'effect_type', 'description', 
            'number', 'amount', 'price', 'total'
        ]



class PartialBudgetSerializer(serializers.ModelSerializer):
    # On read, show the full list of items nested inside the budget
    items = PartialBudgetItemSerializer(many=True, read_only=True)
    
    # For display in a list, show the farmer's name
    farmer_name = serializers.CharField(source='farmer.get_full_name', read_only=True)
    
    # When creating, we only need the farmer's ID
    farmer = serializers.PrimaryKeyRelatedField(
        queryset=CustomUser.objects.filter(role='farmer')
    )

    class Meta:
        model = PartialBudget
        fields = [
            'id', 'name', 'farmer', 'farmer_name', 'proposed_change_desc', 
            'notes', 'created_at', 'items'
        ]
        # These fields are set by the backend automatically
        read_only_fields = ['expert', 'tenant', 'farmer_name']

    def __init__(self, *args, **kwargs):
        """
        Dynamically filter the 'farmer' queryset to only show farmers
        in the expert's tenant.
        """
        super().__init__(*args, **kwargs)
        request = self.context.get('request')
        if request and hasattr(request, "user"):
            user = request.user
            if hasattr(user, 'tenant') and user.tenant:
                self.fields['farmer'].queryset = CustomUser.objects.filter(
                    tenant=user.tenant, role='farmer'
                )





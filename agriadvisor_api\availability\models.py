# availability/models.py

from django.db import models
from accounts.models import CustomUser

class AvailabilityRule(models.Model):
    DAY_CHOICES = [
        (0, 'Monday'), (1, 'Tuesday'), (2, 'Wednesday'), (3, 'Thursday'),
        (4, 'Friday'), (5, 'Saturday'), (6, 'Sunday'),
    ]
    expert = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name="availability_rules", limit_choices_to={'role': 'expert'})
    day_of_week = models.IntegerField(choices=DAY_CHOICES)
    start_time = models.TimeField()
    end_time = models.TimeField()
    class Meta:
        unique_together = ('expert', 'day_of_week', 'start_time', 'end_time')
    def __str__(self):
        return f"{self.expert.username} - {self.get_day_of_week_display()} {self.start_time}-{self.end_time}"

class AvailabilityException(models.Model):
    expert = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name="availability_exceptions", limit_choices_to={'role': 'expert'})
    date = models.DateField()
    is_available = models.BooleanField(default=False)
    start_time = models.TimeField(null=True, blank=True)
    end_time = models.TimeField(null=True, blank=True)
    def __str__(self):
        status = "Available" if self.is_available else "Unavailable"
        return f"{self.expert.username} - {self.date}: {status}"




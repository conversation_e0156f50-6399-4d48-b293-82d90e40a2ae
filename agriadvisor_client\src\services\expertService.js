// src/services/expertService.js

import api from "./api";

// For Admin
export const getExperts = () => api.get("/accounts/experts/");

export const createExpert = async (expertData) => {
  console.log("expertService: Creating expert with data", expertData);
  try {
    const response = await api.post("/accounts/experts/", expertData);
    console.log("expertService: Create expert response", response);
    return response;
  } catch (error) {
    console.error("expertService: Create expert error", error);
    throw error;
  }
};

export const updateExpert = async (id, expertData) => {
  console.log("expertService: Updating expert", id, "with data", expertData);
  try {
    const response = await api.put(`/accounts/experts/${id}/`, expertData);
    console.log("expertService: Update expert response", response);
    return response;
  } catch (error) {
    console.error("expertService: Update expert error", error);
    throw error;
  }
};

export const deleteExpert = (id) => api.delete(`/accounts/experts/${id}/`);

// For Farmer
export const getFarmerExperts = async () => {
  console.log("expertService: Calling /accounts/farmer/experts/");
  try {
    const response = await api.get("/accounts/farmer/experts/");
    console.log("expertService: Farmer experts response", response);
    return response;
  } catch (error) {
    console.error("expertService: Error calling /accounts/farmer/experts/", error);
    throw error;
  }
};




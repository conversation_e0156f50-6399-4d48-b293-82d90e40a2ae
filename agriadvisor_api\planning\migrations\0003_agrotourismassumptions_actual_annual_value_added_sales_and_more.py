# Generated by Django 5.2.5 on 2025-08-28 10:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('planning', '0002_enterprisetemplate_model_type_agrotourismassumptions_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='agrotourismassumptions',
            name='actual_annual_value_added_sales',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True),
        ),
        migrations.AddField(
            model_name='agrotourismassumptions',
            name='actual_avg_revenue_per_guest_night',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True),
        ),
        migrations.AddField(
            model_name='agrotourismassumptions',
            name='actual_break_even_annual_value_added_sales',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True),
        ),
        migrations.AddField(
            model_name='agrotourismassumptions',
            name='actual_break_even_avg_revenue_per_guest_night',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True),
        ),
        migrations.AddField(
            model_name='agrotourismassumptions',
            name='actual_break_even_avg_tours_per_year',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='agrotourismassumptions',
            name='actual_break_even_cost_of_food_per_guest_day',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='agrotourismassumptions',
            name='actual_break_even_lodging_capacity_rooms',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='agrotourismassumptions',
            name='actual_break_even_payback_period_years',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='agrotourismassumptions',
            name='actual_break_even_price_per_tour',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='agrotourismassumptions',
            name='actual_break_even_roi',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='agrotourismassumptions',
            name='actual_break_even_target_annual_occupancy_percent',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='agrotourismassumptions',
            name='actual_break_even_total_cost',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True),
        ),
        migrations.AddField(
            model_name='agrotourismassumptions',
            name='actual_break_even_total_land_area_acres',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='agrotourismassumptions',
            name='actual_break_even_total_profit',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True),
        ),
        migrations.AddField(
            model_name='agrotourismassumptions',
            name='actual_break_even_total_revenue',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True),
        ),
        migrations.AddField(
            model_name='agrotourismassumptions',
            name='actual_cost_of_food_per_guest_day',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='agrotourismassumptions',
            name='actual_payback_period_years',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='agrotourismassumptions',
            name='actual_price_per_tour',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='agrotourismassumptions',
            name='actual_roi',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='beeflivestockassumptions',
            name='actual_birth_rate_percent',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='beeflivestockassumptions',
            name='actual_feed_conversion_ratio',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True),
        ),
        migrations.AddField(
            model_name='beeflivestockassumptions',
            name='actual_price_per_kg_live_weight',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='beeflivestockassumptions',
            name='actual_sale_weight_kg',
            field=models.DecimalField(blank=True, decimal_places=1, max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='beeflivestockassumptions',
            name='actual_weaning_weight_kg',
            field=models.DecimalField(blank=True, decimal_places=1, max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='broilerproductionassumptions',
            name='actual_feed_conversion_ratio',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True),
        ),
        migrations.AddField(
            model_name='broilerproductionassumptions',
            name='actual_sale_price_per_kg',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='conservancyassumptions',
            name='actual_avg_price_per_au',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='conservancyassumptions',
            name='actual_avg_revenue_per_package',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True),
        ),
        migrations.AddField(
            model_name='conservancyassumptions',
            name='actual_break_even_avg_price_per_au',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='conservancyassumptions',
            name='actual_break_even_avg_revenue_per_package',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True),
        ),
        migrations.AddField(
            model_name='conservancyassumptions',
            name='actual_break_even_live_game_offtake_au',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='conservancyassumptions',
            name='actual_break_even_lodge_rooms',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='conservancyassumptions',
            name='actual_break_even_payback_period_years',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='conservancyassumptions',
            name='actual_break_even_price_per_night',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='conservancyassumptions',
            name='actual_break_even_roi',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='conservancyassumptions',
            name='actual_break_even_target_occupancy_percent',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='conservancyassumptions',
            name='actual_break_even_total_land_area_acres',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='conservancyassumptions',
            name='actual_break_even_trophy_hunting_packages_sold',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='conservancyassumptions',
            name='actual_payback_period_years',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='conservancyassumptions',
            name='actual_price_per_night',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='conservancyassumptions',
            name='actual_roi',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='dairylivestockassumptions',
            name='actual_avg_milk_yield_liters_per_day',
            field=models.DecimalField(blank=True, decimal_places=1, max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='dairylivestockassumptions',
            name='actual_feed_conversion_ratio',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True),
        ),
        migrations.AddField(
            model_name='dairylivestockassumptions',
            name='actual_price_per_liter_milk',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='eggproductionassumptions',
            name='actual_avg_hen_housed_production_percent',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='eggproductionassumptions',
            name='actual_feed_conversion_ratio',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True),
        ),
        migrations.AddField(
            model_name='eggproductionassumptions',
            name='actual_mortality_during_lay_percent',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='eggproductionassumptions',
            name='actual_sale_price_per_tray',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='fieldcropassumptions',
            name='actual_break_even_expected_yield_kg_per_hectare',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='fieldcropassumptions',
            name='actual_break_even_field_size_hectares',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='fieldcropassumptions',
            name='actual_break_even_payback_period_years',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='fieldcropassumptions',
            name='actual_break_even_roi',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='fieldcropassumptions',
            name='actual_break_even_sale_price_per_bag',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='fieldcropassumptions',
            name='actual_break_even_sale_price_per_kg',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='fieldcropassumptions',
            name='actual_expected_yield_in_bags',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='fieldcropassumptions',
            name='actual_expected_yield_kg_per_hectare',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='fieldcropassumptions',
            name='actual_field_size_hectares',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='fieldcropassumptions',
            name='actual_payback_period_years',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='fieldcropassumptions',
            name='actual_roi',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='fieldcropassumptions',
            name='actual_sale_price_per_bag',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='fieldcropassumptions',
            name='actual_sale_price_per_kg',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='forageproductionassumptions',
            name='actual_cuts_per_year',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='forageproductionassumptions',
            name='actual_productive_lifespan_years',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='forageproductionassumptions',
            name='actual_sale_price_per_bale',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='forageproductionassumptions',
            name='actual_yield_bales_per_cut',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='horticulturecropassumptions',
            name='actual_expected_yield_kg_per_acre',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='horticulturecropassumptions',
            name='actual_market_commission_percent',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='horticulturecropassumptions',
            name='actual_percent_grade_a',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='horticulturecropassumptions',
            name='actual_percent_grade_b',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='horticulturecropassumptions',
            name='actual_percent_grade_c',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='horticulturecropassumptions',
            name='actual_sale_price_grade_a_per_kg',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='horticulturecropassumptions',
            name='actual_sale_price_grade_b_per_kg',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='horticulturecropassumptions',
            name='actual_sale_price_grade_c_per_kg',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='horticulturecropassumptions',
            name='actual_seedling_price_each',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='irrigationassumptions',
            name='actual_break_even_area_to_irrigate_acres',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='irrigationassumptions',
            name='actual_break_even_irrigated_yield_units_per_acre',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='irrigationassumptions',
            name='actual_break_even_payback_period_years',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='irrigationassumptions',
            name='actual_break_even_rainfed_yield_units_per_acre',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='irrigationassumptions',
            name='actual_break_even_roi',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='irrigationassumptions',
            name='actual_irrigated_yield_units_per_acre',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='irrigationassumptions',
            name='actual_payback_period_years',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='irrigationassumptions',
            name='actual_rainfed_yield_units_per_acre',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='irrigationassumptions',
            name='actual_roi',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='pigproductionassumptions',
            name='actual_farrowing_rate_percent',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='pigproductionassumptions',
            name='actual_feed_conversion_ratio',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True),
        ),
        migrations.AddField(
            model_name='pigproductionassumptions',
            name='actual_pigs_weaned_per_sow_per_year',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='pigproductionassumptions',
            name='actual_sale_price_per_kg',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='pigproductionassumptions',
            name='actual_sale_weight_kg',
            field=models.DecimalField(blank=True, decimal_places=1, max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='plantationcropassumptions',
            name='actual_break_even_expected_mai_m3_ha_yr',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='plantationcropassumptions',
            name='actual_break_even_gestation_period_years',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='plantationcropassumptions',
            name='actual_break_even_payback_period_years',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='plantationcropassumptions',
            name='actual_break_even_roi',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='plantationcropassumptions',
            name='actual_break_even_stumpage_price_m3_y7',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='plantationcropassumptions',
            name='actual_break_even_total_area_hectares',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='plantationcropassumptions',
            name='actual_expected_mai_m3_ha_yr',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='plantationcropassumptions',
            name='actual_payback_period_years',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='plantationcropassumptions',
            name='actual_roi',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='plantationcropassumptions',
            name='actual_stumpage_price_m3_y7',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='sheepproductionassumptions',
            name='actual_avg_sale_weight_cull_ewes_kg',
            field=models.DecimalField(blank=True, decimal_places=1, max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='sheepproductionassumptions',
            name='actual_avg_sale_weight_lambs_kg',
            field=models.DecimalField(blank=True, decimal_places=1, max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='sheepproductionassumptions',
            name='actual_lambing_percent',
            field=models.DecimalField(blank=True, decimal_places=1, max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='sheepproductionassumptions',
            name='actual_pre_weaning_lamb_mortality_percent',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='sheepproductionassumptions',
            name='actual_sale_price_cull_ewes_per_kg',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='sheepproductionassumptions',
            name='actual_sale_price_lambs_per_kg',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='sheepproductionassumptions',
            name='actual_sale_price_wool_per_kg',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='tilapiaproductionassumptions',
            name='actual_break_even_avg_feed_bag_weight_kg',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='tilapiaproductionassumptions',
            name='actual_break_even_avg_feed_price_per_bag',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='tilapiaproductionassumptions',
            name='actual_break_even_avg_feed_price_per_kg',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='tilapiaproductionassumptions',
            name='actual_break_even_cost_per_kg',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='tilapiaproductionassumptions',
            name='actual_break_even_feed_conversion_ratio',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True),
        ),
        migrations.AddField(
            model_name='tilapiaproductionassumptions',
            name='actual_break_even_feed_cost_per_kg',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='tilapiaproductionassumptions',
            name='actual_break_even_fingerling_price_each',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='tilapiaproductionassumptions',
            name='actual_break_even_payback_period_years',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='tilapiaproductionassumptions',
            name='actual_break_even_productivity_kg_per_m2',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='tilapiaproductionassumptions',
            name='actual_break_even_roi',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='tilapiaproductionassumptions',
            name='actual_break_even_sale_price_per_kg',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='tilapiaproductionassumptions',
            name='actual_cost_per_kg',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='tilapiaproductionassumptions',
            name='actual_feed_conversion_ratio',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True),
        ),
        migrations.AddField(
            model_name='tilapiaproductionassumptions',
            name='actual_feed_cost_per_kg',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='tilapiaproductionassumptions',
            name='actual_payback_period_years',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='tilapiaproductionassumptions',
            name='actual_productivity_kg_per_m2',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='tilapiaproductionassumptions',
            name='actual_roi',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='tilapiaproductionassumptions',
            name='actual_sale_price_per_kg',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
    ]

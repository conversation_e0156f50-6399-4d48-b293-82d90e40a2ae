// src/components/Header.jsx
import React from "react";
import useAuth from "../hooks/useAuth";
import { Dropdown, Badge } from "react-bootstrap";
import { Link } from "react-router-dom";

const Header = ({ pageTitle }) => {
  const { user, logout } = useAuth();
  const initials = user?.username ? user.username.substring(0, 2).toUpperCase() : "AU";
  const avatarStyle = {
    width: "40px",
    height: "40px",
    borderRadius: "50%",
    background: "linear-gradient(45deg, #228B22, #90EE90)",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    color: "white",
    fontWeight: "bold",
  };

  return (
    <header className="bg-white shadow-sm p-3 mb-4">
      <div className="d-flex justify-content-between align-items-center">
        <div>
          <h4 className="mb-0">{pageTitle}</h4>
          <small className="text-muted">Welcome back, {user?.username || "Admin User"}</small>
        </div>
        <div className="d-flex align-items-center">
          <div className="me-3 position-relative">
            <i className="bi bi-bell fs-5"></i>
            <Badge
              pill
              bg="danger"
              className="position-absolute top-0 start-100 translate-middle"
              style={{ fontSize: "0.6rem" }}
            >
              3
            </Badge>
          </div>
          <Dropdown align="end">
            <Dropdown.Toggle as="div" style={avatarStyle} className="cursor-pointer">
              {initials}
            </Dropdown.Toggle>
            <Dropdown.Menu>
              <Dropdown.Item as={Link} to="/admin/profile">
                Profile
              </Dropdown.Item>
              <Dropdown.Item href="#">Settings</Dropdown.Item>
              <Dropdown.Divider />
              <Dropdown.Item onClick={logout} className="text-danger">
                Logout
              </Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>
        </div>
      </div>
    </header>
  );
};
export default Header;

# api/urls.py

from django.urls import path
from rest_framework.routers import DefaultRouter
from .views import (
    GlobalIngredientViewSet, FarmerIngredientViewSet, NutrientRequirementViewSet,
    FormulationViewSet, AvailableIngredientsView
)

router = DefaultRouter()
router.register(r'global-ingredients', GlobalIngredientViewSet, basename='global-ingredient')
router.register(r'farmer-ingredients', FarmerIngredientViewSet, basename='farmer-ingredient')
router.register(r'nutrient-requirements', NutrientRequirementViewSet, basename='nutrient-requirement')
router.register(r'formulations', FormulationViewSet, basename='formulation')

urlpatterns = router.urls + [
    # Custom endpoint for the calculator ingredient list
    path('available-ingredients/', AvailableIngredientsView.as_view(), name='available-ingredients'),
]




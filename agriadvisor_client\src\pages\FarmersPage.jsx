// src/pages/FarmersPage.jsx
import React, { useState, useEffect } from "react";
import { Container, Table, Spinner, Alert } from "react-bootstrap";
import { getFarmers } from "../services/farmerService";

const FarmersPage = () => {
  const [farmers, setFarmers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    const fetchFarmers = async () => {
      try {
        const data = await getFarmers();
        setFarmers(data);
      } catch (err) {
        setError("Failed to fetch farmers.");
      } finally {
        setLoading(false);
      }
    };
    fetchFarmers();
  }, []);

  if (loading)
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" />
      </Container>
    );
  if (error)
    return (
      <Container className="mt-4">
        <Alert variant="danger">{error}</Alert>
      </Container>
    );

  return (
    <Container fluid>
      <Table striped bordered hover responsive>
        <thead>
          <tr>
            <th>Name</th>
            <th>Email</th>
            <th>Onboarding Complete?</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {farmers.length > 0 ? (
            farmers.map((farmer) => (
              <tr key={farmer.id}>
                <td>
                  {farmer.first_name} {farmer.last_name}
                </td>
                <td>{farmer.email}</td>
                <td>{farmer.has_completed_onboarding ? "Yes" : "No"}</td>
                <td>
                  <button className="btn btn-sm btn-outline-info" disabled>
                    View Details
                  </button>
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan="4" className="text-center">
                No farmers have registered yet.
              </td>
            </tr>
          )}
        </tbody>
      </Table>
    </Container>
  );
};
export default FarmersPage;

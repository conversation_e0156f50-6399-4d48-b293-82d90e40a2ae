// src/components/PlanItemModal.jsx

import React, { useState, useEffect } from "react";
import { Modal, Button, Form } from "react-bootstrap";

const PlanItemModal = ({ show, onHide, onSave, item, itemType, planId }) => {
  const [formData, setFormData] = useState({
    item_name: "",
    category: "",
    unit: "",
    quantity: "",
    price_per_unit: "",
  });

  const isEditing = item !== null;

  useEffect(() => {
    if (isEditing) {
      setFormData({
        item_name: item.item_name || "",
        category: item.category || "",
        unit: item.unit || "",
        quantity: item.quantity || "",
        price_per_unit: item.price_per_unit || "",
      });
    } else {
      // Reset for adding a new item
      setFormData({ item_name: "", category: "", unit: "", quantity: "", price_per_unit: "" });
    }
  }, [item, show]);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Pass back all the data the API needs
    onSave({ ...formData, item_type: itemType, plan: planId });
  };

  // Determine a user-friendly title for the modal
  const titleType = itemType ? itemType.replace("_", " ") : "Item";

  return (
    <Modal show={show} onHide={onHide}>
      <Form onSubmit={handleSubmit}>
        <Modal.Header closeButton>
          <Modal.Title className="text-capitalize">
            {isEditing ? "Edit" : "Add"} {titleType}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form.Group className="mb-3">
            <Form.Label>Item Name</Form.Label>
            <Form.Control type="text" name="item_name" value={formData.item_name} onChange={handleChange} required />
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Category</Form.Label>
            <Form.Control type="text" name="category" value={formData.category} onChange={handleChange} required />
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Unit (e.g., Kg, Acre, Head)</Form.Label>
            <Form.Control type="text" name="unit" value={formData.unit} onChange={handleChange} required />
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Quantity</Form.Label>
            <Form.Control
              type="number"
              name="quantity"
              step="0.01"
              value={formData.quantity}
              onChange={handleChange}
              required
            />
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Price per Unit ($)</Form.Label>
            <Form.Control
              type="number"
              name="price_per_unit"
              step="0.01"
              value={formData.price_per_unit}
              onChange={handleChange}
              required
            />
          </Form.Group>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={onHide}>
            Cancel
          </Button>
          <Button variant="primary" type="submit">
            Save Item
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
};
export default PlanItemModal;



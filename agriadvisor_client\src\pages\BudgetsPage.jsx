// src/pages/BudgetsPage.jsx

import React, { useState, useEffect, useMemo } from "react";
import { Container, Row, Col, Card, ListGroup, Button, Table, Spinner, Alert } from "react-bootstrap";
import {
  getBudgets,
  getBudgetDetail,
  createBudget,
  createBudgetItem,
  updateBudgetItem,
  deleteBudgetItem,
} from "../services/budgetService";
import BudgetItemModal from "../components/BudgetItemModal";
import NewBudgetModal from "../components/NewBudgetModal"; // <-- IMPORT


/**
 * A helper function to format a number as a currency string.
 * Uses spaces for thousand separators and ensures two decimal places.
 * Example: 1194000 -> "$1 194 000.00"
 * @param {number|string} number - The number to format.
 * @returns {string} The formatted currency string.
 */
const formatCurrency = (number) => {
  const num = parseFloat(number || 0);
  return new Intl.NumberFormat('en-GB', { // 'en-GB' often uses spaces, but we can customize
    style: 'currency',
    currency: 'USD', // Or make this dynamic if needed
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(num).replace(/,/g, ' '); // Replace commas with spaces for the desired style
};


const BudgetsPage = () => {
  const [budgets, setBudgets] = useState([]);
  const [selectedBudget, setSelectedBudget] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showNewBudgetModal, setShowNewBudgetModal] = useState(false);

  const fetchBudgets = async () => {
    try {
      const response = await getBudgets();
      setBudgets(response.data);
    } catch (error) {
      console.error("Failed to fetch budgets", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBudgets();
  }, []);

  const handleSelectBudget = async (id) => {
    try {
      const response = await getBudgetDetail(id);
      setSelectedBudget(response.data);
    } catch (error) {
      console.error("Failed to fetch budget details", error);
    }
  };

  const handleSaveNewBudget = async (budgetData) => {
    try {
      await createBudget(budgetData);
      setShowNewBudgetModal(false);
      fetchBudgets(); // Refresh the whole list
    } catch (error) {
      alert("Failed to create new budget.");
    }
  };

  const refreshSelectedBudget = () => {
    if (selectedBudget) handleSelectBudget(selectedBudget.id);
  };

  if (loading)
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" />
      </Container>
    );

  return (
    <Container fluid>
      <Row>
        {/* Left Panel: Budget List */}
        <Col md={4} className="mb-3">
          <Card>
            <Card.Header as="h4" className="d-flex justify-content-between align-items-center">
              Budgets
              <Button size="sm" onClick={() => setShowNewBudgetModal(true)}>
                + New Budget
              </Button>
            </Card.Header>
            <ListGroup variant="flush">
              {budgets.map((budget) => (
                <ListGroup.Item
                  key={budget.id}
                  action
                  active={selectedBudget?.id === budget.id}
                  onClick={() => handleSelectBudget(budget.id)}
                >
                  {budget.name} <small className="text-muted">({budget.farmer_name})</small>
                </ListGroup.Item>
              ))}
            </ListGroup>
          </Card>
        </Col>

        {/* Right Panel: Budget Worksheet */}
        <Col md={8}>
          {selectedBudget ? (
            <BudgetWorksheet budget={selectedBudget} onRefresh={refreshSelectedBudget} />
          ) : (
            <Card>
              <Card.Body className="text-center text-muted">
                <p>Select a budget from the list.</p>
              </Card.Body>
            </Card>
          )}
        </Col>
      </Row>
      <NewBudgetModal
        show={showNewBudgetModal}
        onHide={() => setShowNewBudgetModal(false)}
        onSave={handleSaveNewBudget}
      />
    </Container>
  );
};

const BudgetWorksheet = ({ budget, onRefresh }) => {
  const [showModal, setShowModal] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [itemType, setItemType] = useState("income");

  const handleShowAddItemModal = (type) => {
    setItemType(type);
    setEditingItem(null);
    setShowModal(true);
  };
  const handleShowEditItemModal = (item) => {
    setItemType(item.item_type);
    setEditingItem(item);
    setShowModal(true);
  };
  const handleCloseModal = () => setShowModal(false);

  const handleSaveItem = async (itemData) => {
    const dataToSave = { ...itemData, budget: budget.id };
    try {
      if (editingItem) {
        await updateBudgetItem(editingItem.id, dataToSave);
      } else {
        await createBudgetItem(dataToSave);
      }
      onRefresh();
      handleCloseModal();
    } catch (error) {
      alert("Failed to save item.");
    }
  };

  const handleDeleteItem = async (id) => {
    if (window.confirm("Delete this item?")) {
      try {
        await deleteBudgetItem(id);
        onRefresh();
      } catch (error) {
        alert("Failed to delete item.");
      }
    }
  };

  const incomeItems = budget.items.filter((i) => i.item_type === "income");
  const variableCostItems = budget.items.filter((i) => i.item_type === "variable");
  const fixedCostItems = budget.items.filter((i) => i.item_type === "fixed");

  const totals = useMemo(() => {
    const calculate = (items) =>
      items.reduce(
        (acc, item) => {
          acc.proj += parseFloat(item.projected_amount || 0);
          acc.act += parseFloat(item.actual_amount || 0);
          return acc;
        },
        { proj: 0, act: 0 }
      );

    const income = calculate(incomeItems);
    const variable = calculate(variableCostItems);
    const fixed = calculate(fixedCostItems);

    return {
      projIncome: income.proj,
      actIncome: income.act,
      projVar: variable.proj,
      actVar: variable.act,
      projFix: fixed.proj,
      actFix: fixed.act,
    };
  }, [budget.items]);

  return (
    <div>
      <BudgetSection
        title="Sources of Income"
        items={incomeItems}
        onAdd={() => handleShowAddItemModal("income")}
        onEdit={handleShowEditItemModal}
        onDelete={handleDeleteItem}
      />
      <BudgetSection
        title="Variable Costs"
        items={variableCostItems}
        onAdd={() => handleShowAddItemModal("variable")}
        onEdit={handleShowEditItemModal}
        onDelete={handleDeleteItem}
      />
      <BudgetSection
        title="Fixed Costs"
        items={fixedCostItems}
        onAdd={() => handleShowAddItemModal("fixed")}
        onEdit={handleShowEditItemModal}
        onDelete={handleDeleteItem}
      />
      <Summary totals={totals} />
      <BudgetItemModal
        show={showModal}
        onHide={handleCloseModal}
        onSave={handleSaveItem}
        item={editingItem}
        itemType={itemType}
      />
    </div>
  );
};

const BudgetSection = ({ title, items, onAdd, onEdit, onDelete }) => (
  <Card className="mb-3">
    <Card.Header className="d-flex justify-content-between align-items-center">
      <h5>{title}</h5>
      <Button variant="outline-primary" size="sm" onClick={onAdd}>
        + Add
      </Button>
    </Card.Header>
    <Card.Body>
      <Table striped bordered size="sm">
        <thead>
          <tr>
            <th>Category</th>
            <th>Description</th>
            <th className="text-end">Projected</th> {/* <-- ALIGN HEADER */}
            <th className="text-end">Actual</th> {/* <-- ALIGN HEADER */}
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {items.map((item) => (
            <tr key={item.id}>
              <td>{item.category}</td>
              <td>{item.description}</td>
              <td className="text-end">{formatCurrency(item.projected_amount)}</td> {/* <-- FORMAT & ALIGN */}
              <td className="text-end">{formatCurrency(item.actual_amount)}</td> {/* <-- FORMAT & ALIGN */}
              <td>
                <Button variant="outline-secondary" size="sm" className="me-1" onClick={() => onEdit(item)}>
                  Edit
                </Button>
                <Button variant="outline-danger" size="sm" onClick={() => onDelete(item.id)}>
                  Del
                </Button>
              </td>
            </tr>
          ))}
          {items.length === 0 && (
            <tr>
              <td colSpan="5" className="text-center">
                No items added.
              </td>
            </tr>
          )}
        </tbody>
      </Table>
    </Card.Body>
  </Card>
);

// --- THIS IS THE SUMMARY COMPONENT THAT WAS MISSING ---
const Summary = ({ totals }) => (
  <Card bg="light">
    <Card.Header as="h5">Budget Summary</Card.Header>
    <Card.Body>
      <Row>
        <Col>
          <strong>Category</strong>
        </Col>
        <Col className="text-end">
          <strong>Projected</strong>
        </Col>
        <Col className="text-end">
          <strong>Actual</strong>
        </Col>
      </Row>
      <hr className="my-2" />
      <Row>
        <Col>Total Income:</Col>
        <Col className="text-end">{formatCurrency(totals.projIncome)}</Col> {/* <-- FORMAT */}
        <Col className="text-end">{formatCurrency(totals.actIncome)}</Col> {/* <-- FORMAT */}
      </Row>
      <Row>
        <Col>Total Variable Costs:</Col>
        <Col className="text-end">({formatCurrency(totals.projVar)})</Col> {/* <-- FORMAT */}
        <Col className="text-end">({formatCurrency(totals.actVar)})</Col> {/* <-- FORMAT */}
      </Row>
      <Row>
        <Col>Total Fixed Costs:</Col>
        <Col className="text-end">({formatCurrency(totals.projFix)})</Col> {/* <-- FORMAT */}
        <Col className="text-end">({formatCurrency(totals.actFix)})</Col> {/* <-- FORMAT */}
      </Row>
      <hr />
      <Row className="fw-bold">
        <Col>Net Profit / (Loss):</Col>
        <Col className="text-end">{formatCurrency(totals.projIncome - totals.projVar - totals.projFix)}</Col>{" "}
        {/* <-- FORMAT */}
        <Col className="text-end">{formatCurrency(totals.actIncome - totals.actVar - totals.actFix)}</Col>{" "}
        {/* <-- FORMAT */}
      </Row>
    </Card.Body>
  </Card>
);

export default BudgetsPage;




# agriadvisor/asgi.py

import os
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
import advisory.routing # We will create this file later for real-time chat

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agriadvisor.settings')

application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    # "websocket": AuthMiddlewareStack(
    #     URLRouter(
    #         advisory.routing.websocket_urlpatterns
    #     )
    # ),
})



# Generated by Django 5.2.5 on 2025-08-26 21:50

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Announcement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ('message', models.TextField()),
                ('target_role', models.CharField(choices=[('farmer', 'Farmers Only'), ('expert', 'Experts Only'), ('all', 'All Users')], max_length=10)),
                ('is_active', models.BooleanField(default=False, help_text='Only one announcement per role can be active at a time.')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('tenant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='announcements', to='accounts.tenant')),
            ],
        ),
    ]

// src/pages/AnnouncementsPage.jsx

import React, { useState, useEffect } from "react";
import { Container, <PERSON>, <PERSON><PERSON>, Spin<PERSON>, <PERSON>ert } from "react-bootstrap";
import {
  getAnnouncements,
  createAnnouncement,
  updateAnnouncement,
  deleteAnnouncement,
} from "../services/announcementService";
import AnnouncementModal from "../components/AnnouncementModal";

const AnnouncementsPage = () => {
  const [announcements, setAnnouncements] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  const [showModal, setShowModal] = useState(false);
  const [editingAnn, setEditingAnn] = useState(null);

  const fetchAnnouncements = async () => {
    try {
      setLoading(true);
      const response = await getAnnouncements();
      setAnnouncements(response.data);
    } catch (err) {
      setError("Failed to fetch announcements.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnnouncements();
  }, []);

  const handleShowAddModal = () => {
    setEditingAnn(null);
    setShowModal(true);
  };

  const handleShowEditModal = (ann) => {
    setEditingAnn(ann);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setEditingAnn(null);
  };

  const handleSave = async (data) => {
    try {
      if (editingAnn) {
        await updateAnnouncement(editingAnn.id, data);
      } else {
        await createAnnouncement(data);
      }
      handleCloseModal();
      fetchAnnouncements();
    } catch (err) {
      alert("Failed to save announcement. Please try again.");
      console.error(err);
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm("Are you sure you want to delete this announcement?")) {
      try {
        await deleteAnnouncement(id);
        fetchAnnouncements();
      } catch (err) {
        alert("Failed to delete announcement.");
      }
    }
  };

  if (loading)
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" />
      </Container>
    );
  if (error)
    return (
      <Container className="mt-4">
        <Alert variant="danger">{error}</Alert>
      </Container>
    );

  return (
    <Container fluid>
      <div className="d-flex justify-content-end align-items-center mb-4">
        <Button variant="primary" onClick={handleShowAddModal}>
          <i className="bi bi-plus-lg me-2"></i>New Announcement
        </Button>
      </div>

      <Table striped bordered hover responsive>
        <thead>
          <tr>
            <th>Title</th>
            <th>Target Audience</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {announcements.length > 0 ? (
            announcements.map((ann) => (
              <tr key={ann.id}>
                <td>{ann.title}</td>
                <td className="text-capitalize">{ann.target_role}</td>
                <td>
                  {ann.is_active ? (
                    <span className="badge bg-success">Active</span>
                  ) : (
                    <span className="badge bg-secondary">Inactive</span>
                  )}
                </td>
                <td>
                  <Button
                    variant="outline-secondary"
                    size="sm"
                    className="me-2"
                    onClick={() => handleShowEditModal(ann)}
                  >
                    Edit
                  </Button>
                  <Button variant="outline-danger" size="sm" onClick={() => handleDelete(ann.id)}>
                    Delete
                  </Button>
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan="4" className="text-center">
                No announcements found.
              </td>
            </tr>
          )}
        </tbody>
      </Table>

      <AnnouncementModal show={showModal} onHide={handleCloseModal} onSave={handleSave} announcement={editingAnn} />
    </Container>
  );
};

export default AnnouncementsPage;



// src/pages/ExpertsPage.jsx

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap"; // Import Row and Col
import { getExperts, createExpert, updateExpert, deleteExpert } from "../services/expertService";
import ExpertModal from "../components/ExpertModal";
import AdminExpertCard from "../components/AdminExpertCard"; // <-- IMPORT THE NEW CARD

const ExpertsPage = () => {
  const [experts, setExperts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  const [showModal, setShowModal] = useState(false);
  const [editingExpert, setEditingExpert] = useState(null);

  console.log("ExpertsPage: Current state", { showModal, editingExpert });

  const fetchExperts = async () => {
    try {
      setLoading(true);
      console.log("ExpertsPage: Fetching experts...");
      const response = await getExperts();
      console.log("ExpertsPage: Experts response", response);
      const data = response.data || response;
      console.log("ExpertsPage: Experts data", data);
      setExperts(data);
    } catch (err) {
      console.error("ExpertsPage: Error fetching experts", err);
      setError("Failed to fetch experts.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchExperts();
  }, []);

  const handleShowAddModal = () => {
    setEditingExpert(null);
    setShowModal(true);
  };

  const handleShowEditModal = (expert) => {
    console.log("ExpertsPage: Opening edit modal for expert", expert);
    setEditingExpert(expert);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setEditingExpert(null);
  };

  const handleSaveExpert = async (expertData) => {
    console.log("ExpertsPage: Saving expert data", expertData);
    console.log("ExpertsPage: Editing expert", editingExpert);

    const formattedData = {
      full_name: expertData.full_name,
      email: expertData.email,
      phone_number: expertData.phone_number,
      profile: {
        specialty: expertData.specialty,
        bio: expertData.bio,
      },
    };

    console.log("ExpertsPage: Formatted data", formattedData);

    try {
      if (editingExpert) {
        console.log("ExpertsPage: Updating expert", editingExpert.id);
        await updateExpert(editingExpert.id, formattedData);
      } else {
        console.log("ExpertsPage: Creating new expert");
        await createExpert(formattedData);
      }
      handleCloseModal();
      await fetchExperts(); // Use await to ensure list is refreshed
    } catch (err) {
      console.error("ExpertsPage: Error saving expert", err);
      const errorMessage = err.response?.data?.detail ||
                          err.response?.data?.message ||
                          err.message ||
                          "Failed to save expert. Please ensure the email is unique and your login is not expired.";
      alert(errorMessage);
    }
  };

  const handleDeleteExpert = async (id) => {
    if (window.confirm("Are you sure you want to delete this expert?")) {
      try {
        await deleteExpert(id);
        await fetchExperts(); // Use await to ensure list is refreshed
      } catch (err) {
        alert("Failed to delete expert. They may be assigned to a booking.");
      }
    }
  };

  if (loading)
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" />
      </Container>
    );
  if (error)
    return (
      <Container className="mt-4">
        <Alert variant="danger">{error}</Alert>
      </Container>
    );

  return (
    <Container fluid>
      <div className="d-flex justify-content-end align-items-center mb-4">
        <Button variant="primary" onClick={handleShowAddModal}>
          <i className="bi bi-plus-lg me-2"></i>Add New Expert
        </Button>
      </div>

      {/* --- RENDER CARDS INSTEAD OF TABLE --- */}
      <Row>
        {console.log("ExpertsPage: Rendering experts", experts, "Length:", experts.length)}
        {experts.length > 0 ? (
          experts.map((expert) => {
            console.log("ExpertsPage: Rendering expert", expert);
            return (
              <Col key={expert.id} sm={12} md={6} lg={4} xl={3} className="mb-4">
                <AdminExpertCard expert={expert} onEdit={handleShowEditModal} onDelete={handleDeleteExpert} />
              </Col>
            );
          })
        ) : (
          <Col>
            <Alert variant="info">No experts found. Add one to get started!</Alert>
          </Col>
        )}
      </Row>

      <ExpertModal show={showModal} onHide={handleCloseModal} onSave={handleSaveExpert} expert={editingExpert} />
    </Container>
  );
};
export default ExpertsPage;



// src/components/PlanWorksheetFarmerView.jsx

import React, { useState, useEffect } from "react";
import { Card, Table, Form, Button, InputGroup, Spinner } from "react-bootstrap";
import { updatePlanItem } from "../services/planningService"; // We will use this to save

const formatCurrency = (number) => {
  const num = parseFloat(number || 0);
  return new Intl.NumberFormat("en-GB", { style: "currency", currency: "USD" }).format(num).replace(/,/g, " ");
};

const PlanWorksheetFarmerView = ({ plan, onRefresh }) => {
  // We need to manage the state of all the "actual" inputs
  const [actuals, setActuals] = useState({});
  const [isSaving, setIsSaving] = useState(false);

  // When a new plan is selected, initialize the 'actuals' state from the plan's data
  useEffect(() => {
    const initialActuals = {};
    plan.plan_items.forEach((item) => {
      initialActuals[item.id] = item.actual_amount || "";
    });
    setActuals(initialActuals);
  }, [plan]);

  const handleActualChange = (itemId, value) => {
    setActuals((prev) => ({ ...prev, [itemId]: value }));
  };

  // This is a simple save function. In a real app, you might save field by field on blur.
  const handleSaveAllActuals = async () => {
    setIsSaving(true);
    // Create an array of promises to update all changed items
    const updatePromises = Object.keys(actuals)
      .map((itemId) => {
        const originalItem = plan.plan_items.find((item) => item.id == itemId);
        // Only update if the value has changed
        if (originalItem && originalItem.actual_amount != actuals[itemId]) {
          // We only update the 'actual_amount' field
          return updatePlanItem(itemId, { actual_amount: actuals[itemId] });
        }
        return null;
      })
      .filter(Boolean); // Filter out any null promises

    try {
      await Promise.all(updatePromises);
      alert("Your actuals have been saved!");
      onRefresh(); // Refresh the data
    } catch (error) {
      console.error("Failed to save actuals", error);
      alert("An error occurred while saving. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  const incomeItems = plan.plan_items.filter((i) => i.item_type === "income");
  const variableCostItems = plan.plan_items.filter((i) => i.item_type === "variable");
  const fixedCostItems = plan.plan_items.filter((i) => i.item_type === "fixed");

  return (
    <div>
      <div className="d-flex justify-content-end mb-3">
        <Button onClick={handleSaveAllActuals} disabled={isSaving}>
          {isSaving ? <Spinner as="span" animation="border" size="sm" /> : "Save My Actuals"}
        </Button>
      </div>

      <SectionTable
        title="Sources of Income"
        items={incomeItems}
        actuals={actuals}
        onActualChange={handleActualChange}
      />
      <SectionTable
        title="Variable Costs"
        items={variableCostItems}
        actuals={actuals}
        onActualChange={handleActualChange}
      />
      <SectionTable title="Fixed Costs" items={fixedCostItems} actuals={actuals} onActualChange={handleActualChange} />

      {/* The Summary and Analysis tabs would go here, now showing Projected vs Actual */}
    </div>
  );
};

const SectionTable = ({ title, items, actuals, onActualChange }) => (
  <Card className="mb-4">
    <Card.Header as="h5">{title}</Card.Header>
    <Card.Body>
      <Table striped bordered hover size="sm">
        <thead>
          <tr>
            <th>Item</th>
            <th className="text-end">Projected</th>
            <th className="text-end">Actual</th>
          </tr>
        </thead>
        <tbody>
          {items.map((item) => (
            <tr key={item.id}>
              <td>
                {item.item_name}
                <div className="text-muted small">{item.category}</div>
              </td>
              <td className="text-end align-middle">{formatCurrency(item.projected_amount)}</td>
              <td style={{ minWidth: "150px" }}>
                <InputGroup>
                  <InputGroup.Text>$</InputGroup.Text>
                  <Form.Control
                    type="number"
                    step="0.01"
                    className="text-end"
                    value={actuals[item.id] || ""}
                    onChange={(e) => onActualChange(item.id, e.target.value)}
                    placeholder="0.00"
                  />
                </InputGroup>
              </td>
            </tr>
          ))}
          {items.length === 0 && (
            <tr>
              <td colSpan="3" className="text-center">
                No items in this category.
              </td>
            </tr>
          )}
        </tbody>
      </Table>
    </Card.Body>
  </Card>
);

export default PlanWorksheetFarmerView;




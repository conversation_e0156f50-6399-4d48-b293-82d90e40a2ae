# reviews/views.py

from rest_framework import viewsets, permissions, serializers
from .models import Review
from .serializers import ReviewSerializer
from advisory.models import Booking

class ReviewViewSet(viewsets.ModelViewSet):
    queryset = Review.objects.all().order_by('-created_at')
    serializer_class = ReviewSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]

    def get_queryset(self):
        queryset = super().get_queryset()
        expert_id = self.request.query_params.get('expert_id')
        if expert_id:
            queryset = queryset.filter(expert_id=expert_id)
        return queryset

    def perform_create(self, serializer):
        farmer = self.request.user
        booking_id = self.request.data.get('booking')
        try:
            booking = Booking.objects.get(id=booking_id, farmer=farmer, status='completed')
        except Booking.DoesNotExist:
            raise serializers.ValidationError({"booking": "You can only review your own completed bookings."})
        if Review.objects.filter(booking=booking).exists():
            raise serializers.ValidationError({"booking": "A review for this booking already exists."})
        serializer.save(farmer=farmer, expert=booking.expert)





# availability/serializers.py

from rest_framework import serializers
from .models import AvailabilityRule, AvailabilityException

class AvailabilityRuleSerializer(serializers.ModelSerializer):
    class Meta:
        model = AvailabilityRule
        fields = ['id', 'day_of_week', 'start_time', 'end_time']

class AvailabilityExceptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = AvailabilityException
        fields = ['id', 'date', 'is_available', 'start_time', 'end_time']




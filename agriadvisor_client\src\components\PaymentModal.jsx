// src/components/PaymentModal.jsx
import React from "react";
import { Modal, Row, Col } from "react-bootstrap";
import CheckoutForm from "./CheckoutForm";

const PaymentModal = ({ show, onHide, clientSecret, bookingDetails, onPaymentSuccess }) => {
  if (!bookingDetails || !clientSecret) return null;

  return (
    <Modal show={show} onHide={onHide} centered>
      <Modal.Header closeButton>
        <Modal.Title>Payment for {bookingDetails.serviceName}</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <div className="mb-4">
          <Row>
            <Col>
              <strong>Service:</strong>
            </Col>
            <Col className="text-end">{bookingDetails.serviceName}</Col>
          </Row>
          <Row>
            <Col>
              <strong>Date:</strong>
            </Col>
            <Col className="text-end">{bookingDetails.date}</Col>
          </Row>
          <Row>
            <Col>
              <strong>Time:</strong>
            </Col>
            <Col className="text-end">{bookingDetails.time}</Col>
          </Row>
          <hr />
          <Row className="fw-bold fs-5">
            <Col>Total Amount:</Col>
            <Col className="text-end text-success">${bookingDetails.amount.toFixed(2)}</Col>
          </Row>
        </div>
        <CheckoutForm clientSecret={clientSecret} onPaymentSuccess={onPaymentSuccess} amount={bookingDetails.amount} />
      </Modal.Body>
    </Modal>
  );
};
export default PaymentModal;

{"version": 3, "sources": ["../src/index.ts"], "sourcesContent": ["import { TableCell } from '@tiptap/extension-table'\n\nexport type { TableCellOptions } from '@tiptap/extension-table'\nexport { TableCell } from '@tiptap/extension-table'\n\nexport default TableCell\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,6BAA0B;AAG1B,IAAAA,0BAA0B;AAE1B,IAAO,gBAAQ;", "names": ["import_extension_table"]}
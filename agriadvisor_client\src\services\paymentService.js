// src/services/paymentService.js

import api from "./api";

export const createPaymentIntent = async (bookingData) => {
  console.log("paymentService: Creating payment intent with data:", bookingData);
  try {
    const response = await api.post("/payments/create-payment-intent/", bookingData);
    console.log("paymentService: Payment intent response:", response);
    return response;
  } catch (error) {
    console.error("paymentService: Error creating payment intent:", error);
    throw error;
  }
};


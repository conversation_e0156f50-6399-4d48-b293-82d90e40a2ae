// // src/components/BudgetItemModal.jsx

// import React, { useState, useEffect } from "react";
// import { Modal, Button, Form } from "react-bootstrap";

// const BudgetItemModal = ({ show, onHide, onSave, item, itemType }) => {
//   const [formData, setFormData] = useState({
//     category: "",
//     description: "",
//     projected_amount: "",
//   });

//   const isEditing = item !== null;

//   useEffect(() => {
//     if (isEditing) {
//       setFormData({
//         category: item.category,
//         description: item.description,
//         projected_amount: item.projected_amount,
//       });
//     } else {
//       setFormData({ category: "", description: "", projected_amount: "" });
//     }
//   }, [item, show]);

//   const handleChange = (e) => {
//     setFormData({ ...formData, [e.target.name]: e.target.value });
//   };

//   const handleSubmit = (e) => {
//     e.preventDefault();
//     // Pass back the form data along with the item type
//     onSave({ ...formData, item_type: itemType });
//   };

//   return (
//     <Modal show={show} onHide={onHide}>
//       <Form onSubmit={handleSubmit}>
//         <Modal.Header closeButton>
//           <Modal.Title>
//             {isEditing ? "Edit" : "Add"} {itemType?.replace("_", " ")} Item
//           </Modal.Title>
//         </Modal.Header>
//         <Modal.Body>
//           <Form.Group className="mb-3">
//             <Form.Label>Category</Form.Label>
//             <Form.Control type="text" name="category" value={formData.category} onChange={handleChange} required />
//           </Form.Group>
//           <Form.Group className="mb-3">
//             <Form.Label>Description (Optional)</Form.Label>
//             <Form.Control type="text" name="description" value={formData.description} onChange={handleChange} />
//           </Form.Group>
//           <Form.Group className="mb-3">
//             <Form.Label>Projected Amount ($)</Form.Label>
//             <Form.Control
//               type="number"
//               name="projected_amount"
//               step="0.01"
//               value={formData.projected_amount}
//               onChange={handleChange}
//               required
//             />
//           </Form.Group>
//         </Modal.Body>
//         <Modal.Footer>
//           <Button variant="secondary" onClick={onHide}>
//             Cancel
//           </Button>
//           <Button variant="primary" type="submit">
//             Save Item
//           </Button>
//         </Modal.Footer>
//       </Form>
//     </Modal>
//   );
// };
// export default BudgetItemModal;



// src/services/stripeService.js

import api from "./api";

/**
 * Calls the backend to create a new Stripe Connect account and get an onboarding link.
 * @returns {Promise<Object>} A promise that resolves to an object containing the onboarding_url.
 */
export const createConnectAccount = async () => {
  try {
    const response = await api.post("/stripe/create-connect-account/");
    return response.data;
  } catch (error) {
    console.error("Error creating Stripe Connect account:", error);
    throw error;
  }
};



{"hash": "82099f77", "configHash": "2258c8ce", "lockfileHash": "3104d522", "browserHash": "bd340478", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "ea86ee4e", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "f23fe516", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "466f1933", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "0548a9bb", "needsInterop": true}, "@fullcalendar/daygrid": {"src": "../../@fullcalendar/daygrid/index.js", "file": "@fullcalendar_daygrid.js", "fileHash": "7fdd6c29", "needsInterop": false}, "@fullcalendar/interaction": {"src": "../../@fullcalendar/interaction/index.js", "file": "@fullcalendar_interaction.js", "fileHash": "59130d7b", "needsInterop": false}, "@fullcalendar/react": {"src": "../../@fullcalendar/react/dist/index.js", "file": "@fullcalendar_react.js", "fileHash": "c86b8326", "needsInterop": false}, "@fullcalendar/timegrid": {"src": "../../@fullcalendar/timegrid/index.js", "file": "@fullcalendar_timegrid.js", "fileHash": "faf97465", "needsInterop": false}, "@stripe/react-stripe-js": {"src": "../../@stripe/react-stripe-js/dist/react-stripe.esm.mjs", "file": "@stripe_react-stripe-js.js", "fileHash": "7e49268a", "needsInterop": false}, "@stripe/stripe-js": {"src": "../../@stripe/stripe-js/lib/index.mjs", "file": "@stripe_stripe-js.js", "fileHash": "13c528f0", "needsInterop": false}, "@tiptap/extension-table": {"src": "../../@tiptap/extension-table/dist/index.js", "file": "@tiptap_extension-table.js", "fileHash": "b1b90afe", "needsInterop": false}, "@tiptap/extension-table-cell": {"src": "../../@tiptap/extension-table-cell/dist/index.js", "file": "@tiptap_extension-table-cell.js", "fileHash": "86231cbb", "needsInterop": false}, "@tiptap/extension-table-header": {"src": "../../@tiptap/extension-table-header/dist/index.js", "file": "@tiptap_extension-table-header.js", "fileHash": "b4875d86", "needsInterop": false}, "@tiptap/extension-table-row": {"src": "../../@tiptap/extension-table-row/dist/index.js", "file": "@tiptap_extension-table-row.js", "fileHash": "70b3adc4", "needsInterop": false}, "@tiptap/react": {"src": "../../@tiptap/react/dist/index.js", "file": "@tiptap_react.js", "fileHash": "2653c97c", "needsInterop": false}, "@tiptap/starter-kit": {"src": "../../@tiptap/starter-kit/dist/index.js", "file": "@tiptap_starter-kit.js", "fileHash": "c3015419", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "ff627159", "needsInterop": false}, "jwt-decode": {"src": "../../jwt-decode/build/esm/index.js", "file": "jwt-decode.js", "fileHash": "4c17eb22", "needsInterop": false}, "react-bootstrap": {"src": "../../react-bootstrap/esm/index.js", "file": "react-bootstrap.js", "fileHash": "cdeae865", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "c9990d4c", "needsInterop": true}, "react-router-bootstrap": {"src": "../../react-router-bootstrap/index.js", "file": "react-router-bootstrap.js", "fileHash": "dc8cb852", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "b5bc2416", "needsInterop": false}, "react-toastify": {"src": "../../react-toastify/dist/index.mjs", "file": "react-toastify.js", "fileHash": "abba073e", "needsInterop": false}}, "chunks": {"chunk-YJIB3BN6": {"file": "chunk-YJIB3BN6.js"}, "chunk-63ILDAJJ": {"file": "chunk-63ILDAJJ.js"}, "chunk-APVAXGYH": {"file": "chunk-APVAXGYH.js"}, "chunk-U3XDO3EQ": {"file": "chunk-U3XDO3EQ.js"}, "chunk-FDMQADGV": {"file": "chunk-FDMQADGV.js"}, "chunk-MIEWXLXB": {"file": "chunk-MIEWXLXB.js"}, "chunk-SS2ORZSQ": {"file": "chunk-SS2ORZSQ.js"}, "chunk-WZWH36PQ": {"file": "chunk-WZWH36PQ.js"}, "chunk-7DKZST3T": {"file": "chunk-7DKZST3T.js"}, "chunk-V4OQ3NZ2": {"file": "chunk-V4OQ3NZ2.js"}}}
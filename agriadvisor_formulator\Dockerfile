# Use a Python base image
FROM python:3.10-slim

# Set environment variables for Python
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# Set the working directory inside the container
WORKDIR /app

# Install system dependencies (like netcat for the startup script)
RUN apt-get update && apt-get install -y netcat-openbsd

# Copy and install Python dependencies
COPY requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt

# # Copy the startup script and make it executable
COPY ./entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

# Copy the rest of the application code into the container
COPY . /app/

# Expose the port the Django app will run on
EXPOSE 8000

# # Set the startup command
ENTRYPOINT ["/app/entrypoint.sh"]



# # Use a Python base image
# FROM python:3.10-slim

# # Set environment variables for Python
# ENV PYTHONDONTWRITEBYTECODE 1
# ENV PYTHONUNBUFFERED 1

# # Set the working directory inside the container
# WORKDIR /app

# # Install system dependencies (like netcat for the startup script)
# RUN apt-get update && apt-get install -y netcat-openbsd

# # Copy and install Python dependencies
# COPY requirements.txt /app/
# RUN pip install --no-cache-dir -r requirements.txt

# # # Copy the startup script and make it executable
# # COPY ./entrypoint.sh /app/entrypoint.sh
# # RUN chmod +x /app/entrypoint.sh

# # Copy the rest of the application code into the container
# COPY . /app/

# # Expose the port the Django app will run on
# EXPOSE 8000

# # # Set the startup command
# # ENTRYPOINT ["/app/entrypoint.sh"]


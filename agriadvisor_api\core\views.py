# core/views.py

from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from .permissions import IsAdminUser

class BaseTenantViewSet(viewsets.ModelViewSet):
    """
    A secure-by-default ViewSet for tenant-aware models.
    - Automatically filters querysets to the user's tenant.
    - Automatically assigns the user's tenant to new objects.
    - Defaults to requiring Admin permissions.
    """
    permission_classes = [IsAuthenticated, IsAdminUser]

    def get_queryset(self):
        # Get the original queryset from the subclass
        queryset = super().get_queryset()
        user = self.request.user
        # Securely filter by the user's tenant
        return queryset.filter(tenant=user.tenant)

    def perform_create(self, serializer):
        # Automatically assign the user's tenant when creating a new object
        serializer.save(tenant=self.request.user.tenant)




// src/services/availabilityService.js

import api from "./api";

export const getMyAvailabilityRules = () => api.get("/availability/rules/");
export const createAvailabilityRule = (data) => api.post("/availability/rules/", data);
export const deleteAvailabilityRule = (id) => api.delete(`/availability/rules/${id}/`);
export const checkAvailability = async (params) => {
  console.log("availabilityService: Checking availability with params:", params);
  try {
    const response = await api.get("/availability/check/", { params });
    console.log("availabilityService: Availability response:", response);
    return response;
  } catch (error) {
    console.error("availabilityService: Error checking availability:", error);
    throw error;
  }
};

{"version": 3, "sources": ["../../src/jsx-runtime.ts"], "sourcesContent": ["export type Attributes = Record<string, any>\n\nexport type DOMOutputSpecElement = 0 | Attributes | DOMOutputSpecArray\n/**\n * Better describes the output of a `renderHTML` function in prosemirror\n * @see https://prosemirror.net/docs/ref/#model.DOMOutputSpec\n */\nexport type DOMOutputSpecArray =\n  | [string]\n  | [string, Attributes]\n  | [string, 0]\n  | [string, Attributes, 0]\n  | [string, Attributes, DOMOutputSpecArray | 0]\n  | [string, DOMOutputSpecArray]\n\n// JSX types for Tiptap's JSX runtime\n// These types only apply when using @jsxImportSource @tiptap/core\n// eslint-disable-next-line @typescript-eslint/no-namespace\nexport namespace JSX {\n  export type Element = DOMOutputSpecArray\n  export interface IntrinsicElements {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    [key: string]: any\n  }\n  export interface ElementChildrenAttribute {\n    children: unknown\n  }\n}\n\nexport type JSXRenderer = (\n  tag: 'slot' | string | ((props?: Attributes) => DOMOutputSpecArray | DOMOutputSpecElement),\n  props?: Attributes,\n  ...children: JSXRenderer[]\n) => DOMOutputSpecArray | DOMOutputSpecElement\n\nexport function Fragment(props: { children: JSXRenderer[] }) {\n  return props.children\n}\n\nexport const h: JSXRenderer = (tag, attributes) => {\n  // Treat the slot tag as the Prosemirror hole to render content into\n  if (tag === 'slot') {\n    return 0\n  }\n\n  // If the tag is a function, call it with the props\n  if (tag instanceof Function) {\n    return tag(attributes)\n  }\n\n  const { children, ...rest } = attributes ?? {}\n\n  if (tag === 'svg') {\n    throw new Error('SVG elements are not supported in the JSX syntax, use the array syntax instead')\n  }\n\n  // Otherwise, return the tag, attributes, and children\n  return [tag, rest, children]\n}\n\n// See\n// https://esbuild.github.io/api/#jsx-import-source\n// https://www.typescriptlang.org/tsconfig/#jsxImportSource\n\nexport { h as createElement, h as jsx, h as jsxDEV, h as jsxs }\n"], "mappings": ";AAmCO,SAAS,SAAS,OAAoC;AAC3D,SAAO,MAAM;AACf;AAEO,IAAM,IAAiB,CAAC,KAAK,eAAe;AAEjD,MAAI,QAAQ,QAAQ;AAClB,WAAO;AAAA,EACT;AAGA,MAAI,eAAe,UAAU;AAC3B,WAAO,IAAI,UAAU;AAAA,EACvB;AAEA,QAAM,EAAE,UAAU,GAAG,KAAK,IAAI,kCAAc,CAAC;AAE7C,MAAI,QAAQ,OAAO;AACjB,UAAM,IAAI,MAAM,gFAAgF;AAAA,EAClG;AAGA,SAAO,CAAC,KAAK,MAAM,QAAQ;AAC7B;", "names": []}
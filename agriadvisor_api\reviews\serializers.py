# reviews/serializers.py

from rest_framework import serializers
from .models import Review
from accounts.serializers import CustomUserSerializer
from advisory.models import Booking

class ReviewSerializer(serializers.ModelSerializer):
    farmer = CustomUserSerializer(read_only=True)
    booking = serializers.PrimaryKeyRelatedField(queryset=Booking.objects.all())

    class Meta:
        model = Review
        fields = ['id', 'rating', 'comment', 'created_at', 'farmer', 'booking']
        read_only_fields = ['farmer', 'expert']

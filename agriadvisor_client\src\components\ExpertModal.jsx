// src/components/ExpertModal.jsx

import React, { useState, useEffect } from "react";
import { Modal, Button, Form, Row, Col } from "react-bootstrap";

const ExpertModal = ({ show, onHide, onSave, expert }) => {
  console.log("ExpertModal: Rendered with props", { show, expert });

  const [formData, setFormData] = useState({
    full_name: "",
    specialty: "",
    email: "",
    phone_number: "",
    bio: "",
  });

  const isEditing = expert !== null;

  useEffect(() => {
    console.log("ExpertModal: useEffect triggered", { expert, show, isEditing });
    if (isEditing) {
      console.log("ExpertModal: Setting form data for editing", expert);
      setFormData({
        full_name: `${expert.first_name || ''} ${expert.last_name || ''}`.trim() || expert.full_name || '',
        specialty: expert.profile?.specialty || "",
        email: expert.email || "",
        phone_number: expert.phone_number || "",
        bio: expert.profile?.bio || "",
      });
    } else {
      console.log("ExpertModal: Setting form data for new expert");
      setFormData({
        full_name: "",
        specialty: "",
        email: "",
        phone_number: "",
        bio: "",
      });
    }
  }, [expert, show, isEditing]);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("ExpertModal: Form submitted with data", formData);
    console.log("ExpertModal: Is editing?", isEditing);
    console.log("ExpertModal: Expert prop", expert);
    onSave(formData);
  };

  return (
    <Modal show={show} onHide={onHide} size="lg">
      <Form onSubmit={handleSubmit}>
        <Modal.Header closeButton>
          <Modal.Title>{isEditing ? "Edit Expert" : "Add New Expert"}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form.Group className="mb-3">
            <Form.Label>Full Name</Form.Label>
            <Form.Control type="text" name="full_name" value={formData.full_name} onChange={handleChange} required />
          </Form.Group>
          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Specialty</Form.Label>
                <Form.Control
                  type="text"
                  name="specialty"
                  value={formData.specialty}
                  onChange={handleChange}
                  placeholder="e.g., Animal Nutrition"
                  required
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Email</Form.Label>
                <Form.Control type="email" name="email" value={formData.email} onChange={handleChange} required />
              </Form.Group>
            </Col>
          </Row>
          <Form.Group className="mb-3">
            <Form.Label>Phone (Optional)</Form.Label>
            <Form.Control type="tel" name="phone_number" value={formData.phone_number} onChange={handleChange} />
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Bio (Optional)</Form.Label>
            <Form.Control as="textarea" rows={3} name="bio" value={formData.bio} onChange={handleChange} />
          </Form.Group>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={onHide}>
            Cancel
          </Button>
          <Button variant="primary" type="submit">
            Save Changes
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
};
export default ExpertModal;



# Generated by Django 5.2.5 on 2025-08-28 07:57

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('planning', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='enterprisetemplate',
            name='model_type',
            field=models.CharField(choices=[('generic', 'Generic Budget'), ('pig_production', 'Pig Production Model'), ('egg_production', 'Egg Production Model'), ('dairy_livestock', 'Dairy Livestock Model'), ('beef_livestock', 'Beef Livestock Model'), ('broiler_production', 'Broiler Production Model'), ('tilapia_production', 'Tilapia Production Model'), ('sheep_production', 'Sheep Production Model'), ('forage_production', 'Forage Production Model'), ('horticulture_crop', 'Horticultural Crop Model'), ('field_crop', 'Field Crop Model'), ('conservancy', 'Wildlife Conservancy Model'), ('plantation_crop', 'Plantation Crop Model'), ('irrigation_investment', 'Irrigation Investment Model')], default='generic', max_length=50),
        ),
        migrations.CreateModel(
            name='AgrotourismAssumptions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lodging_capacity_rooms', models.PositiveIntegerField(default=5)),
                ('target_annual_occupancy_percent', models.DecimalField(decimal_places=2, default=45.0, max_digits=5)),
                ('avg_revenue_per_guest_night', models.DecimalField(decimal_places=2, default=6000.0, max_digits=12)),
                ('avg_tours_per_year', models.PositiveIntegerField(default=500)),
                ('price_per_tour', models.DecimalField(decimal_places=2, default=500.0, max_digits=10)),
                ('annual_value_added_sales', models.DecimalField(decimal_places=2, default=1500000.0, max_digits=12)),
                ('cost_of_food_per_guest_day', models.DecimalField(decimal_places=2, default=1200.0, max_digits=10)),
                ('plan', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='agrotourism_assumptions', to='planning.enterpriseplan')),
            ],
        ),
        migrations.CreateModel(
            name='BeefLivestockAssumptions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('number_of_breeding_females', models.PositiveIntegerField(default=20)),
                ('birth_rate_percent', models.DecimalField(decimal_places=2, default=90.0, max_digits=5)),
                ('weaning_weight_kg', models.DecimalField(decimal_places=1, default=150.0, max_digits=5)),
                ('avg_daily_gain_g', models.DecimalField(decimal_places=1, default=800.0, max_digits=6)),
                ('sale_weight_kg', models.DecimalField(decimal_places=1, default=450.0, max_digits=6)),
                ('mortality_rate_percent', models.DecimalField(decimal_places=2, default=3.0, max_digits=5)),
                ('price_per_kg_live_weight', models.DecimalField(decimal_places=2, default=250.0, max_digits=10)),
                ('plan', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='beef_assumptions', to='planning.enterpriseplan')),
            ],
        ),
        migrations.CreateModel(
            name='BroilerProductionAssumptions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('batch_size', models.PositiveIntegerField(default=100)),
                ('growing_period_days', models.PositiveIntegerField(default=42)),
                ('target_sale_weight_kg', models.DecimalField(decimal_places=2, default=2.8, max_digits=4)),
                ('expected_mortality_percent', models.DecimalField(decimal_places=2, default=5.0, max_digits=5)),
                ('feed_conversion_ratio', models.DecimalField(decimal_places=2, default=1.8, max_digits=4)),
                ('sale_price_per_kg_live_weight', models.DecimalField(decimal_places=2, default=350.0, max_digits=10)),
                ('day_old_chick_price', models.DecimalField(decimal_places=2, default=110.0, max_digits=10)),
                ('avg_feed_price_per_bag', models.DecimalField(decimal_places=2, default=3900.0, max_digits=10)),
                ('feed_bag_weight_kg', models.DecimalField(decimal_places=1, default=70.0, max_digits=5)),
                ('plan', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='broiler_assumptions', to='planning.enterpriseplan')),
            ],
        ),
        migrations.CreateModel(
            name='ConservancyAssumptions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_land_area_acres', models.DecimalField(decimal_places=2, default=10000.0, max_digits=10)),
                ('lodge_rooms', models.PositiveIntegerField(default=5)),
                ('target_occupancy_percent', models.DecimalField(decimal_places=2, default=60.0, max_digits=5)),
                ('price_per_night', models.DecimalField(decimal_places=2, default=250.0, max_digits=10)),
                ('trophy_hunting_packages_sold', models.PositiveIntegerField(default=5)),
                ('avg_revenue_per_package', models.DecimalField(decimal_places=2, default=15000.0, max_digits=12)),
                ('live_game_offtake_au', models.PositiveIntegerField(default=50)),
                ('avg_price_per_au', models.DecimalField(decimal_places=2, default=400.0, max_digits=10)),
                ('plan', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='conservancy_assumptions', to='planning.enterpriseplan')),
            ],
        ),
        migrations.CreateModel(
            name='DairyLivestockAssumptions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('number_of_milking_cows', models.PositiveIntegerField(default=10)),
                ('avg_milk_yield_liters_per_day', models.DecimalField(decimal_places=1, default=15.0, max_digits=5)),
                ('lactation_days', models.PositiveIntegerField(default=305)),
                ('calving_interval_months', models.DecimalField(decimal_places=1, default=13.0, max_digits=4)),
                ('calf_mortality_percent', models.DecimalField(decimal_places=2, default=5.0, max_digits=5)),
                ('price_per_liter_milk', models.DecimalField(decimal_places=2, default=50.0, max_digits=10)),
                ('cull_cow_price_per_kg', models.DecimalField(decimal_places=2, default=120.0, max_digits=10)),
                ('heifer_sale_price', models.DecimalField(decimal_places=2, default=40000.0, max_digits=10)),
                ('concentrate_price_per_bag', models.DecimalField(decimal_places=2, default=2500.0, max_digits=10)),
                ('feed_bag_weight_kg', models.DecimalField(decimal_places=1, default=70.0, max_digits=5)),
                ('plan', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='dairy_assumptions', to='planning.enterpriseplan')),
            ],
        ),
        migrations.CreateModel(
            name='EggProductionAssumptions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('flock_size', models.PositiveIntegerField(default=1000)),
                ('rearing_period_weeks', models.PositiveIntegerField(default=18)),
                ('laying_period_weeks', models.PositiveIntegerField(default=52)),
                ('avg_hen_housed_production_percent', models.DecimalField(decimal_places=2, default=85.0, max_digits=5)),
                ('avg_egg_weight_g', models.DecimalField(decimal_places=1, default=62.0, max_digits=5)),
                ('mortality_during_lay_percent', models.DecimalField(decimal_places=2, default=5.0, max_digits=5)),
                ('avg_feed_intake_g_per_bird_day', models.DecimalField(decimal_places=1, default=120.0, max_digits=6)),
                ('sale_price_per_tray', models.DecimalField(decimal_places=2, default=300.0, max_digits=10)),
                ('day_old_chick_price', models.DecimalField(decimal_places=2, default=120.0, max_digits=10)),
                ('starter_grower_feed_price_per_bag', models.DecimalField(decimal_places=2, default=3800.0, max_digits=10)),
                ('layer_mash_price_per_bag', models.DecimalField(decimal_places=2, default=3500.0, max_digits=10)),
                ('spent_hen_price', models.DecimalField(decimal_places=2, default=200.0, max_digits=10)),
                ('feed_bag_weight_kg', models.DecimalField(decimal_places=1, default=70.0, max_digits=5)),
                ('plan', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='egg_assumptions', to='planning.enterpriseplan')),
            ],
        ),
        migrations.CreateModel(
            name='FieldCropAssumptions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_size_hectares', models.DecimalField(decimal_places=2, default=1.0, max_digits=10)),
                ('expected_yield_kg_per_hectare', models.DecimalField(decimal_places=2, default=2700.0, max_digits=10)),
                ('sale_price_per_kg', models.DecimalField(decimal_places=2, default=27.78, max_digits=10)),
                ('plan', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='field_crop_assumptions', to='planning.enterpriseplan')),
            ],
        ),
        migrations.CreateModel(
            name='ForageProductionAssumptions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_size_hectares', models.DecimalField(decimal_places=2, default=1.0, max_digits=10)),
                ('productive_lifespan_years', models.PositiveIntegerField(default=4)),
                ('cuts_per_year', models.PositiveIntegerField(default=6)),
                ('yield_bales_per_cut', models.DecimalField(decimal_places=2, default=35.0, max_digits=10)),
                ('sale_price_per_bale', models.DecimalField(decimal_places=2, default=250.0, max_digits=10)),
                ('plan', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='forage_assumptions', to='planning.enterpriseplan')),
            ],
        ),
        migrations.CreateModel(
            name='HorticultureCropAssumptions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_size_acres', models.DecimalField(decimal_places=2, default=1.0, max_digits=10)),
                ('crop_cycle_days', models.PositiveIntegerField(default=120)),
                ('planting_density_per_acre', models.PositiveIntegerField(default=10000)),
                ('expected_yield_kg_per_acre', models.DecimalField(decimal_places=2, default=25000.0, max_digits=10)),
                ('percent_grade_a', models.DecimalField(decimal_places=2, default=60.0, max_digits=5)),
                ('percent_grade_b', models.DecimalField(decimal_places=2, default=25.0, max_digits=5)),
                ('percent_grade_c', models.DecimalField(decimal_places=2, default=15.0, max_digits=5)),
                ('sale_price_grade_a_per_kg', models.DecimalField(decimal_places=2, default=50.0, max_digits=10)),
                ('sale_price_grade_b_per_kg', models.DecimalField(decimal_places=2, default=30.0, max_digits=10)),
                ('sale_price_grade_c_per_kg', models.DecimalField(decimal_places=2, default=10.0, max_digits=10)),
                ('seedling_price_each', models.DecimalField(decimal_places=2, default=15.0, max_digits=10)),
                ('market_commission_percent', models.DecimalField(decimal_places=2, default=10.0, max_digits=5)),
                ('plan', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='horticulture_assumptions', to='planning.enterpriseplan')),
            ],
        ),
        migrations.CreateModel(
            name='IrrigationAssumptions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('area_to_irrigate_acres', models.DecimalField(decimal_places=2, default=5.0, max_digits=10)),
                ('system_lifespan_years', models.PositiveIntegerField(default=7)),
                ('rainfed_crop_name', models.CharField(default='Maize', max_length=100)),
                ('rainfed_yield_units_per_acre', models.DecimalField(decimal_places=2, default=15.0, max_digits=10)),
                ('rainfed_yield_unit_name', models.CharField(default='90kg bag', max_length=50)),
                ('rainfed_price_per_unit', models.DecimalField(decimal_places=2, default=2500.0, max_digits=10)),
                ('irrigated_crop_name', models.CharField(default='Tomatoes', max_length=100)),
                ('irrigated_yield_units_per_acre', models.DecimalField(decimal_places=2, default=220.0, max_digits=10)),
                ('irrigated_yield_unit_name', models.CharField(default='50kg crate', max_length=50)),
                ('irrigated_price_per_unit', models.DecimalField(decimal_places=2, default=2000.0, max_digits=10)),
                ('discount_rate_percent', models.DecimalField(decimal_places=2, default=10.0, max_digits=5)),
                ('plan', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='irrigation_assumptions', to='planning.enterpriseplan')),
            ],
        ),
        migrations.CreateModel(
            name='PigProductionAssumptions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('number_of_sows', models.PositiveIntegerField(default=10)),
                ('farrowing_rate_percent', models.DecimalField(decimal_places=2, default=85.0, max_digits=5)),
                ('litters_per_sow_per_year', models.DecimalField(decimal_places=1, default=2.4, max_digits=4)),
                ('pigs_born_alive_per_litter', models.DecimalField(decimal_places=1, default=13.0, max_digits=4)),
                ('pre_weaning_mortality_percent', models.DecimalField(decimal_places=2, default=12.0, max_digits=5)),
                ('post_weaning_mortality_percent', models.DecimalField(decimal_places=2, default=4.0, max_digits=5)),
                ('sale_weight_kg', models.DecimalField(decimal_places=1, default=90.0, max_digits=5)),
                ('avg_daily_gain_g', models.DecimalField(decimal_places=1, default=600.0, max_digits=6)),
                ('feed_conversion_ratio', models.DecimalField(decimal_places=2, default=2.8, max_digits=4)),
                ('sale_price_per_kg', models.DecimalField(decimal_places=2, default=300.0, max_digits=10)),
                ('sow_feed_price_per_bag', models.DecimalField(decimal_places=2, default=3500.0, max_digits=10)),
                ('weaner_feed_price_per_bag', models.DecimalField(decimal_places=2, default=4200.0, max_digits=10)),
                ('finisher_feed_price_per_bag', models.DecimalField(decimal_places=2, default=3800.0, max_digits=10)),
                ('feed_bag_weight_kg', models.DecimalField(decimal_places=1, default=70.0, max_digits=5)),
                ('plan', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='pig_assumptions', to='planning.enterpriseplan')),
            ],
        ),
        migrations.CreateModel(
            name='PlantationCropAssumptions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_area_hectares', models.DecimalField(decimal_places=2, default=50.0, max_digits=10)),
                ('gestation_period_years', models.PositiveIntegerField(default=7)),
                ('expected_mai_m3_ha_yr', models.DecimalField(decimal_places=2, default=25.0, max_digits=5, verbose_name='Expected Mean Annual Increment (m³/Ha/Year)')),
                ('stumpage_price_m3_y7', models.DecimalField(decimal_places=2, default=4000.0, max_digits=10, verbose_name='Stumpage Price per m³ at Harvest (Year 7)')),
                ('plan', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='plantation_assumptions', to='planning.enterpriseplan')),
            ],
        ),
        migrations.CreateModel(
            name='SheepProductionAssumptions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('number_of_ewes', models.PositiveIntegerField(default=100)),
                ('replacement_rate_percent', models.DecimalField(decimal_places=2, default=20.0, max_digits=5)),
                ('rams_required', models.PositiveIntegerField(default=3)),
                ('lambing_percent', models.DecimalField(decimal_places=1, default=130.0, max_digits=5)),
                ('pre_weaning_lamb_mortality_percent', models.DecimalField(decimal_places=2, default=10.0, max_digits=5)),
                ('avg_sale_weight_lambs_kg', models.DecimalField(decimal_places=1, default=35.0, max_digits=5)),
                ('avg_sale_weight_cull_ewes_kg', models.DecimalField(decimal_places=1, default=45.0, max_digits=5)),
                ('sale_price_lambs_per_kg', models.DecimalField(decimal_places=2, default=400.0, max_digits=10)),
                ('sale_price_cull_ewes_per_kg', models.DecimalField(decimal_places=2, default=300.0, max_digits=10)),
                ('sale_price_wool_per_kg', models.DecimalField(blank=True, decimal_places=2, default=100.0, max_digits=10, null=True)),
                ('plan', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='sheep_assumptions', to='planning.enterpriseplan')),
            ],
        ),
        migrations.CreateModel(
            name='TilapiaProductionAssumptions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('pond_size_m2', models.DecimalField(decimal_places=2, default=100.0, max_digits=10)),
                ('stocking_density_per_m2', models.PositiveIntegerField(default=3)),
                ('production_cycle_months', models.PositiveIntegerField(default=6)),
                ('target_harvest_size_g', models.PositiveIntegerField(default=500)),
                ('expected_survival_rate_percent', models.DecimalField(decimal_places=2, default=85.0, max_digits=5)),
                ('feed_conversion_ratio', models.DecimalField(decimal_places=2, default=1.7, max_digits=4)),
                ('sale_price_per_kg', models.DecimalField(decimal_places=2, default=400.0, max_digits=10)),
                ('fingerling_price_each', models.DecimalField(decimal_places=2, default=15.0, max_digits=10)),
                ('avg_feed_price_per_kg', models.DecimalField(decimal_places=2, default=120.0, max_digits=10)),
                ('plan', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='tilapia_assumptions', to='planning.enterpriseplan')),
            ],
        ),
    ]

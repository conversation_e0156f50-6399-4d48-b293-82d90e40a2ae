// src/pages/MyBudgetsPage.jsx

import React, { useState, useEffect, useMemo } from "react";
import { Container, Row, Col, Card, ListGroup, Table, Spinner, Alert } from "react-bootstrap";
import { getMyBudgets } from "../services/budgetService";

const formatCurrency = (number) => {
  const num = parseFloat(number || 0);
  return new Intl.NumberFormat("en-GB", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })
    .format(num)
    .replace(/,/g, " ");
};

const MyBudgetsPage = () => {
  const [budgets, setBudgets] = useState([]);
  const [selectedBudget, setSelectedBudget] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(''); // <-- THIS WAS A MISSING PIECE

  useEffect(() => {
    const fetchBudgets = async () => {
      try {
        setLoading(true);
        const data = await getMyBudgets();
        setBudgets(data);
        if (data && data.length > 0) {
          setSelectedBudget(data[0]);
        }
      } catch (error) {
        console.error("Failed to fetch budgets", error);
        setError("Could not load your budgets. Please try again later.");
      } finally {
        setLoading(false);
      }
    };
    fetchBudgets();
  }, []);

  if (loading) return <Container className="text-center mt-5"><Spinner animation="border" /></Container>;
  if (error) return <Container className="mt-4"><Alert variant="danger">{error}</Alert></Container>;

  return (
    <Container>
      <h1 className="mb-4">My Farm Budgets</h1>
      {budgets.length === 0 ? (
        <Alert variant="info">No budgets have been created for you yet.</Alert>
      ) : (
        <Row>
          <Col md={4} className="mb-3">
            <Card>
              <Card.Header as="h4">Budget Plans</Card.Header>
              <ListGroup variant="flush">
                {budgets.map((budget) => (
                  <ListGroup.Item
                    key={budget.id}
                    action
                    active={selectedBudget?.id === budget.id}
                    onClick={() => setSelectedBudget(budget)}
                  >
                    {budget.name}
                  </ListGroup.Item>
                ))}
              </ListGroup>
            </Card>
          </Col>
          <Col md={8}>
            {selectedBudget ? <BudgetWorksheet budget={selectedBudget} /> : <p>Select a budget to view.</p>}
          </Col>
        </Row>
      )}
    </Container>
  );
};

// --- Child Components with Full Logic ---

const BudgetWorksheet = ({ budget }) => {
  const incomeItems = budget.items.filter((i) => i.item_type === "income");
  const variableCostItems = budget.items.filter((i) => i.item_type === "variable");
  const fixedCostItems = budget.items.filter((i) => i.item_type === "fixed");

  // --- THIS IS THE MISSING CALCULATION LOGIC ---
  const totals = useMemo(() => {
    const calculate = (items) => items.reduce((acc, item) => {
        acc.proj += parseFloat(item.projected_amount || 0);
        acc.act += parseFloat(item.actual_amount || 0);
        return acc;
    }, { proj: 0, act: 0 });

    const income = calculate(incomeItems);
    const variable = calculate(variableCostItems);
    const fixed = calculate(fixedCostItems);
    
    return {
        projIncome: income.proj, actIncome: income.act,
        projVar: variable.proj, actVar: variable.act,
        projFix: fixed.proj, actFix: fixed.act,
    };
  }, [budget.items]);
  // --- END OF MISSING LOGIC ---

  return (
    <div>
      <BudgetSection title="Sources of Income" items={incomeItems} />
      <BudgetSection title="Variable Costs" items={variableCostItems} />
      <BudgetSection title="Fixed Costs" items={fixedCostItems} />
      <Summary totals={totals} />
    </div>
  );
};

const BudgetSection = ({ title, items }) => (
  <Card className="mb-3">
    <Card.Header as="h5">{title}</Card.Header>
    <Card.Body>
      <div className="table-responsive">
        <Table striped bordered size="sm">
          <thead>
            <tr>
              <th>Category</th>
              <th>Description</th>
              <th className="text-end">Projected</th>
              <th className="text-end">Actual</th>
            </tr>
          </thead>
          <tbody>
            {items.map((item) => (
              <tr key={item.id}>
                <td>{item.category}</td>
                <td>{item.description}</td>
                <td className="text-end">{formatCurrency(item.projected_amount)}</td>
                <td className="text-end">{formatCurrency(item.actual_amount)}</td>
              </tr>
            ))}
            {items.length === 0 && (
              <tr><td colSpan="4" className="text-center">No items.</td></tr>
            )}
          </tbody>
        </Table>
      </div>
    </Card.Body>
  </Card>
);

// --- THIS IS THE MISSING SUMMARY COMPONENT ---
const Summary = ({ totals }) => (
    <Card bg="light">
        <Card.Header as="h5">Budget Summary</Card.Header>
        <Card.Body>
            <Row>
                <Col><strong>Category</strong></Col>
                <Col className="text-end"><strong>Projected</strong></Col>
                <Col className="text-end"><strong>Actual</strong></Col>
            </Row>
            <hr className="my-2"/>
            <Row>
                <Col>Total Income:</Col>
                <Col className="text-end">{formatCurrency(totals.projIncome)}</Col>
                <Col className="text-end">{formatCurrency(totals.actIncome)}</Col>
            </Row>
             <Row>
                <Col>Total Variable Costs:</Col>
                <Col className="text-end">({formatCurrency(totals.projVar)})</Col>
                <Col className="text-end">({formatCurrency(totals.actVar)})</Col>
            </Row>
             <Row>
                <Col>Total Fixed Costs:</Col>
                <Col className="text-end">({formatCurrency(totals.projFix)})</Col>
                <Col className="text-end">({formatCurrency(totals.actFix)})</Col>
            </Row>
            <hr/>
            <Row className="fw-bold text-success">
                <Col>Net Profit / (Loss):</Col>
                <Col className="text-end">{formatCurrency(totals.projIncome - totals.projVar - totals.projFix)}</Col>
                <Col className="text-end">{formatCurrency(totals.actIncome - totals.actVar - totals.actFix)}</Col>
            </Row>
        </Card.Body>
    </Card>
);
// --- END OF MISSING COMPONENT ---

export default MyBudgetsPage;



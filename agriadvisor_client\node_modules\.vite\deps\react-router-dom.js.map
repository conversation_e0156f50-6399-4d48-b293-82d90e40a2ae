{"version": 3, "sources": ["../../react-router-dom/dist/index.mjs"], "sourcesContent": ["/**\n * react-router-dom v7.8.2\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\n\n// index.ts\nimport { HydratedRouter, RouterProvider } from \"react-router/dom\";\nexport * from \"react-router\";\nexport {\n  HydratedRouter,\n  RouterProvider\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA;AACA;", "names": []}
// src/pages/ArticleManagementPage.jsx
import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Al<PERSON> } from "react-bootstrap";
import { Link } from "react-router-dom";

const ArticleManagementPage = () => {
  return (
    <Container fluid>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h3>Manage Articles</h3>
        <Button as={Link} to="/admin/kb-articles/editor/new">
          <i className="bi bi-plus-lg me-2"></i>New Article
        </Button>
      </div>
      <Alert variant="info">Article list will be displayed here.</Alert>
    </Container>
  );
};
export default ArticleManagementPage;

// src/pages/ArticleManagementPage.jsx
import React, { useState, useEffect } from "react";
import {
  Contain<PERSON>,
  <PERSON><PERSON>,
  Alert,
  Table,
  Badge,
  Spinner,
  Form,
  Row,
  Col,
  Card,
  Dropdown,
  Modal
} from "react-bootstrap";
import { Link } from "react-router-dom";
import { getAdminArticles, deleteArticle } from "../services/knowledgeBaseService";

const ArticleManagementPage = () => {
  const [articles, setArticles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [articleToDelete, setArticleToDelete] = useState(null);
  const [deleting, setDeleting] = useState(false);

  const fetchArticles = async () => {
    try {
      setLoading(true);
      setError("");
      const response = await getAdminArticles();
      setArticles(response.data || []);
    } catch (err) {
      console.error("Error fetching articles:", err);
      setError("Failed to load articles. Please try again.");
      setArticles([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchArticles();
  }, []);

  const handleDeleteClick = (article) => {
    setArticleToDelete(article);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!articleToDelete) return;

    try {
      setDeleting(true);
      await deleteArticle(articleToDelete.id);
      setArticles(articles.filter(a => a.id !== articleToDelete.id));
      setShowDeleteModal(false);
      setArticleToDelete(null);
    } catch (err) {
      console.error("Error deleting article:", err);
      setError("Failed to delete article. Please try again.");
    } finally {
      setDeleting(false);
    }
  };

  const getStatusBadge = (status) => {
    const variants = {
      published: "success",
      draft: "warning",
      archived: "secondary"
    };
    return <Badge bg={variants[status] || "secondary"}>{status}</Badge>;
  };

  const filteredArticles = articles.filter(article => {
    const matchesSearch = article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         article.category_name?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || article.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <Container fluid className="d-flex justify-content-center align-items-center" style={{ minHeight: "400px" }}>
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading articles...</span>
        </Spinner>
      </Container>
    );
  }

  return (
    <Container fluid>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h3 className="mb-1">Manage Articles</h3>
          <p className="text-muted mb-0">Create and manage knowledge base articles</p>
        </div>
        <Button as={Link} to="/admin/kb-articles/editor/new" variant="primary">
          <i className="bi bi-plus-lg me-2"></i>New Article
        </Button>
      </div>

      {error && (
        <Alert variant="danger" className="mb-3" onClose={() => setError("")} dismissible>
          <i className="bi bi-exclamation-circle me-2"></i>
          {error}
        </Alert>
      )}

      <Card className="mb-4">
        <Card.Body>
          <Row className="g-3">
            <Col md={6}>
              <Form.Group>
                <Form.Label>Search Articles</Form.Label>
                <Form.Control
                  type="text"
                  placeholder="Search by title or category..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </Form.Group>
            </Col>
            <Col md={3}>
              <Form.Group>
                <Form.Label>Filter by Status</Form.Label>
                <Form.Select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <option value="all">All Status</option>
                  <option value="published">Published</option>
                  <option value="draft">Draft</option>
                  <option value="archived">Archived</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={3} className="d-flex align-items-end">
              <Button variant="outline-secondary" onClick={fetchArticles} className="w-100">
                <i className="bi bi-arrow-clockwise me-2"></i>Refresh
              </Button>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {filteredArticles.length === 0 ? (
        <Card>
          <Card.Body className="text-center py-5">
            <i className="bi bi-file-text fs-1 text-muted mb-3"></i>
            <h5 className="text-muted">No articles found</h5>
            <p className="text-muted mb-3">
              {articles.length === 0
                ? "Get started by creating your first article."
                : "No articles match your current filters."
              }
            </p>
            {articles.length === 0 && (
              <Button as={Link} to="/admin/kb-articles/editor/new" variant="primary">
                <i className="bi bi-plus-lg me-2"></i>Create First Article
              </Button>
            )}
          </Card.Body>
        </Card>
      ) : (
        <Card>
          <Card.Body className="p-0">
            <Table responsive hover className="mb-0">
              <thead className="table-light">
                <tr>
                  <th>Title</th>
                  <th>Category</th>
                  <th>Status</th>
                  <th>Featured</th>
                  <th>Views</th>
                  <th>Created</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredArticles.map((article) => (
                  <tr key={article.id}>
                    <td>
                      <div>
                        <strong>{article.title}</strong>
                        {article.meta_description && (
                          <div className="text-muted small">{article.meta_description}</div>
                        )}
                      </div>
                    </td>
                    <td>
                      <Badge bg="light" text="dark">{article.category_name || "Uncategorized"}</Badge>
                    </td>
                    <td>{getStatusBadge(article.status)}</td>
                    <td>
                      {article.is_featured && (
                        <Badge bg="warning" text="dark">
                          <i className="bi bi-star-fill me-1"></i>Featured
                        </Badge>
                      )}
                    </td>
                    <td>{article.view_count || 0}</td>
                    <td>
                      <small className="text-muted">
                        {new Date(article.created_at).toLocaleDateString()}
                      </small>
                    </td>
                    <td>
                      <Dropdown>
                        <Dropdown.Toggle variant="outline-secondary" size="sm">
                          <i className="bi bi-three-dots"></i>
                        </Dropdown.Toggle>
                        <Dropdown.Menu>
                          <Dropdown.Item as={Link} to={`/admin/kb-articles/editor/${article.id}`}>
                            <i className="bi bi-pencil me-2"></i>Edit
                          </Dropdown.Item>
                          <Dropdown.Item href={`/kb/articles/${article.slug}`} target="_blank">
                            <i className="bi bi-eye me-2"></i>View
                          </Dropdown.Item>
                          <Dropdown.Divider />
                          <Dropdown.Item
                            className="text-danger"
                            onClick={() => handleDeleteClick(article)}
                          >
                            <i className="bi bi-trash me-2"></i>Delete
                          </Dropdown.Item>
                        </Dropdown.Menu>
                      </Dropdown>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </Card.Body>
        </Card>
      )}

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Confirm Delete</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>Are you sure you want to delete the article <strong>"{articleToDelete?.title}"</strong>?</p>
          <p className="text-muted">This action cannot be undone.</p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)} disabled={deleting}>
            Cancel
          </Button>
          <Button variant="danger" onClick={handleDeleteConfirm} disabled={deleting}>
            {deleting ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Deleting...
              </>
            ) : (
              <>
                <i className="bi bi-trash me-2"></i>Delete Article
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default ArticleManagementPage;

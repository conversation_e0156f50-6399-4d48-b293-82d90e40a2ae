# planning/urls.py

from django.urls import path, include
from rest_framework_nested import routers
from .views import (
    EnterprisePlanViewSet, PlanItemViewSet, EnterpriseTemplateViewSet, 
    PlanAssumptionViewSet, PartialBudgetViewSet, PartialBudgetItemViewSet
)

# Main router for top-level resources
router = routers.SimpleRouter()
router.register(r'templates', EnterpriseTemplateViewSet, basename='template')
router.register(r'plans', EnterprisePlanViewSet, basename='plan')
router.register(r'partial-budgets', PartialBudgetViewSet, basename='partial-budget') # <-- Make it top-level
router.register(r'partial-budget-items', PartialBudgetItemViewSet, basename='partial-budget-item') # <-- Make it top-level
router.register(r'items', PlanItemViewSet, basename='plan-item')


# Nested router specifically for assumptions under a plan
plans_router = routers.NestedSimpleRouter(router, r'plans', lookup='plan')
plans_router.register(r'assumptions', PlanAssumptionViewSet, basename='plan-assumptions')

# Combine all the URL patterns
urlpatterns = [
    path('', include(router.urls)),
    path('', include(plans_router.urls)),
]














# # planning/urls.py

# from django.urls import path
# from rest_framework.routers import DefaultRouter
# from .views import (
#     EnterprisePlanViewSet, PlanItemViewSet, EnterpriseTemplateViewSet, 
#     FarmerEnterprisePlanViewSet, PartialBudgetItemViewSet, PartialBudgetViewSet # Make sure this is imported
# )

# # The router will handle the EXPERT's endpoints
# router = DefaultRouter()
# router.register(r'templates', EnterpriseTemplateViewSet, basename='template')
# router.register(r'plans', EnterprisePlanViewSet, basename='plan')
# router.register(r'items', PlanItemViewSet, basename='plan-item')

# router.register(r'partial-budgets', PartialBudgetViewSet, basename='partial-budget')
# router.register(r'partial-budget-items', PartialBudgetItemViewSet, basename='partial-budget-item')

# # We will define the FARMER's endpoint manually to be sure
# urlpatterns = router.urls + [
#     path(
#         'my-plans/', 
#         FarmerEnterprisePlanViewSet.as_view({'get': 'list'}), 
#         name='my-plan-list'
#     ),
# ]



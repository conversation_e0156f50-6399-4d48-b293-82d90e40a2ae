// src/App.jsx

import React from "react";
import { BrowserRouter as Router, Routes, Route, Navigate, useParams } from "react-router-dom";
import useAuth from "./hooks/useAuth";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css"; // Import toastify CSS

// Layouts
import AdminLayout from "./layouts/AdminLayout";
import FarmerLayout from "./layouts/FarmerLayout";
import ExpertLayout from "./layouts/ExpertLayout";

// --- Import ALL your page components ---
// Public
import LoginPage from "./pages/LoginPage";
import RegistrationPage from "./pages/RegistrationPage";
import ForgotPasswordPage from "./pages/ForgotPasswordPage";
import ResetPasswordPage from "./pages/ResetPasswordPage";

// Admin
import DashboardPage from "./pages/DashboardPage";
import ServicesPage from "./pages/ServicesPage";
import ExpertsPage from "./pages/ExpertsPage";
import BookingsPage from "./pages/BookingsPage";
import FarmersPage from "./pages/FarmersPage";
import AnnouncementsPage from "./pages/AnnouncementsPage";
import CategoryManagementPage from "./pages/CategoryManagementPage";
import ArticleManagementPage from "./pages/ArticleManagementPage";
import ArticleEditorPage from "./pages/ArticleEditorPage";

// Farmer
import FarmerDashboardPage from "./pages/FarmerDashboardPage";
import BrowseServicesPage from "./pages/BrowseServicesPage";
import FarmerBookingsPage from "./pages/FarmerBookingsPage";
import NewBookingPage from "./pages/NewBookingPage";
import ConsultationFormPage from "./pages/PreConsultationFormPage";
import ReportViewerPage from "./pages/ReportViewerPage";

// Expert
import ExpertDashboardPage from "./pages/ExpertDashboardPage";
import PayoutsPage from "./pages/PayoutsPage";
import MyAvailabilityPage from "./pages/MyAvailabilityPage";
import EnterprisePlanningPage from "./pages/EnterprisePlanningPage";
import MyFarmersPage from "./pages/MyFarmersPage";
import PartialBudgetsPage from "./pages/PartialBudgetsPage";
import ReportEditorPage from "./pages/ReportEditorPage";

// Shared
import ProfilePage from "./pages/ProfilePage";
import LiveSessionPage from "./pages/LiveSessionPage";

// Router
import ProtectedRoute from "./router/ProtectedRoute";

// --- Placeholder for pages you imported but might not have created yet ---
const MyActionPlansPage = () => <h1>My Action Plans</h1>;
const MyEnterprisePlansPage = () => <h1>My Enterprise Plans</h1>;
const MyFarmPage = () => <h1>My Farm</h1>;
const FieldsPage = () => <h1>Fields Page</h1>;
const CropPlantingsPage = () => <h1>Crop Plantings</h1>;
const LivestockPage = () => <h1>Livestock Page</h1>;
const LivestockTypesPage = () => <h1>Livestock Types</h1>;
const LivestockBatchesPage = () => <h1>Livestock Batches</h1>;
const ActionPlansPage = () => <h1>Expert Action Plans</h1>;
const EnterpriseWorksheetPage = () => <h1>Enterprise Worksheet</h1>;
const FarmerDetailViewPage = () => {
  const { farmerId } = useParams();
  return <h1>Dashboard for Farmer ID: {farmerId}</h1>;
};

// --- Helper Components for Routing ---
const PublicRoute = ({ children }) => {
  const { user } = useAuth();

  // If user is already logged in, redirect to their dashboard
  if (user) {
    if (user.role === 'admin') return <Navigate to="/admin/dashboard" replace />;
    if (user.role === 'expert') return <Navigate to="/expert/dashboard" replace />;
    if (user.role === 'farmer') return <Navigate to="/farmer/dashboard" replace />;
  }

  // If not logged in, show the public page
  return children;
};

const RootRedirector = () => {
  const { user } = useAuth();

  // If user is logged in, redirect to their dashboard
  if (user) {
    if (user.role === 'admin') return <Navigate to="/admin/dashboard" replace />;
    if (user.role === 'expert') return <Navigate to="/expert/dashboard" replace />;
    if (user.role === 'farmer') return <Navigate to="/farmer/dashboard" replace />;
  }

  // If not logged in, redirect to login
  return <Navigate to="/login" replace />;
};

function App() {
  const { loading } = useAuth();

  if (loading) return <div style={{ textAlign: "center", marginTop: "5rem" }}>Loading Application...</div>;

  return (
    <Router>
      <ToastContainer position="top-right" autoClose={5000} hideProgressBar={false} />
      <Routes>
        {/* --- Public Routes --- */}
        <Route
          path="/login"
          element={
            <PublicRoute>
              <LoginPage />
            </PublicRoute>
          }
        />
        <Route
          path="/register"
          element={
            <PublicRoute>
              <RegistrationPage />
            </PublicRoute>
          }
        />
        <Route
          path="/forgot-password"
          element={
            <PublicRoute>
              <ForgotPasswordPage />
            </PublicRoute>
          }
        />
        <Route
          path="/reset-password"
          element={
            <PublicRoute>
              <ResetPasswordPage />
            </PublicRoute>
          }
        />

        {/* --- Admin Protected Routes --- */}
        <Route element={<ProtectedRoute allowedRoles={["admin"]} />}>
          <Route path="/admin" element={<AdminLayout />}>
            <Route path="dashboard" element={<DashboardPage />} />
            <Route path="experts" element={<ExpertsPage />} />
            <Route path="services" element={<ServicesPage />} />
            <Route path="bookings" element={<BookingsPage />} />
            <Route path="farmers" element={<FarmersPage />} />
            <Route path="announcements" element={<AnnouncementsPage />} />
            <Route path="profile" element={<ProfilePage />} />
            <Route path="kb-categories" element={<CategoryManagementPage />} />
            <Route path="kb-articles" element={<ArticleManagementPage />} />
            <Route path="kb-articles/editor" element={<ArticleEditorPage />} />
            <Route path="kb-articles/editor/:articleId" element={<ArticleEditorPage />} />
          </Route>
        </Route>

        {/* --- Farmer Protected Routes --- */}
        <Route element={<ProtectedRoute allowedRoles={["farmer"]} />}>
          {/* Routes WITH the main FarmerLayout */}
          <Route path="/farmer" element={<FarmerLayout />}>
            <Route path="dashboard" element={<FarmerDashboardPage />} />
            <Route path="profile" element={<ProfilePage />} />
            <Route path="my-action-plans" element={<MyActionPlansPage />} />
            <Route path="my-enterprise-plans" element={<MyEnterprisePlansPage />} />
            <Route path="my-farm" element={<MyFarmPage />} />
            <Route path="my-farm/fields" element={<FieldsPage />} />
            <Route path="my-farm/crops" element={<CropPlantingsPage />} />
            <Route path="my-farm/livestock" element={<LivestockPage />} />
            <Route path="my-farm/livestock/types" element={<LivestockTypesPage />} />
            <Route path="my-farm/livestock/batches" element={<LivestockBatchesPage />} />
          </Route>

          {/* Farmer routes WITHOUT the main layout */}
          <Route path="/browse-services" element={<BrowseServicesPage />} />
          <Route path="/my-bookings" element={<FarmerBookingsPage />} />
          <Route path="/new-booking/:serviceId" element={<NewBookingPage />} />
          <Route path="/booking/:bookingId/pre-consultation-form" element={<ConsultationFormPage />} />
          <Route path="/booking/:bookingId/report" element={<ReportViewerPage />} />
        </Route>

        {/* --- Expert Protected Routes --- */}
        <Route element={<ProtectedRoute allowedRoles={["expert"]} />}>
          {/* Routes WITH the main ExpertLayout */}
          <Route path="/expert" element={<ExpertLayout />}>
            <Route path="dashboard" element={<ExpertDashboardPage />} />
            <Route path="my-farmers" element={<MyFarmersPage />} />
            <Route path="planning" element={<EnterprisePlanningPage />} />
            <Route path="partial-budgets" element={<PartialBudgetsPage />} />
            <Route path="action-plans" element={<ActionPlansPage />} />
            <Route path="availability" element={<MyAvailabilityPage />} />
            <Route path="payouts" element={<PayoutsPage />} />
            <Route path="profile" element={<ProfilePage />} />
          </Route>

          {/* Expert routes WITHOUT the main layout */}
          <Route path="/expert/farmer-view/:farmerId" element={<FarmerDetailViewPage />} />
          <Route path="/expert/planning/:planId" element={<EnterpriseWorksheetPage />} />
          <Route path="/expert/booking/:bookingId/report" element={<ReportEditorPage />} />
        </Route>

        {/* --- Shared Protected Routes --- */}
        <Route element={<ProtectedRoute allowedRoles={["farmer", "expert", "admin"]} />}>
          <Route path="/live-session/:bookingId" element={<LiveSessionPage />} />
        </Route>

        {/* --- Root Redirector & Fallback --- */}
        <Route path="/" element={<RootRedirector />} />
        <Route
          path="*"
          element={
            <div style={{ textAlign: "center", marginTop: "5rem" }}>
              <h2>404 Not Found</h2>
              <p>The page you are looking for does not exist.</p>
            </div>
          }
        />
      </Routes>
    </Router>
  );
}

export default App;

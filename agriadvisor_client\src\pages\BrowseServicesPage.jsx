// src/pages/BrowseServicesPage.jsx
import React, { useState, useEffect } from "react";
import { Container, Row, Col, Spinner, Alert } from "react-bootstrap";
import { getFarmerServices } from "../services/serviceService";
import ServiceCard from "../components/ServiceCard";

const BrowseServicesPage = () => {
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    const fetchServices = async () => {
      try {
        console.log("BrowseServicesPage: Fetching farmer services...");
        const response = await getFarmerServices();
        console.log("BrowseServicesPage: Services response", response);
        const data = response.data || response;
        console.log("BrowseServicesPage: Services data", data);
        setServices(data);
      } catch (err) {
        console.error("BrowseServicesPage: Error fetching services", err);
        console.error("BrowseServicesPage: Error response", err.response?.data);
        setError("Could not load available services.");
      } finally {
        setLoading(false);
      }
    };
    fetchServices();
  }, []);

  if (loading)
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" variant="success" />
      </Container>
    );
  if (error)
    return (
      <Container className="mt-4">
        <Alert variant="danger">{error}</Alert>
      </Container>
    );

  return (
    <Container>
      <h1 className="mb-4">Browse Services</h1>
      <Row>
        {console.log("BrowseServicesPage: Rendering services", services, "Length:", services.length)}
        {services.length > 0 ? (
          services.map((service) => {
            console.log("BrowseServicesPage: Rendering service", service);
            return (
              <Col key={service.id} sm={12} md={6} lg={4} className="mb-4">
                <ServiceCard service={service} />
              </Col>
            );
          })
        ) : (
          <Col>
            <Alert variant="info">No services are currently available.</Alert>
          </Col>
        )}
      </Row>
    </Container>
  );
};
export default BrowseServicesPage;

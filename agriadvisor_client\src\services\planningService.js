// src/services/planningService.js

import api from "./api";

// --- Template Functions ---
export const getEnterpriseTemplates = () => api.get("/planning/templates/");

// --- Enterprise Plan Functions ---
export const getEnterprisePlans = () => api.get("/planning/plans/");
export const getEnterprisePlanDetail = async (id) => {
  console.log("planningService: Fetching plan detail for ID:", id);
  const url = `/planning/plans/${id}/`;
  console.log("planningService: Full URL:", url);
  try {
    const response = await api.get(url);
    console.log("planningService: Plan detail response:", response);
    return response;
  } catch (error) {
    console.error("planningService: Error fetching plan detail:", error);
    console.error("planningService: Error response:", error.response);
    throw error;
  }
};
export const updateEnterprisePlan = (id, data) => api.patch(`/planning/plans/${id}/`, data);

/**
 * Creates a new Enterprise Plan from a selected template.
 * @param {object} data - { template_id, plan_name, farmer_id }
 */
export const createPlanFromTemplate = (data) => {
  // --- THIS IS THE CORRECTED URL ---
  return api.post("/planning/plans/create-from-template/", data);
};


// --- Plan Item Functions ---
export const createPlanItem = (data) => api.post(`/planning/items/`, data);
export const updatePlanItem = (id, data) => api.put(`/planning/items/${id}/`, data);
export const deletePlanItem = (id) => api.delete(`/planning/items/${id}/`);

// --- Assumption Functions ---
// We will need to add the correct URLs for each assumption type
// For example:
// export const getPlanAssumptions = (planId) => api.get(`/planning/plans/${planId}/assumptions/`);
// export const updatePlanAssumptions = (planId, data) => api.put(`/planning/plans/${planId}/assumptions/`, data);


/**
 * Fetches the full financial analysis for a specific Enterprise Plan.
 * @param {number} id - The ID of the plan to analyze.
 */
export const getPlanAnalysis = (id) => api.get(`/planning/plans/${id}/analysis/`);
// --- END OF MISSING FUNCTION ---


/**
 * Fetches all enterprise plans for the currently logged-in FARMER.
 */
export const getMyEnterprisePlans = async () => {
  console.log("planningService: Fetching farmer enterprise plans...");
  try {
    const response = await api.get('/planning/my-plans/');
    console.log("planningService: Farmer plans response", response);
    return response;
  } catch (error) {
    console.error("planningService: Error fetching farmer's enterprise plans:", error);
    throw error;
  }
};


// --- Pig Production Assumption Functions (Corrected URLs) ---
/**
 * Fetches the pig production assumptions for a specific plan.
 * @param {number} planId - The ID of the plan.
 */
export const getPigAssumptions = (planId) => api.get(`/planning/plans/${planId}/pig-assumptions/`);

/**
 * Creates or updates the pig production assumptions for a specific plan.
 * @param {number} planId - The ID of the plan.
 * @param {object} data - The assumption data to save.
 */
export const updatePigAssumptions = (planId, data) => api.put(`/planning/plans/${planId}/pig-assumptions/`, data);






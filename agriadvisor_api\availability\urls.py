# availability/urls.py

from django.urls import path
from rest_framework.routers import DefaultRouter
from .views import MyAvailabilityRuleViewSet, MyAvailabilityExceptionViewSet, CheckAvailabilityView

router = DefaultRouter()
router.register(r'rules', MyAvailabilityRuleViewSet, basename='availability-rule')
router.register(r'exceptions', MyAvailabilityExceptionViewSet, basename='availability-exception')

urlpatterns = router.urls + [
    path('check/', CheckAvailabilityView.as_view(), name='check-availability'),
]


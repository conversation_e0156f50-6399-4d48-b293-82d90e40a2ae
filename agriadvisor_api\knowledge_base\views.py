# knowledge_base/views.py

from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count, F, Case, When, Value
from django.utils import timezone

from .models import ArticleCategory, Article, ArticleView, ArticleLike
from .serializers import (
    ArticleCategorySerializer, ArticleReadSerializer, ArticleWriteSerializer,
    ArticleStatusSerializer, ArticleSearchSerializer, ArticleStatsSerializer
)
from core.views import BaseTenantViewSet
from core.permissions import IsAdminUser

# --- Views for Admins (Full CRUD) ---

class ArticleCategoryAdminViewSet(BaseTenantViewSet):
    """
    Admin endpoint to manage article categories.
    Inherits tenant security from BaseTenantViewSet.
    """
    queryset = ArticleCategory.objects.all().order_by('name')
    serializer_class = ArticleCategorySerializer

    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'order', 'created_at', 'article_count']

    # Override permissions to allow both admins and experts to manage categories
    def get_permissions(self):
        if self.action in ['list', 'retrieve', 'create', 'update', 'partial_update', 'destroy']:
            self.permission_classes = [permissions.IsAuthenticated]
        return super().get_permissions()

    def check_permissions(self, request):
        # Allow admins and experts to manage categories
        super().check_permissions(request)
        if not (request.user.role in ['admin', 'expert']):
            self.permission_denied(request, message="Only admins and experts can manage categories.")
    
    def get_queryset(self):
        queryset = super().get_queryset()
        # Annotate with article counts for better ordering
        return queryset.annotate(
            article_count=Count('articles'),
            published_article_count=Count('articles', filter=Q(articles__status='published'))
        )
    
    @action(detail=True, methods=['post'])
    def toggle_active(self, request, pk=None):
        """Toggle category active status"""
        category = self.get_object()
        category.is_active = not category.is_active
        category.save()
        return Response({'status': 'success', 'is_active': category.is_active})
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get category statistics"""
        categories = self.get_queryset()
        stats = {
            'total_categories': categories.count(),
            'active_categories': categories.filter(is_active=True).count(),
            'categories_with_articles': categories.filter(article_count__gt=0).count(),
        }
        return Response(stats)

class ArticleAdminViewSet(BaseTenantViewSet):
    """
    Admin endpoint to manage articles (drafts and published).
    Inherits tenant security from BaseTenantViewSet.
    """
    queryset = Article.objects.all().order_by('-updated_at')
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'category', 'is_featured', 'author']
    search_fields = ['title', 'content', 'tags__name']
    ordering_fields = ['title', 'created_at', 'updated_at', 'published_at', 'view_count']

    # Override permissions to allow both admins and experts to manage articles
    def get_permissions(self):
        if self.action in ['list', 'retrieve', 'create', 'update', 'partial_update', 'destroy']:
            self.permission_classes = [permissions.IsAuthenticated]
        return super().get_permissions()

    def check_permissions(self, request):
        # Allow admins and experts to manage articles
        super().check_permissions(request)
        if not (request.user.role in ['admin', 'expert']):
            self.permission_denied(request, message="Only admins and experts can manage articles.")
    
    def get_serializer_class(self):
        if self.action in ['create', 'update', 'partial_update']:
            return ArticleWriteSerializer
        return ArticleReadSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        queryset = queryset.select_related('author', 'category').prefetch_related('tags')
        
        # Add view and like counts
        queryset = queryset.annotate(
            view_count=Count('article_views', distinct=True),
            like_count=Count('article_likes', distinct=True)
        )
        
        return queryset

    def perform_create(self, serializer):
        serializer.save(author=self.request.user, tenant=self.request.user.tenant)

    @action(detail=True, methods=['post'])
    def publish(self, request, pk=None):
        """Publish an article"""
        article = self.get_object()
        serializer = ArticleStatusSerializer(article, data={'status': 'published'}, partial=True)
        if serializer.is_valid():
            serializer.save(published_at=timezone.now())
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def archive(self, request, pk=None):
        """Archive an article"""
        article = self.get_object()
        serializer = ArticleStatusSerializer(article, data={'status': 'archived'}, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def toggle_featured(self, request, pk=None):
        """Toggle featured status"""
        article = self.get_object()
        article.is_featured = not article.is_featured
        article.save()
        return Response({'status': 'success', 'is_featured': article.is_featured})

    @action(detail=False, methods=['get'])
    def dashboard_stats(self, request):
        """Get article statistics for admin dashboard"""
        queryset = self.get_queryset()
        
        stats = {
            'total_articles': queryset.count(),
            'published_articles': queryset.filter(status='published').count(),
            'draft_articles': queryset.filter(status='draft').count(),
            'archived_articles': queryset.filter(status='archived').count(),
            'featured_articles': queryset.filter(is_featured=True).count(),
            'total_views': queryset.aggregate(total_views=Sum('view_count'))['total_views'] or 0,
            'total_likes': queryset.aggregate(total_likes=Sum('like_count'))['total_likes'] or 0,
        }
        
        # Most viewed article
        most_viewed = queryset.order_by('-view_count').first()
        if most_viewed:
            stats['most_viewed_article'] = most_viewed.title
        
        # Most liked article
        most_liked = queryset.order_by('-like_count').first()
        if most_liked:
            stats['most_liked_article'] = most_liked.title
            
        return Response(stats)


# --- Views for Farmers/Experts (Read-Only) ---

class PublicArticleCategoryViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Public, read-only endpoint for any authenticated user to view categories
    in their own tenant.
    """
    serializer_class = ArticleCategorySerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.OrderingFilter]
    ordering_fields = ['name', 'order']

    def get_queryset(self):
        # Only show active categories with published articles
        return ArticleCategory.objects.filter(
            tenant=self.request.user.tenant,
            is_active=True,
            articles__status='published'
        ).distinct().order_by('order', 'name')

class PublicArticleViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Public, read-only endpoint for any authenticated user to view PUBLISHED articles
    in their own tenant.
    """
    serializer_class = ArticleReadSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['category', 'is_featured']
    search_fields = ['title', 'content', 'tags__name']
    ordering_fields = ['title', 'created_at', 'updated_at', 'view_count']

    def get_queryset(self):
        queryset = Article.objects.filter(
            tenant=self.request.user.tenant,
            status='published'
        ).select_related('author', 'category').prefetch_related('tags')
        
        # Add view and like counts
        queryset = queryset.annotate(
            view_count=Count('article_views', distinct=True),
            like_count=Count('article_likes', distinct=True)
        )
        
        # Allow filtering by category_id
        category_id = self.request.query_params.get('category_id')
        if category_id:
            queryset = queryset.filter(category_id=category_id)
            
        # Allow filtering by tag
        tag = self.request.query_params.get('tag')
        if tag:
            queryset = queryset.filter(tags__name__icontains=tag)
            
        # Allow filtering by author
        author_id = self.request.query_params.get('author_id')
        if author_id:
            queryset = queryset.filter(author_id=author_id)
            
        return queryset.order_by('-is_featured', '-published_at')

    def retrieve(self, request, *args, **kwargs):
        """Track article views when retrieving individual articles"""
        instance = self.get_object()
        
        # Record view
        ArticleView.objects.create(
            article=instance,
            user=request.user,
            tenant=request.user.tenant
        )
        
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def like(self, request, pk=None):
        """Like or unlike an article"""
        article = self.get_object()
        like, created = ArticleLike.objects.get_or_create(
            article=article,
            user=request.user,
            tenant=request.user.tenant,
            defaults={'liked': True}
        )
        
        if not created:
            like.liked = not like.liked
            like.save()
        
        return Response({
            'status': 'success', 
            'liked': like.liked,
            'like_count': article.article_likes.filter(liked=True).count()
        })

    @action(detail=True, methods=['get'])
    def likes(self, request, pk=None):
        """Get article like status for current user"""
        article = self.get_object()
        user_like = ArticleLike.objects.filter(
            article=article,
            user=request.user
        ).first()
        
        return Response({
            'user_has_liked': user_like.liked if user_like else False,
            'total_likes': article.article_likes.filter(liked=True).count()
        })

    @action(detail=False, methods=['get'])
    def featured(self, request):
        """Get featured articles"""
        queryset = self.get_queryset().filter(is_featured=True)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def recent(self, request):
        """Get recent articles (last 30 days)"""
        thirty_days_ago = timezone.now() - timezone.timedelta(days=30)
        queryset = self.get_queryset().filter(published_at__gte=thirty_days_ago)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def popular(self, request):
        """Get popular articles (most viewed)"""
        queryset = self.get_queryset().order_by('-view_count')
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

# --- Search View ---

class ArticleSearchViewSet(viewsets.GenericViewSet):
    """
    Specialized search endpoint for articles
    """
    serializer_class = ArticleSearchSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter]
    search_fields = ['title', 'content', 'tags__name', 'category__name']

    def get_queryset(self):
        return Article.objects.filter(
            tenant=self.request.user.tenant,
            status='published'
        ).select_related('category', 'author')

    def list(self, request):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)





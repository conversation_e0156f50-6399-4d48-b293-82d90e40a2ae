// src/components/NewPlanModal.jsx

import React, { useState, useEffect } from "react";
import { <PERSON>dal, Button, Form, Spinner, Alert } from "react-bootstrap";
import { getFarmersForExpert } from "../services/farmerService"; // Uses the correct expert-facing service
import { getEnterpriseTemplates } from "../services/planningService";

const NewPlanModal = ({ show, onHide, onSave }) => {
  const [templates, setTemplates] = useState([]);
  const [farmers, setFarmers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  // Form state
  const [plan_name, setPlanName] = useState("");
  const [template_id, setTemplateId] = useState("");
  const [farmer_id, setFarmerId] = useState("");

  useEffect(() => {
    if (show) {
      const fetchData = async () => {
        try {
          setLoading(true);
          setError("");
          const [templatesRes, farmersRes] = await Promise.all([getEnterpriseTemplates(), getFarmersForExpert()]);
          setTemplates(templatesRes.data);
          setFarmers(farmersRes.data);
        } catch (error) {
          setError("Failed to load necessary data. Please try again.");
          console.error("Failed to load data for new plan modal", error);
        } finally {
          setLoading(false);
        }
      };
      fetchData();
    }
  }, [show]);

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave({ plan_name, template_id, farmer_id });
  };

  return (
    <Modal show={show} onHide={onHide}>
      <Form onSubmit={handleSubmit}>
        <Modal.Header closeButton>
          <Modal.Title>Create New Enterprise Plan</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {loading ? (
            <div className="text-center">
              <Spinner animation="border" />
            </div>
          ) : error ? (
            <Alert variant="danger">{error}</Alert>
          ) : (
            <>
              <Form.Group className="mb-3">
                <Form.Label>
                  <strong>1. Select Enterprise Template</strong>
                </Form.Label>
                <Form.Select value={template_id} onChange={(e) => setTemplateId(e.target.value)} required>
                  <option value="">Choose a template...</option>
                  {templates.map((template) => (
                    <option key={template.id} value={template.id}>
                      {template.name}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>
                  <strong>2. Assign to Farmer</strong>
                </Form.Label>
                <Form.Select value={farmer_id} onChange={(e) => setFarmerId(e.target.value)} required>
                  <option value="">Select a farmer...</option>
                  {farmers.map((farmer) => (
                    <option key={farmer.id} value={farmer.id}>
                      {farmer.first_name} {farmer.last_name} ({farmer.username})
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>
                  <strong>3. Give the Plan a Name</strong>
                </Form.Label>
                <Form.Control
                  type="text"
                  value={plan_name}
                  onChange={(e) => setPlanName(e.target.value)}
                  placeholder="e.g., 2025 Dairy Expansion"
                  required
                />
              </Form.Group>
            </>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={onHide}>
            Cancel
          </Button>
          <Button variant="primary" type="submit" disabled={loading || error}>
            Create Plan
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
};
export default NewPlanModal;




# availability/views.py

from rest_framework import viewsets
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from core.permissions import IsExpertUser
from .models import AvailabilityRule, AvailabilityException
from .serializers import AvailabilityRuleSerializer, AvailabilityExceptionSerializer
from accounts.models import CustomUser
from advisory.models import Service
from .utils import calculate_available_slots

class MyAvailabilityRuleViewSet(viewsets.ModelViewSet):
    serializer_class = AvailabilityRuleSerializer
    permission_classes = [IsAuthenticated, IsExpertUser]
    def get_queryset(self):
        return AvailabilityRule.objects.filter(expert=self.request.user)
    def perform_create(self, serializer):
        serializer.save(expert=self.request.user)

class MyAvailabilityExceptionViewSet(viewsets.ModelViewSet):
    serializer_class = AvailabilityExceptionSerializer
    permission_classes = [IsAuthenticated, IsExpertUser]
    def get_queryset(self):
        return AvailabilityException.objects.filter(expert=self.request.user)
    def perform_create(self, serializer):
        serializer.save(expert=self.request.user)

class CheckAvailabilityView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request, *args, **kwargs):
        try:
            expert_id = int(request.query_params.get('expert_id'))
            service_id = int(request.query_params.get('service_id'))
            year = int(request.query_params.get('year'))
            month = int(request.query_params.get('month'))
        except (TypeError, ValueError):
            return Response({"error": "Invalid or missing query parameters."}, status=400)
        try:
            farmer = request.user
            expert = CustomUser.objects.get(id=expert_id, tenant=farmer.tenant, role='expert')
            service = Service.objects.get(id=service_id, tenant=farmer.tenant)
        except (CustomUser.DoesNotExist, Service.DoesNotExist):
            return Response({"error": "Expert or service not found in your organization."}, status=404)
        available_slots = calculate_available_slots(expert, year, month, service.duration_minutes)

        # For testing: If no availability rules are set up, provide some mock slots
        if not available_slots:
            print(f"⚠️  No availability rules found for expert {expert.id}. Generating mock slots for testing.")
            from datetime import datetime, timedelta
            import calendar

            mock_slots = []
            num_days = calendar.monthrange(year, month)[1]
            current_date = datetime.now().date()

            # Generate slots for the next 7 days, 9 AM to 5 PM
            for day_offset in range(7):
                slot_date = current_date + timedelta(days=day_offset)
                if slot_date.year == year and slot_date.month == month:
                    for hour in [9, 11, 14, 16]:  # 9 AM, 11 AM, 2 PM, 4 PM
                        slot_datetime = datetime.combine(slot_date, datetime.min.time().replace(hour=hour))
                        if slot_datetime > datetime.now():
                            mock_slots.append(slot_datetime)

            available_slots = mock_slots

        slots_iso = [slot.isoformat() for slot in available_slots]
        return Response(slots_iso)




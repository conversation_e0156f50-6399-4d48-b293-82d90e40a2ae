# payments/views.py

import stripe
from django.conf import settings
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from core.permissions import IsFarmerUser
from advisory.models import Service, Booking
from accounts.models import CustomUser
from datetime import datetime

stripe.api_key = settings.STRIPE_SECRET_KEY

class CreatePaymentIntentView(APIView):
    permission_classes = [IsAuthenticated, IsFarmerUser] # Reverted to correct permission

    def post(self, request, *args, **kwargs):
        user = request.user
        service_id = request.data.get('service_id')
        expert_id = request.data.get('expert_id')
        booking_time_str = request.data.get('booking_time')

        try:
            service = Service.objects.get(id=service_id, tenant=user.tenant)
            expert = CustomUser.objects.get(id=expert_id, tenant=user.tenant, role='expert')
            booking_time = datetime.fromisoformat(booking_time_str.replace("Z", "+00:00"))

            # Step 1: Create the placeholder booking. This is the crucial first step.
            placeholder_booking = Booking.objects.create(
                service=service,
                expert=expert,
                farmer=user,
                tenant=user.tenant,
                booking_time=booking_time,
                status='pending_payment', # Always start as pending
            )

            amount_in_cents = int(service.price * 100)
            
            # This is your clever logic for testing vs. production
            if not expert.expert_profile or not expert.expert_profile.stripe_account_id:
                # Test Mode: Simple payment
                print(f"⚠️  Expert {expert.id} has no Stripe ID. Creating a simple payment intent.")
                payment_intent = stripe.PaymentIntent.create(
                    amount=amount_in_cents,
                    currency=service.currency.lower(),
                    metadata={'booking_id': placeholder_booking.id, 'test_mode': 'true'}
                )
            else:
                # Production Mode: Payment with transfer
                application_fee = int(amount_in_cents * 0.20)
                payment_intent = stripe.PaymentIntent.create(
                    amount=amount_in_cents,
                    currency=service.currency.lower(),
                    application_fee_amount=application_fee,
                    transfer_data={'destination': expert.expert_profile.stripe_account_id},
                    metadata={'booking_id': placeholder_booking.id}
                )
            
            # Step 2: Link the payment intent to our placeholder booking
            placeholder_booking.stripe_payment_intent_id = payment_intent.id
            placeholder_booking.save()

            # --- STEP 3: THE CRITICAL FIX ---
            # Return BOTH the secret and the ID of the booking we just created.
            return Response({
                'clientSecret': payment_intent.client_secret,
                'bookingId': placeholder_booking.id,
            })

        except Exception as e:
            print(f"❌ Error in CreatePaymentIntentView: {e}")
            return Response({"error": "An unexpected error occurred while initiating payment."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)





# # payments/views.py

# import stripe
# from django.conf import settings
# from rest_framework.views import APIView
# from rest_framework.response import Response
# from rest_framework import status
# from rest_framework.permissions import IsAuthenticated
# from core.permissions import IsFarmerUser
# from advisory.models import Service, Booking # <--- IMPORT Booking
# from accounts.models import CustomUser
# from datetime import datetime # <--- IMPORT datetime

# stripe.api_key = settings.STRIPE_SECRET_KEY

# class CreatePaymentIntentView(APIView):
#     permission_classes = [IsAuthenticated]  # Allow all authenticated users for testing

#     def post(self, request, *args, **kwargs):
#         user = request.user
#         service_id = request.data.get('service_id')
#         expert_id = request.data.get('expert_id')

#         try:
#             service = Service.objects.get(id=service_id, tenant=user.tenant)
#             expert = CustomUser.objects.get(id=expert_id, tenant=user.tenant, role='expert')

#             amount_in_cents = int(service.price * 100)

#             # For testing: Create a simple payment intent without Connect account
#             if not expert.expert_profile or not expert.expert_profile.stripe_account_id:
#                 print(f"⚠️  Expert {expert.id} doesn't have Stripe Connect set up. Creating simple payment intent for testing.")
#                 payment_intent = stripe.PaymentIntent.create(
#                     amount=amount_in_cents,
#                     currency=service.currency.lower(),
#                     metadata={
#                         'service_id': service.id,
#                         'expert_id': expert.id,
#                         'farmer_id': user.id,
#                         'tenant_id': user.tenant.id,
#                         'booking_time': request.data.get('booking_time'),
#                         'test_mode': 'true'  # Flag for testing
#                     }
#                 )
#             else:
#                 # Production: Use Connect account with application fee
#                 application_fee = int(amount_in_cents * 0.20) # 20% platform fee
#                 payment_intent = stripe.PaymentIntent.create(
#                     amount=amount_in_cents,
#                     currency=service.currency.lower(),
#                     application_fee_amount=application_fee,
#                     transfer_data={'destination': expert.expert_profile.stripe_account_id},
#                     metadata={
#                         'service_id': service.id,
#                         'expert_id': expert.id,
#                         'farmer_id': user.id,
#                         'tenant_id': user.tenant.id,
#                         'booking_time': request.data.get('booking_time')
#                     }
#                 )

#             # For testing: Create booking immediately instead of waiting for webhook
#             try:
#                 from advisory.models import Booking
#                 from datetime import datetime

#                 booking_time_str = request.data.get('booking_time')
#                 if booking_time_str:
#                     booking_time = datetime.fromisoformat(booking_time_str.replace("Z", "+00:00"))
#                 else:
#                     booking_time = datetime.now()

#                 booking = Booking.objects.create(
#                     service_id=service.id,
#                     expert_id=expert.id,
#                     farmer_id=user.id,
#                     tenant_id=user.tenant.id,
#                     booking_time=booking_time,
#                     status='confirmed',
#                     stripe_payment_intent_id=payment_intent.id
#                 )
#                 print(f"✅ Created test booking {booking.id} for payment intent {payment_intent.id}")

#             except Exception as e:
#                 print(f"❌ Error creating test booking: {e}")
#                 # Don't fail the payment intent creation if booking creation fails

#             return Response({'clientSecret': payment_intent.client_secret})

#         except Service.DoesNotExist:
#             return Response({"error": "Service not found."}, status=status.HTTP_404_NOT_FOUND)
#         except CustomUser.DoesNotExist:
#             return Response({"error": "Expert not found."}, status=status.HTTP_404_NOT_FOUND)
#         except Exception as e:
#             return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

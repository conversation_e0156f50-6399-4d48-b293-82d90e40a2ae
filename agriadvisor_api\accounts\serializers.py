# accounts/serializers.py

from rest_framework import serializers
from django.db.models import Avg
from django.contrib.auth.models import UserManager
from .models import CustomUser, Tenant, ExpertProfile, FarmerProfile

class TenantSerializer(serializers.ModelSerializer):
    class Meta:
        model = Tenant
        fields = ['id', 'name']

class CustomUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomUser
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'role', 'tenant', 'has_completed_onboarding']

class UserCreateSerializer(serializers.ModelSerializer):
    tenant_name = serializers.CharField(write_only=True)
    class Meta:
        model = CustomUser
        fields = ['username', 'password', 'email', 'first_name', 'last_name', 'tenant_name']
        extra_kwargs = {'password': {'write_only': True}}

    def create(self, validated_data):
        tenant_name = validated_data.pop('tenant_name')
        tenant, created = Tenant.objects.get_or_create(name=tenant_name)
        user = CustomUser.objects.create_user(
            **validated_data, role='farmer', tenant=tenant
        )
        FarmerProfile.objects.create(user=user)
        return user

class ExpertProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = ExpertProfile
        fields = ['specialty', 'bio']

class ExpertReadSerializer(serializers.ModelSerializer):
    profile = ExpertProfileSerializer(source='expert_profile', read_only=True)
    average_rating = serializers.SerializerMethodField()
    review_count = serializers.SerializerMethodField()
    class Meta:
        model = CustomUser
        fields = ['id', 'first_name', 'last_name', 'email', 'phone_number', 'profile', 'average_rating', 'review_count']

    def get_average_rating(self, obj):
        avg = obj.received_reviews.aggregate(Avg('rating')).get('rating__avg')
        return round(avg, 1) if avg else 0

    def get_review_count(self, obj):
        return obj.received_reviews.count()

class ExpertWriteSerializer(serializers.ModelSerializer):
    """
    Serializer for creating/writing Expert data from the Admin panel.
    """
    profile = ExpertProfileSerializer(source='expert_profile')
    full_name = serializers.CharField(write_only=True, required=True)
    class Meta:
        model = CustomUser
        fields = ['id', 'email', 'first_name', 'last_name', 'phone_number', 'profile', 'full_name']
        extra_kwargs = {
            'first_name': {'read_only': True},
            'last_name': {'read_only': True},
        }

    def create(self, validated_data):
        profile_data = validated_data.pop('expert_profile')
        full_name = validated_data.pop('full_name')
        first_name, last_name = (full_name.split(' ', 1) + [''])[:2]
        
        username = validated_data.get('email').split('@')[0]
        
        # Generate a random password for the expert
        import secrets
        import string
        alphabet = string.ascii_letters + string.digits
        password = ''.join(secrets.choice(alphabet) for i in range(12))
        
        user = CustomUser.objects.create_user(
            username=username, 
            password=password, 
            first_name=first_name, 
            last_name=last_name,
            role='expert', 
            tenant=validated_data.get('tenant'),
            email=validated_data.get('email'),
            phone_number=validated_data.get('phone_number')
        )
        ExpertProfile.objects.create(user=user, **profile_data)
        return user
    

    def update(self, instance, validated_data):
        """
        Handles updating an Expert user and their nested ExpertProfile.
        """
        # Pop the nested profile data. If it's not present, default to an empty dict.
        profile_data = validated_data.pop('expert_profile', {})
        full_name = validated_data.pop('full_name', None)

        # --- Update the main CustomUser instance fields ---
        if full_name:
            # Split the full name into first and last
            instance.first_name, instance.last_name = (full_name.split(' ', 1) + [''])[:2]
        
        # Get other fields from validated_data, falling back to the existing value
        instance.email = validated_data.get('email', instance.email)
        instance.phone_number = validated_data.get('phone_number', instance.phone_number)
        
        # Save the changes to the CustomUser model
        instance.save()

        # --- Update the related ExpertProfile instance fields ---
        # We can use update_or_create for safety, but getting should be fine
        profile, created = ExpertProfile.objects.get_or_create(user=instance)
        
        profile.specialty = profile_data.get('specialty', profile.specialty)
        profile.bio = profile_data.get('bio', profile.bio)
        
        # Save the changes to the ExpertProfile model
        profile.save()

        return instance
    
    




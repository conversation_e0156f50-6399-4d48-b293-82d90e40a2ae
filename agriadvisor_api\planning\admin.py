# planning/admin.py

from django.contrib import admin
from .models import (
    EnterpriseTemplate, 
    EnterprisePlan, 
    
    # Import all the specific assumption models so we can register them
    PigProductionAssumptions,
    EggProductionAssumptions,
    DairyLivestockAssumptions,
    BeefLivestockAssumptions,
    SheepProductionAssumptions,
    ForageProductionAssumptions,
    HorticultureCropAssumptions,
    FieldCropAssumptions,
    TilapiaProductionAssumptions,
    ConservancyAssumptions,
    PlantationCropAssumptions,
    IrrigationAssumptions
)

# Simple registration for the main models is usually enough to start
admin.site.register(EnterpriseTemplate)
admin.site.register(EnterprisePlan)

# Register all the assumption models so you can see them in the admin
admin.site.register(PigProductionAssumptions)
admin.site.register(EggProductionAssumptions)
admin.site.register(DairyLivestockAssumptions)
admin.site.register(BeefLivestockAssumptions)
admin.site.register(SheepProductionAssumptions)
admin.site.register(ForageProductionAssumptions)
admin.site.register(HorticultureCropAssumptions)
admin.site.register(FieldCropAssumptions)
admin.site.register(TilapiaProductionAssumptions)
admin.site.register(ConservancyAssumptions)
admin.site.register(PlantationCropAssumptions)
admin.site.register(IrrigationAssumptions)


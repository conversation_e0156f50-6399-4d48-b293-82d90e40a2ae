// src/components/TipTapMenuBar.jsx

import React from "react";
import { Button, ButtonGroup, Dropdown } from "react-bootstrap";

const TipTapMenuBar = ({ editor }) => {
  if (!editor) {
    return null;
  }

  return (
    <div className="p-2 border rounded-top bg-light">
      {/* Basic Formatting Buttons */}
      <ButtonGroup className="me-2">
        <Button
          variant="outline-secondary"
          size="sm"
          onClick={() => editor.chain().focus().toggleBold().run()}
          className={editor.isActive("bold") ? "is-active" : ""}
        >
          Bold
        </Button>
        <Button
          variant="outline-secondary"
          size="sm"
          onClick={() => editor.chain().focus().toggleItalic().run()}
          className={editor.isActive("italic") ? "is-active" : ""}
        >
          Italic
        </Button>
        <Button
          variant="outline-secondary"
          size="sm"
          onClick={() => editor.chain().focus().toggleStrike().run()}
          className={editor.isActive("strike") ? "is-active" : ""}
        >
          Strike
        </Button>
      </ButtonGroup>

      {/* Table-specific Buttons */}
      <Dropdown as={ButtonGroup}>
        <Dropdown.Toggle variant="outline-primary" size="sm" id="table-dropdown">
          Table
        </Dropdown.Toggle>
        <Dropdown.Menu>
          <Dropdown.Item
            onClick={() => editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()}
          >
            Insert Table
          </Dropdown.Item>
          <Dropdown.Divider />
          <Dropdown.Item onClick={() => editor.chain().focus().addColumnBefore().run()}>
            Add Column Before
          </Dropdown.Item>
          <Dropdown.Item onClick={() => editor.chain().focus().addColumnAfter().run()}>Add Column After</Dropdown.Item>
          <Dropdown.Item onClick={() => editor.chain().focus().deleteColumn().run()}>Delete Column</Dropdown.Item>
          <Dropdown.Divider />
          <Dropdown.Item onClick={() => editor.chain().focus().addRowBefore().run()}>Add Row Before</Dropdown.Item>
          <Dropdown.Item onClick={() => editor.chain().focus().addRowAfter().run()}>Add Row After</Dropdown.Item>
          <Dropdown.Item onClick={() => editor.chain().focus().deleteRow().run()}>Delete Row</Dropdown.Item>
          <Dropdown.Divider />
          <Dropdown.Item onClick={() => editor.chain().focus().deleteTable().run()}>Delete Table</Dropdown.Item>
        </Dropdown.Menu>
      </Dropdown>
    </div>
  );
};

export default TipTapMenuBar;




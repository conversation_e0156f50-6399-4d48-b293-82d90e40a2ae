// src/pages/PartialBudgetsPage.jsx

import React, { useState, useEffect, useMemo } from "react";
import { Container, Row, Col, Card, ListGroup, Button, Table, Spinner, Form, InputGroup } from "react-bootstrap";
import {
  getPartialBudgets,
  getPartialBudgetDetail,
  createPartialBudget,
  createPartialBudgetItem,
  deletePartialBudgetItem,
} from "../services/partialBudgetService";
import NewPartialBudgetModal from "../components/NewPartialBudgetModal";

const formatCurrency = (number) =>
  new Intl.NumberFormat("en-US", { style: "currency", currency: "USD" }).format(parseFloat(number || 0));

// Helper function to round numbers to 2 decimal places for backend compatibility
const roundToTwoDecimals = (number) => Math.round((parseFloat(number) || 0) * 100) / 100;

const PartialBudgetsPage = () => {
  const [budgets, setBudgets] = useState([]);
  const [selectedBudget, setSelectedBudget] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showNewBudgetModal, setShowNewBudgetModal] = useState(false);

  // Debug: Check authentication
  console.log("PartialBudgetsPage: Access token:", localStorage.getItem("access_token") ? "Present" : "Missing");

  const fetchBudgets = async () => {
    try {
      setLoading(true);
      const response = await getPartialBudgets();
      setBudgets(response.data);
    } catch (error) {
      console.error("Failed to fetch partial budgets", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBudgets();
  }, []);

  const handleSelectBudget = async (id) => {
    try {
      setSelectedBudget(null);
      const response = await getPartialBudgetDetail(id);
      setSelectedBudget(response.data);
    } catch (error) {
      console.error("Failed to fetch budget details", error);
    }
  };

  const handleSaveNewBudget = async (budgetData) => {
    try {
      await createPartialBudget(budgetData);
      setShowNewBudgetModal(false);
      fetchBudgets();
    } catch (error) {
      alert("Failed to create new analysis.");
      console.error(error);
    }
  };

  const refreshSelectedBudget = () => {
    if (selectedBudget) {
      handleSelectBudget(selectedBudget.id);
    }
  };

  if (loading)
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" />
      </Container>
    );

  return (
    <Container fluid>
      <Row>
        <Col md={4} className="mb-3">
          <Card>
            <Card.Header as="h4" className="d-flex justify-content-between align-items-center">
              Partial Budget Analyses
              <Button size="sm" onClick={() => setShowNewBudgetModal(true)}>
                + New Analysis
              </Button>
            </Card.Header>
            <ListGroup variant="flush">
              {budgets.map((budget) => (
                <ListGroup.Item
                  key={budget.id}
                  action
                  active={selectedBudget?.id === budget.id}
                  onClick={() => handleSelectBudget(budget.id)}
                >
                  {budget.name}
                </ListGroup.Item>
              ))}
              {budgets.length === 0 && <ListGroup.Item>No analyses created yet.</ListGroup.Item>}
            </ListGroup>
          </Card>
        </Col>
        <Col md={8}>
          {selectedBudget ? (
            <PartialBudgetWorksheet budget={selectedBudget} onRefresh={refreshSelectedBudget} />
          ) : (
            <Card>
              <Card.Body className="text-center text-muted">
                <p>Select an analysis to view its details.</p>
              </Card.Body>
            </Card>
          )}
        </Col>
      </Row>
      <NewPartialBudgetModal
        show={showNewBudgetModal}
        onHide={() => setShowNewBudgetModal(false)}
        onSave={handleSaveNewBudget}
      />
    </Container>
  );
};

// --- Child Components ---

const PartialBudgetWorksheet = ({ budget, onRefresh }) => {
  console.log("PartialBudgetWorksheet received budget:", budget);

  const positiveItems = budget.items.filter(
    (i) => i.effect_type === "additional_income" || i.effect_type === "reduced_cost"
  );
  const negativeItems = budget.items.filter(
    (i) => i.effect_type === "reduced_income" || i.effect_type === "additional_cost"
  );

  const totals = useMemo(() => {
    const totalPositives = positiveItems.reduce((sum, item) => sum + parseFloat(item.total || 0), 0);
    const totalNegatives = negativeItems.reduce((sum, item) => sum + parseFloat(item.total || 0), 0);
    return { positives: totalPositives, negatives: totalNegatives, netChange: totalPositives - totalNegatives };
  }, [positiveItems, negativeItems]);

  const handleAddItem = async (effectType, itemData) => {
    try {
      // Validate budget exists
      if (!budget || !budget.id) {
        alert("No budget selected. Please select a budget first.");
        return;
      }

      // Validate effect type
      const validEffectTypes = ['additional_income', 'reduced_cost', 'reduced_income', 'additional_cost'];
      if (!validEffectTypes.includes(effectType)) {
        alert("Invalid effect type: " + effectType);
        return;
      }

      // Ensure numeric fields are properly converted and rounded to 2 decimal places
      const payload = {
        budget: parseInt(budget.id),
        effect_type: effectType,
        description: itemData.description.trim(),
        number: roundToTwoDecimals(itemData.number) || 1.0,
        amount: roundToTwoDecimals(itemData.amount),
        price: roundToTwoDecimals(itemData.price)
      };

      console.log("Creating partial budget item with payload:", payload);
      const response = await createPartialBudgetItem(payload);
      console.log("Successfully created item:", response.data);
      onRefresh();
    } catch (error) {
      console.error("Error creating partial budget item:", error);
      console.error("Error response:", error.response);
      console.error("Error response data:", JSON.stringify(error.response?.data, null, 2));
      console.error("Error response status:", error.response?.status);
      console.error("Error response headers:", error.response?.headers);
      console.error("Request config:", error.config);

      let errorMessage = "Failed to add item.";
      if (error.response?.data) {
        if (typeof error.response.data === 'object') {
          errorMessage += "\nServer validation errors:\n" + JSON.stringify(error.response.data, null, 2);
        } else {
          errorMessage += "\nServer response: " + error.response.data;
        }
      }
      if (error.response?.status) {
        errorMessage += "\nHTTP Status: " + error.response.status;
      }
      alert(errorMessage);
    }
  };

  const handleDeleteItem = async (id) => {
    if (window.confirm("Delete this item?")) {
      try {
        await deletePartialBudgetItem(id);
        onRefresh();
      } catch (error) {
        alert("Failed to delete item.");
      }
    }
  };

  return (
    <div>
      <Card className="mb-3">
        <Card.Body>
          <h5>Proposed Change: {budget.proposed_change_desc}</h5>
        </Card.Body>
      </Card>
      <Row>
        <Col lg={6} className="mb-3 d-flex flex-column">
          <EffectQuadrant
            title="Positive Effects (+)"
            items={positiveItems}
            types={[
              { key: "additional_income", value: "Additional Income" },
              { key: "reduced_cost", value: "Reduced Cost" },
            ]}
            onAddItem={handleAddItem}
            onDeleteItem={handleDeleteItem}
            headerClass="bg-success text-white"
            totalClass="table-success"
          />
        </Col>
        <Col lg={6} className="mb-3 d-flex flex-column">
          <EffectQuadrant
            title="Negative Effects (-)"
            items={negativeItems}
            types={[
              { key: "reduced_income", value: "Reduced Income" },
              { key: "additional_cost", value: "Additional Cost" },
            ]}
            onAddItem={handleAddItem}
            onDeleteItem={handleDeleteItem}
            headerClass="bg-danger text-white"
            totalClass="table-danger"
          />
        </Col>
      </Row>
      <SummaryCard totals={totals} />
      <NotesCard />
    </div>
  );
};

const InputRow = ({ effectType, onAdd }) => {
  const [data, setData] = useState({ description: "", number: "1", amount: "", price: "" });
  const handleChange = (e) => setData({ ...data, [e.target.name]: e.target.value });
  const handleAdd = () => {
    if (!data.description.trim()) {
      alert("Description is required.");
      return;
    }
    if (!data.amount || parseFloat(data.amount) <= 0) {
      alert("Amount must be a positive number.");
      return;
    }
    if (!data.price || parseFloat(data.price) <= 0) {
      alert("Price must be a positive number.");
      return;
    }

    console.log("InputRow sending data:", data);
    onAdd(effectType, data);
    setData({ description: "", number: "1", amount: "", price: "" });
  };

  return (
    <tr>
      <td>
        <Form.Control
          size="sm"
          type="text"
          name="description"
          value={data.description}
          onChange={handleChange}
          placeholder="Item description..."
        />
      </td>
      <td>
        <Form.Control size="sm" type="number" name="number" value={data.number} onChange={handleChange} />
      </td>
      <td>
        <Form.Control
          size="sm"
          type="number"
          name="amount"
          value={data.amount}
          onChange={handleChange}
          step="0.01"
          placeholder="e.g., Weight/Qty"
        />
      </td>
      <td>
        <Form.Control
          size="sm"
          type="number"
          name="price"
          value={data.price}
          onChange={handleChange}
          step="0.01"
          placeholder="Price/Unit"
        />
      </td>
      <td className="text-end align-middle">
        {formatCurrency((data.number || 0) * (data.amount || 0) * (data.price || 0))}
      </td>
      <td className="text-center align-middle">
        <Button size="sm" variant="primary" onClick={handleAdd}>
          Add
        </Button>
      </td>
    </tr>
  );
};

const EffectQuadrant = ({ title, items, types, onAddItem, onDeleteItem, headerClass, totalClass }) => {
  const total = useMemo(() => items.reduce((sum, item) => sum + parseFloat(item.total || 0), 0), [items]);

  return (
    <Card className="flex-grow-1">
      <Card.Header as="h5" className={headerClass}>
        {title}
      </Card.Header>
      <div className="table-responsive">
        <Table striped bordered hover size="sm" className="mb-0">
          <thead>
            <tr>
              <th>Description</th>
              <th>No.</th>
              <th>Amount</th>
              <th>Price</th>
              <th className="text-end">Total</th>
              <th></th>
            </tr>
          </thead>
          <tbody>
            {types.map((type) => (
              <React.Fragment key={type.key}>
                <tr className="table-light">
                  <td colSpan="6">
                    <strong>{type.value}</strong>
                  </td>
                </tr>
                {items
                  .filter((i) => i.effect_type === type.key)
                  .map((item) => (
                    <tr key={item.id}>
                      <td>{item.description}</td>
                      <td>{item.number}</td>
                      <td>{item.amount}</td>
                      <td>{formatCurrency(item.price)}</td>
                      <td className="text-end fw-bold">{formatCurrency(item.total)}</td>
                      <td className="text-center">
                        <Button variant="link" className="text-danger p-0" onClick={() => onDeleteItem(item.id)}>
                          <i className="bi bi-x-circle-fill"></i>
                        </Button>
                      </td>
                    </tr>
                  ))}
                <InputRow effectType={type.key} onAdd={onAddItem} />
              </React.Fragment>
            ))}
            <tr className={`fw-bold ${totalClass}`}>
              <td colSpan="4" className="text-end">
                Total {title}
              </td>
              <td className="text-end">{formatCurrency(total)}</td>
              <td></td>
            </tr>
          </tbody>
        </Table>
      </div>
    </Card>
  );
};

const SummaryCard = ({ totals }) => (
  <Card className="mt-3">
    <Card.Body>
      <Table borderless size="sm" className="mb-0">
        <tbody>
          <tr>
            <td>Total Positive Effects (Additional Income + Reduced Costs)</td>
            <td className="text-end fs-5">{formatCurrency(totals.positives)}</td>
          </tr>
          <tr>
            <td>Total Negative Effects (Reduced Income + Additional Costs)</td>
            <td className="text-end fs-5">({formatCurrency(totals.negatives)})</td>
          </tr>
          <tr className="fw-bold fs-4 border-top">
            <td>Change in Net Income</td>
            <td className={`text-end ${totals.netChange >= 0 ? "text-success" : "text-danger"}`}>
              {formatCurrency(totals.netChange)}
            </td>
          </tr>
        </tbody>
      </Table>
    </Card.Body>
  </Card>
);

const NotesCard = () => (
  <Card className="mt-3" style={{ borderLeft: "5px solid #ffc107" }}>
    <Card.Header as="h5">How to Use This Tool</Card.Header>
    <Card.Body>
      <ul>
        <li>
          A Partial Budget analyzes the financial impact of a <strong>single change</strong> in your farm operation.
        </li>
        <li>
          <strong>Positive Effects</strong> increase profit: either new income you gain (Additional Income) or old costs
          you no longer have (Reduced Costs).
        </li>
        <li>
          <strong>Negative Effects</strong> decrease profit: either old income you give up (Reduced Income) or new costs
          you take on (Additional Costs).
        </li>
        <li>
          The <strong>Net Change in Profit</strong> tells you if the proposed change is financially viable.
        </li>
      </ul>
    </Card.Body>
  </Card>
);

export default PartialBudgetsPage;



// src/pages/PartialBudgetsPage.jsx

import React, { useState, useEffect, useMemo } from "react";
import { Container, Row, Col, Card, ListGroup, Button, Table, Spinner, Form, InputGroup } from "react-bootstrap";
import {
  getPartialBudgets,
  getPartialBudgetDetail,
  createPartialBudget,
  createPartialBudgetItem,
  deletePartialBudgetItem,
} from "../services/partialBudgetService";
import NewPartialBudgetModal from "../components/NewPartialBudgetModal";

const formatCurrency = (number) => {
  const num = parseFloat(number || 0);
  return new Intl.NumberFormat("en-US", { style: "currency", currency: "USD" }).format(num);
};

const PartialBudgetsPage = () => {
  const [budgets, setBudgets] = useState([]);
  const [selectedBudget, setSelectedBudget] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showNewBudgetModal, setShowNewBudgetModal] = useState(false);

  const fetchBudgets = async () => {
    try {
      setLoading(true);
      const response = await getPartialBudgets();
      setBudgets(response.data);
    } catch (error) {
      console.error("Failed to fetch partial budgets", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBudgets();
  }, []);

  const handleSelectBudget = async (id) => {
    try {
      setSelectedBudget(null); // Clear previous selection to show loading state
      const response = await getPartialBudgetDetail(id);
      setSelectedBudget(response.data);
    } catch (error) {
      console.error("Failed to fetch budget details", error);
    }
  };

  const handleSaveNewBudget = async (budgetData) => {
    try {
      await createPartialBudget(budgetData);
      setShowNewBudgetModal(false);
      fetchBudgets(); // Refresh the list to show the new budget
    } catch (error) {
      alert("Failed to create new analysis.");
      console.error(error);
    }
  };

  const refreshSelectedBudget = () => {
    if (selectedBudget) {
      handleSelectBudget(selectedBudget.id);
    }
  };

  if (loading) {
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" />
      </Container>
    );
  }

  return (
    <Container fluid>
      <Row>
        <Col md={4} className="mb-3">
          <Card>
            <Card.Header as="h4" className="d-flex justify-content-between align-items-center">
              Partial Budgets
              <Button size="sm" onClick={() => setShowNewBudgetModal(true)}>
                + New Analysis
              </Button>
            </Card.Header>
            <ListGroup variant="flush">
              {budgets.map((budget) => (
                <ListGroup.Item
                  key={budget.id}
                  action
                  active={selectedBudget?.id === budget.id}
                  onClick={() => handleSelectBudget(budget.id)}
                >
                  {budget.name}
                </ListGroup.Item>
              ))}
              {budgets.length === 0 && <ListGroup.Item>No analyses created yet.</ListGroup.Item>}
            </ListGroup>
          </Card>
        </Col>
        <Col md={8}>
          {selectedBudget ? (
            <PartialBudgetWorksheet budget={selectedBudget} onRefresh={refreshSelectedBudget} />
          ) : (
            <Card>
              <Card.Body className="text-center text-muted">
                <p>Select a partial budget analysis to view its details, or create a new one.</p>
              </Card.Body>
            </Card>
          )}
        </Col>
      </Row>
      <NewPartialBudgetModal
        show={showNewBudgetModal}
        onHide={() => setShowNewBudgetModal(false)}
        onSave={handleSaveNewBudget}
      />
    </Container>
  );
};

// --- Child Components ---

const PartialBudgetWorksheet = ({ budget, onRefresh }) => {
  const positiveItems = budget.items.filter(
    (i) => i.effect_type === "additional_income" || i.effect_type === "reduced_cost"
  );
  const negativeItems = budget.items.filter(
    (i) => i.effect_type === "reduced_income" || i.effect_type === "additional_cost"
  );

  const totals = useMemo(() => {
    const totalPositives = positiveItems.reduce((sum, item) => sum + parseFloat(item.amount), 0);
    const totalNegatives = negativeItems.reduce((sum, item) => sum + parseFloat(item.amount), 0);
    return {
      positives: totalPositives,
      negatives: totalNegatives,
      netChange: totalPositives - totalNegatives,
    };
  }, [budget.items]);

  const handleAddItem = async (effectType, description, amount) => {
    if (!description || !amount) return;
    try {
      await createPartialBudgetItem({ budget: budget.id, effect_type: effectType, description, amount });
      onRefresh();
    } catch (error) {
      alert("Failed to add item.");
    }
  };

  const handleDeleteItem = async (id) => {
    if (window.confirm("Are you sure you want to delete this item?")) {
      try {
        await deletePartialBudgetItem(id);
        onRefresh();
      } catch (error) {
        alert("Failed to delete item.");
      }
    }
  };

  return (
    <div>
      <Row>
        <Col xs={12} className="mb-3">
          <Card>
            <Card.Body>
              <h5>Analysis For: {budget.name}</h5>
              {budget.proposed_change_desc && <p className="mb-0 text-muted">{budget.proposed_change_desc}</p>}
            </Card.Body>
          </Card>
        </Col>
        <Col lg={6} className="mb-3">
          <EffectSection
            title="Positive Effects (+)"
            items={positiveItems}
            types={{ additional_income: "Additional Income", reduced_cost: "Reduced Cost" }}
            onAddItem={handleAddItem}
            onDeleteItem={handleDeleteItem}
            headerClass="bg-success text-white"
          />
        </Col>
        <Col lg={6} className="mb-3">
          <EffectSection
            title="Negative Effects (-)"
            items={negativeItems}
            types={{ reduced_income: "Reduced Income", additional_cost: "Additional Cost" }}
            onAddItem={handleAddItem}
            onDeleteItem={handleDeleteItem}
            headerClass="bg-danger text-white"
          />
        </Col>
        <Col xs={12}>
          <SummaryCard totals={totals} />
        </Col>
        <Col xs={12}>
          <NotesCard />
        </Col>
      </Row>
    </div>
  );
};

const EffectSection = ({ title, items, types, onAddItem, onDeleteItem, headerClass }) => {
  const [description, setDescription] = useState("");
  const [amount, setAmount] = useState("");
  const [effectType, setEffectType] = useState(Object.keys(types)[0]);

  const handleAddClick = () => {
    onAddItem(effectType, description, amount);
    setDescription("");
    setAmount("");
  };

  return (
    <Card className="h-100">
      <Card.Header as="h5" className={headerClass}>
        {title}
      </Card.Header>
      <Card.Body className="d-flex flex-column">
        <div className="table-responsive" style={{ flexGrow: 1 }}>
          <Table striped size="sm">
            <thead>
              <tr>
                <th>Description</th>
                <th>Type</th>
                <th className="text-end">Amount</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              {items.map((item) => (
                <tr key={item.id}>
                  <td>{item.description}</td>
                  <td className="text-capitalize">{types[item.effect_type]}</td>
                  <td className="text-end">{formatCurrency(item.amount)}</td>
                  <td>
                    <Button variant="link" className="text-danger p-0" onClick={() => onDeleteItem(item.id)}>
                      <i className="bi bi-x-circle-fill"></i>
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </Table>
        </div>
        <InputGroup className="mt-3">
          <Form.Control
            placeholder="Description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
          />
          <Form.Control type="number" placeholder="Amount" value={amount} onChange={(e) => setAmount(e.target.value)} />
          <Form.Select value={effectType} onChange={(e) => setEffectType(e.target.value)}>
            {Object.entries(types).map(([key, value]) => (
              <option key={key} value={key}>
                {value}
              </option>
            ))}
          </Form.Select>
          <Button onClick={handleAddClick}>Add</Button>
        </InputGroup>
      </Card.Body>
    </Card>
  );
};

const SummaryCard = ({ totals }) => (
  <Card>
    <Card.Header as="h5">Summary</Card.Header>
    <Card.Body className="text-center">
      <Row>
        <Col>
          <Card bg="light-success">
            <Card.Body>
              <h6>Total Positive Effects</h6>
              <h3>{formatCurrency(totals.positives)}</h3>
            </Card.Body>
          </Card>
        </Col>
        <Col>
          <Card bg="light-danger">
            <Card.Body>
              <h6>Total Negative Effects</h6>
              <h3>({formatCurrency(totals.negatives)})</h3>
            </Card.Body>
          </Card>
        </Col>
      </Row>
      <hr />
      <h4>Net Change in Profit</h4>
      <h2 className={totals.netChange >= 0 ? "text-success" : "text-danger"}>{formatCurrency(totals.netChange)}</h2>
    </Card.Body>
  </Card>
);

const NotesCard = () => (
  <Card className="mt-3" style={{ borderLeft: "5px solid #ffc107" }}>
    <Card.Header as="h5">How to Use This Tool</Card.Header>
    <Card.Body>
      <ul>
        <li>
          A Partial Budget analyzes the financial impact of a <strong>single change</strong> in your farm operation.
        </li>
        <li>
          <strong>Positive Effects</strong> increase profit: either new income you gain (Additional Income) or old costs
          you no longer have (Reduced Costs).
        </li>
        <li>
          <strong>Negative Effects</strong> decrease profit: either old income you give up (Reduced Income) or new costs
          you take on (Additional Costs).
        </li>
        <li>
          The <strong>Net Change in Profit</strong> tells you if the proposed change is financially viable. A positive
          number means the change is profitable.
        </li>
      </ul>
    </Card.Body>
  </Card>
);

export default PartialBudgetsPage;




{"name": "agriadvisor_client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fullcalendar/daygrid": "^6.1.19", "@fullcalendar/interaction": "^6.1.19", "@fullcalendar/react": "^6.1.19", "@fullcalendar/timegrid": "^6.1.19", "@stripe/react-stripe-js": "^3.9.2", "@stripe/stripe-js": "^7.9.0", "@tiptap/extension-character-count": "^3.3.0", "@tiptap/extension-link": "^3.3.0", "@tiptap/extension-placeholder": "^3.3.0", "@tiptap/extension-table": "^3.3.1", "@tiptap/extension-table-cell": "^3.3.1", "@tiptap/extension-table-header": "^3.3.1", "@tiptap/extension-table-row": "^3.3.1", "@tiptap/extension-underline": "^3.3.0", "@tiptap/react": "^3.3.0", "@tiptap/starter-kit": "^3.3.0", "axios": "^1.11.0", "bootstrap": "^5.3.8", "jwt-decode": "^4.0.0", "react": "^19.1.1", "react-bootstrap": "^2.10.10", "react-dom": "^19.1.1", "react-router-bootstrap": "^0.26.3", "react-router-dom": "^7.8.2", "react-toastify": "^11.0.5", "twilio-video": "^2.32.1"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.1.2"}}
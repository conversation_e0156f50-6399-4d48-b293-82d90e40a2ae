# core/email.py

from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings

def send_agriadvisor_email(subject, template_name, context, recipient_list):
    """
    A helper function to send styled HTML emails using Django templates.
    """
    try:
        # In a real app, you would have both html_message and a plain text message.
        html_message = render_to_string(template_name, context)
        
        send_mail(
            subject=subject,
            message='', # Plain text version (can be generated from html)
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=recipient_list,
            html_message=html_message,
            fail_silently=False,
        )
        print(f"✅ Email sent successfully to {recipient_list}")
    except Exception as e:
        # Log the error if the email fails to send
        print(f"❌ Failed to send email to {recipient_list}. Error: {e}")




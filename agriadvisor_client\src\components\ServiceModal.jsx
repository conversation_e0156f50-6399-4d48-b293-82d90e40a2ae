// src/components/ServiceModal.jsx

import React, { useState, useEffect } from "react";
import { Modal, Button, Form } from "react-bootstrap";

const ServiceModal = ({ show, onHide, onSave, service }) => {
  // State to hold the form data
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    price: "",
    currency: "USD",
    duration_minutes: "",
  });

  // A boolean to easily check if we are editing an existing service or adding a new one
  const isEditing = service !== null;

  // This `useEffect` hook runs whenever the 'service' prop or the 'show' prop changes.
  // Its job is to populate the form for editing or reset it for adding.
  useEffect(() => {
    if (isEditing) {
      // If a service object is passed, we're editing. Fill the form.
      setFormData({
        name: service.name,
        description: service.description,
        price: service.price,
        currency: service.currency,
        duration_minutes: service.duration_minutes,
      });
    } else {
      // If no service object, we're adding. Reset the form.
      setFormData({
        name: "",
        description: "",
        price: "",
        currency: "USD",
        duration_minutes: "",
      });
    }
  }, [service, show]); // Dependencies: re-run if these change

  // A generic handler to update the formData state as the user types
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({ ...prevData, [name]: value }));
  };

  // Called when the form is submitted
  const handleSubmit = (e) => {
    e.preventDefault(); // Prevent default browser form submission
    console.log("ServiceModal: Form submitted with data", formData);
    onSave(formData); // Call the onSave function passed from the parent page
  };

  return (
    <Modal show={show} onHide={onHide} centered>
      <Form onSubmit={handleSubmit}>
        <Modal.Header closeButton>
          <Modal.Title>{isEditing ? "Edit Service" : "Add New Service"}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form.Group className="mb-3">
            <Form.Label>Service Name</Form.Label>
            <Form.Control type="text" name="name" value={formData.name} onChange={handleChange} required />
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Description</Form.Label>
            <Form.Control
              as="textarea"
              rows={3}
              name="description"
              value={formData.description}
              onChange={handleChange}
              required
            />
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Price</Form.Label>
            <Form.Control
              type="number"
              name="price"
              step="0.01"
              value={formData.price}
              onChange={handleChange}
              required
            />
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Duration (minutes)</Form.Label>
            <Form.Control
              type="number"
              name="duration_minutes"
              value={formData.duration_minutes}
              onChange={handleChange}
              required
            />
          </Form.Group>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={onHide}>
            Cancel
          </Button>
          <Button variant="primary" type="submit">
            Save Changes
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
};
export default ServiceModal;



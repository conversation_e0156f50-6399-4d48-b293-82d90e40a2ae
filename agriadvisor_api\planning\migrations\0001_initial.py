# Generated by Django 5.2.5 on 2025-08-27 21:31

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='EnterpriseTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('icon', models.CharField(blank=True, max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='EnterprisePlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('created_at', models.DateTime<PERSON>ield(auto_now_add=True)),
                ('assessment_data', models.J<PERSON>NField(blank=True, null=True)),
                ('expert', models.ForeignKey(limit_choices_to={'role': 'expert'}, on_delete=django.db.models.deletion.CASCADE, related_name='managed_plans', to=settings.AUTH_USER_MODEL)),
                ('farmer', models.ForeignKey(limit_choices_to={'role': 'farmer'}, on_delete=django.db.models.deletion.CASCADE, related_name='enterprise_plans', to=settings.AUTH_USER_MODEL)),
                ('tenant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enterprise_plans', to='accounts.tenant')),
                ('based_on_template', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='planning.enterprisetemplate')),
            ],
        ),
        migrations.CreateModel(
            name='TemplateItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item_type', models.CharField(choices=[('income', 'Source of Income'), ('variable', 'Variable Cost'), ('fixed', 'Fixed Cost')], max_length=10)),
                ('category', models.CharField(max_length=100)),
                ('item_name', models.CharField(max_length=100)),
                ('default_unit', models.CharField(default='Unit', max_length=20)),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='template_items', to='planning.enterprisetemplate')),
            ],
        ),
        migrations.CreateModel(
            name='PlanItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item_type', models.CharField(max_length=10)),
                ('category', models.CharField(max_length=100)),
                ('item_name', models.CharField(max_length=100)),
                ('quantity', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('price_per_unit', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('actual_amount', models.DecimalField(blank=True, decimal_places=2, default=0.0, max_digits=12, null=True)),
                ('plan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='plan_items', to='planning.enterpriseplan')),
                ('template_item', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='planning.templateitem')),
            ],
        ),
    ]

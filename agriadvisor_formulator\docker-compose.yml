services:
  formulator_api:
    build: .
    # The command to run after the entrypoint script
    command: python manage.py runserver 0.0.0.0:8000
    

    volumes:
      - .:/app
    ports:
      # Map port 8002 on your machine to port 8000 in the container
      - "8002:8000"
    env_file:
      - .env
    depends_on:
      - formulator_db
  formulator_db:
    image: postgres:14-alpine
    volumes:
      - postgres_data_formulator:/var/lib/postgresql/data/
    environment:
      - POSTGRES_DB=${DB_NAME}
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASS}

volumes:
  postgres_data_formulator:



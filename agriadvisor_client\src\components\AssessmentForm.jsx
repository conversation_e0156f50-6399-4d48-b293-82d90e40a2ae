// src/components/AssessmentForm.jsx

import React, { useState, useEffect } from "react";
import { Form, Row, Col, Card, Button } from "react-bootstrap";

const AssessmentForm = ({ plan, onSave, isSaving }) => {
  // Initialize state with all possible keys to prevent uncontrolled input errors.
  const [formData, setFormData] = useState({
    strategicGoals: "",
    totalAcreage: "",
    landUnderUse: "",
    soilType: "",
    waterAvailability: "",
    landTenure: "",
    familyLabor: "",
    hiredLaborNeeds: "",
    skillLevel: "",
    machinery: "",
    storage: "",
    accessToCredit: "",
    enterpriseSelection: "",
    marketOpportunities: "",
    riskProfile: "",
    otherConsiderations: "", // <-- Initialize the new field
  });

  // This effect correctly populates the form when a plan is selected or updated.
  useEffect(() => {
    const initialData = plan.assessment_data || {};
    // Ensure all state keys have a default value.
    setFormData({
      strategicGoals: initialData.strategicGoals || "",
      totalAcreage: initialData.totalAcreage || "",
      landUnderUse: initialData.landUnderUse || "",
      soilType: initialData.soilType || "",
      waterAvailability: initialData.waterAvailability || "",
      landTenure: initialData.landTenure || "",
      familyLabor: initialData.familyLabor || "",
      hiredLaborNeeds: initialData.hiredLaborNeeds || "",
      skillLevel: initialData.skillLevel || "",
      machinery: initialData.machinery || "",
      storage: initialData.storage || "",
      accessToCredit: initialData.accessToCredit || "",
      enterpriseSelection: initialData.enterpriseSelection || "",
      marketOpportunities: initialData.marketOpportunities || "",
      riskProfile: initialData.riskProfile || "",
      otherConsiderations: initialData.otherConsiderations || "", // <-- Handle the new field
    });
  }, [plan]);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSaveClick = () => {
    onSave(formData);
  };

  return (
    <Card>
      <Card.Header as="h5">Pre-Budgeting Farm Assessment</Card.Header>
      <Card.Body>
        <Form>
          <Row>
            {/* --- Strategic Goals --- */}
            <Col md={12} className="mb-3">
              <Form.Group>
                <Form.Label className="fw-bold">Strategic Goals</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  name="strategicGoals"
                  value={formData.strategicGoals}
                  onChange={handleChange}
                  placeholder="What is the primary goal for this season/enterprise?"
                />
              </Form.Group>
            </Col>

            {/* --- Land Resources --- */}
            <Col md={12}>
              <h6 className="mt-2">Land Resources</h6>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Total Acreage</Form.Label>
                <Form.Control type="text" name="totalAcreage" value={formData.totalAcreage} onChange={handleChange} />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Land Under Use</Form.Label>
                <Form.Control type="text" name="landUnderUse" value={formData.landUnderUse} onChange={handleChange} />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Soil Type & Health</Form.Label>
                <Form.Control type="text" name="soilType" value={formData.soilType} onChange={handleChange} />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Water Availability</Form.Label>
                <Form.Control
                  type="text"
                  name="waterAvailability"
                  value={formData.waterAvailability}
                  onChange={handleChange}
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Land Tenure</Form.Label>
                <Form.Control
                  type="text"
                  name="landTenure"
                  value={formData.landTenure}
                  onChange={handleChange}
                  placeholder="Owned, leased, family land?"
                />
              </Form.Group>
            </Col>

            {/* --- Labor Resources --- */}
            <Col md={12}>
              <h6 className="mt-2">Labor Resources</h6>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Family Labor</Form.Label>
                <Form.Control type="text" name="familyLabor" value={formData.familyLabor} onChange={handleChange} />
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Hired Labor Needs</Form.Label>
                <Form.Control
                  type="text"
                  name="hiredLaborNeeds"
                  value={formData.hiredLaborNeeds}
                  onChange={handleChange}
                />
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Skill Level</Form.Label>
                <Form.Control type="text" name="skillLevel" value={formData.skillLevel} onChange={handleChange} />
              </Form.Group>
            </Col>

            <Col md={12}>
              <h6 className="mt-3 text-muted">Capital Resources</h6>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Machinery & Tools</Form.Label>
                <Form.Control type="text" name="machinery" value={formData.machinery} onChange={handleChange} />
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Storage Facilities</Form.Label>
                <Form.Control type="text" name="storage" value={formData.storage} onChange={handleChange} />
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Access to Credit</Form.Label>
                <Form.Control
                  type="text"
                  name="accessToCredit"
                  value={formData.accessToCredit}
                  onChange={handleChange}
                />
              </Form.Group>
            </Col>
          </Row>

          <hr />

          {/* --- Section 3: Enterprise & Risk --- */}
          <h5 className="fw-bold mb-3">3. Enterprise & Risk Assessment</h5>
          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Enterprise Selection</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  name="enterpriseSelection"
                  value={formData.enterpriseSelection}
                  onChange={handleChange}
                  placeholder="What crops/livestock are you planning and why?"
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Market Opportunities</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  name="marketOpportunities"
                  value={formData.marketOpportunities}
                  onChange={handleChange}
                  placeholder="Who is the buyer? What are the quality requirements?"
                />
              </Form.Group>
            </Col>
            <Col md={12}>
              <Form.Group className="mb-3">
                <Form.Label>Risk Profile</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  name="riskProfile"
                  value={formData.riskProfile}
                  onChange={handleChange}
                  placeholder="What are your biggest concerns (e.g., drought, pests, price crash, theft)?"
                />
              </Form.Group>
            </Col>
            <Col md={12}>
              <Form.Group className="mb-3">
                <Form.Label>Other Considerations</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  name="otherConsiderations"
                  value={formData.otherConsiderations}
                  onChange={handleChange}
                  placeholder="Any other factors to consider?"
                />
              </Form.Group>
            </Col>
          </Row>
          <div className="text-end">
            <Button onClick={handleSaveClick} disabled={isSaving}>
              {isSaving ? "Saving..." : "Save Assessment"}
            </Button>
          </div>
        </Form>
      </Card.Body>
    </Card>
  );
};

export default AssessmentForm;




# farm_management/models.py

from django.db import models
from django.utils import timezone
from django.core.exceptions import ValidationError
from accounts.models import Tenant, CustomUser

class Field(models.Model):
    """Represents a physical field or paddock on the farm."""
    name = models.CharField(max_length=100)
    size_acres = models.DecimalField(max_digits=10, decimal_places=2)
    tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE, related_name="fields")
    location = models.CharField(max_length=200, blank=True)
    soil_type = models.CharField(max_length=100, blank=True)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']
        verbose_name_plural = "Fields"

class CropPlanting(models.Model):
    """Represents a specific crop being grown in a specific field for a season."""
    STATUS_CHOICES = (
        ('planned', 'Planned'),
        ('planted', 'Planted'),
        ('growing', 'Growing'),
        ('harvested', 'Harvested'),
        ('failed', 'Failed'),
    )
    
    field = models.ForeignKey(Field, on_delete=models.CASCADE, related_name="crop_plantings")
    crop_name = models.CharField(max_length=100)
    variety = models.CharField(max_length=100, blank=True)
    planting_date = models.DateField()
    expected_harvest_date = models.DateField(null=True, blank=True)
    actual_harvest_date = models.DateField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='planned')
    is_active = models.BooleanField(default=True)
    tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE, related_name="crop_plantings")
    notes = models.TextField(blank=True)
    estimated_yield = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    yield_unit = models.CharField(max_length=20, blank=True, default='kg')

    def __str__(self):
        return f"{self.crop_name} in {self.field.name} ({self.planting_date})"

    def clean(self):
        """Validate date consistency."""
        if self.expected_harvest_date and self.planting_date > self.expected_harvest_date:
            raise ValidationError("Planting date cannot be after expected harvest date")
        
        if self.actual_harvest_date:
            if self.actual_harvest_date < self.planting_date:
                raise ValidationError("Actual harvest date cannot be before planting date")
            if self.expected_harvest_date and self.actual_harvest_date > self.expected_harvest_date:
                # This is a warning scenario rather than error
                pass

    def update_status(self):
        """Update planting status based on dates."""
        today = timezone.now().date()
        
        if self.actual_harvest_date:
            self.status = 'harvested'
        elif self.planting_date <= today:
            if self.is_active:
                self.status = 'growing'
            else:
                self.status = 'failed'
        else:
            self.status = 'planned'
        
        self.save()

    def save(self, *args, **kwargs):
        """Override save to include validation and auto-status update."""
        self.clean()
        super().save(*args, **kwargs)

    class Meta:
        ordering = ['-planting_date']
        verbose_name_plural = "Crop Plantings"

class LivestockType(models.Model):
    """Type of livestock (e.g., Dairy Cattle, Broilers, Goats)"""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    typical_lifespan = models.PositiveIntegerField(null=True, blank=True, help_text="In months")
    typical_weight = models.DecimalField(max_digits=6, decimal_places=2, null=True, blank=True, help_text="In kg")
    tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE, related_name="livestock_types")

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']
        verbose_name_plural = "Livestock Types"

class LivestockBatch(models.Model):
    """A batch or group of livestock"""
    STATUS_CHOICES = (
        ('active', 'Active'),
        ('sold', 'Sold'),
        ('slaughtered', 'Slaughtered'),
        ('deceased', 'Deceased'),
        ('transferred', 'Transferred'),
    )
    
    name = models.CharField(max_length=100)
    livestock_type = models.ForeignKey(LivestockType, on_delete=models.CASCADE, related_name="batches")
    quantity = models.PositiveIntegerField()
    date_acquired = models.DateField()
    date_sold = models.DateField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    location = models.ForeignKey(Field, on_delete=models.SET_NULL, null=True, blank=True, related_name="livestock_batches")
    purchase_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    sale_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE, related_name="livestock_batches")
    notes = models.TextField(blank=True)

    def __str__(self):
        return f"{self.name} ({self.livestock_type.name}) - {self.quantity} heads"

    def update_status(self):
        """Update batch status based on dates and quantities."""
        if self.quantity == 0:
            self.status = 'deceased' if not self.date_sold else 'sold'
        self.save()

    class Meta:
        ordering = ['-date_acquired']
        verbose_name_plural = "Livestock Batches"

class FarmEvent(models.Model):
    """
    The central model for logging all operational events.
    This is a flexible, generic event log.
    """
    EVENT_TYPES = (
        # Cropping Events
        ('fertilizer', 'Fertilizer Application'),
        ('pesticide', 'Pesticide Application'),
        ('herbicide', 'Herbicide Application'),
        ('irrigation', 'Irrigation'),
        ('scouting', 'Pest/Disease Sighting'),
        ('harvest', 'Harvest'),
        ('planting', 'Planting'),
        ('soil_test', 'Soil Test'),
        # Livestock Events
        ('treatment', 'Animal Treatment'),
        ('feeding', 'Feeding'),
        ('vaccination', 'Vaccination'),
        ('breeding', 'Breeding'),
        ('birth', 'Birth'),
        ('death', 'Death'),
        ('movement', 'Movement'),
        # General Events
        ('maintenance', 'Equipment Maintenance'),
        ('inspection', 'Farm Inspection'),
        ('weather', 'Weather Event'),
        ('other', 'Other Event'),
    )
    
    event_type = models.CharField(max_length=50, choices=EVENT_TYPES)
    event_date = models.DateTimeField(default=timezone.now)
    description = models.TextField()
    
    # Links to specific entities
    crop_planting = models.ForeignKey(
        CropPlanting, 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True, 
        related_name="events"
    )
    
    livestock_batch = models.ForeignKey(
        LivestockBatch, 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True, 
        related_name="events"
    )
    
    field = models.ForeignKey(
        Field,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="events"
    )
    
    # Event details
    event_data = models.JSONField(null=True, blank=True)
    quantity = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    unit = models.CharField(max_length=20, blank=True)
    cost = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    currency = models.CharField(max_length=3, default='USD')
    
    # Weather data
    temperature = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    rainfall = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    weather_notes = models.TextField(blank=True)
    
    # Metadata
    tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE, related_name="farm_events")
    created_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-event_date']
        verbose_name_plural = "Farm Events"

    def __str__(self):
        return f"{self.get_event_type_display()} on {self.event_date.date()}"
    
    def clean(self):
        """Validate that event is properly linked."""
        if not any([self.crop_planting, self.livestock_batch, self.field]):
            raise ValidationError("Event must be linked to at least one entity (crop, livestock, or field)")
        
        # Count how many entities are linked
        linked_entities = sum([bool(self.crop_planting), bool(self.livestock_batch), bool(self.field)])
        if linked_entities > 1:
            raise ValidationError("Event can only be linked to one entity at a time")
    
    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)
    
    def get_linked_entity(self):
        """Return the linked entity for display purposes."""
        if self.crop_planting:
            return self.crop_planting
        elif self.livestock_batch:
            return self.livestock_batch
        elif self.field:
            return self.field
        return None

class YieldRecord(models.Model):
    """Specific yield records for crops or livestock products"""
    RECORD_TYPES = (
        ('crop', 'Crop Yield'),
        ('livestock', 'Livestock Product'),
        ('other', 'Other'),
    )
    
    record_type = models.CharField(max_length=20, choices=RECORD_TYPES)
    crop_planting = models.ForeignKey(
        CropPlanting, 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True, 
        related_name="yield_records"
    )
    livestock_batch = models.ForeignKey(
        LivestockBatch, 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True, 
        related_name="yield_records"
    )
    harvest_date = models.DateField()
    quantity = models.DecimalField(max_digits=10, decimal_places=2)
    unit = models.CharField(max_length=50)
    quality_grade = models.CharField(max_length=50, blank=True)
    market_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    total_value = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    notes = models.TextField(blank=True)
    tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE, related_name="yield_records")
    recorded_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True)
    recorded_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-harvest_date']
        verbose_name_plural = "Yield Records"
    
    def __str__(self):
        if self.crop_planting:
            return f"{self.quantity} {self.unit} from {self.crop_planting}"
        elif self.livestock_batch:
            return f"{self.quantity} {self.unit} from {self.livestock_batch}"
        return f"Yield record: {self.quantity} {self.unit}"
    
    def save(self, *args, **kwargs):
        """Calculate total value if market price is provided."""
        if self.market_price and self.quantity:
            self.total_value = self.market_price * self.quantity
        
        # Set record type based on linked entity
        if self.crop_planting:
            self.record_type = 'crop'
        elif self.livestock_batch:
            self.record_type = 'livestock'
        else:
            self.record_type = 'other'
            
        super().save(*args, **kwargs)

class ExpenseRecord(models.Model):
    """Track farm expenses"""
    CATEGORIES = (
        ('seed', 'Seeds'),
        ('fertilizer', 'Fertilizer'),
        ('pesticide', 'Pesticide'),
        ('feed', 'Animal Feed'),
        ('vet', 'Veterinary'),
        ('labor', 'Labor'),
        ('fuel', 'Fuel'),
        ('maintenance', 'Maintenance'),
        ('equipment', 'Equipment'),
        ('other', 'Other'),
    )
    
    category = models.CharField(max_length=20, choices=CATEGORIES)
    description = models.TextField()
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='USD')
    date_incurred = models.DateField()
    related_event = models.ForeignKey(
        FarmEvent, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        related_name="expenses"
    )
    crop_planting = models.ForeignKey(
        CropPlanting,
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    livestock_batch = models.ForeignKey(
        LivestockBatch,
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE, related_name="expenses")
    recorded_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True)
    recorded_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-date_incurred']
        verbose_name_plural = "Expense Records"
    
    def __str__(self):
        return f"{self.category}: {self.amount} {self.currency} on {self.date_incurred}"




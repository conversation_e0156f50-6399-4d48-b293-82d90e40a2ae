# planning/views.py

from rest_framework import viewsets, permissions, status, generics
from rest_framework.decorators import action
from rest_framework.views import APIView
from rest_framework.response import Response
from .models import EnterprisePlan, PlanItem, EnterpriseTemplate

from rest_framework.exceptions import NotFound
from decimal import Decimal

from .models import (
    EnterprisePlan, PlanItem, EnterpriseTemplate, PlanAssumption,
    PartialBudget, PartialBudgetItem
)
from .serializers import (
    EnterprisePlanSerializer, PlanItemSerializer, EnterpriseTemplateSerializer,
    PlanAssumptionSerializer, PartialBudgetSerializer, PartialBudgetItemSerializer,
    PigProductionAssumptionsSerializer, EggProductionAssumptionsSerializer, 
    DairyLivestockAssumptionsSerializer, BeefLivestockAssumptionsSerializer, 
    SheepProductionAssumptionsSerializer, ForageProductionAssumptionsSerializer, 
    HorticultureCropAssumptionsSerializer, FieldCropAssumptionsSerializer, 
    TilapiaProductionAssumptionsSerializer, ConservancyAssumptionsSerializer, 
    PlantationCropAssumptionsSerializer, IrrigationAssumptionsSerializer

)
from core.permissions import <PERSON>Ex<PERSON>User

from rest_framework.permissions import IsAuthenticated 
from core.permissions import IsExpertUser, IsFarmerUser


# --- HELPER FUNCTIONS FOR ANALYSIS ---
# By defining these at the top level, any view below can access them.

def get_pig_production_analysis_data(plan):
    try:
        assumptions = plan.pig_assumptions
    except PigProductionAssumptions.DoesNotExist:
        return {"error": "Pig production assumptions not found for this plan."}

    num_sows = Decimal(assumptions.number_of_sows)
    pwsy = Decimal(assumptions.pigs_weaned_per_sow_per_year)
    sale_weight = Decimal(assumptions.sale_weight_kg)
    post_weaning_mortality = Decimal(assumptions.post_weaning_mortality_percent) / 100
    sale_price_per_kg = Decimal(assumptions.sale_price_per_kg)
    
    total_variable_costs = Decimal('95500.00') # Placeholder from blueprint
    total_fixed_costs = Decimal('14000.00')    # Placeholder from blueprint
    total_cost_of_production_per_sow = total_variable_costs + total_fixed_costs

    pigs_sold_per_sow = pwsy * (1 - post_weaning_mortality)
    total_kg_sold_per_sow = pigs_sold_per_sow * sale_weight
    revenue_from_porkers = total_kg_sold_per_sow * sale_price_per_kg
    revenue_from_culls = Decimal('4500.00') # Placeholder
    total_revenue_per_sow = revenue_from_porkers + revenue_from_culls

    net_profit_per_sow = total_revenue_per_sow - total_cost_of_production_per_sow
    roi = (net_profit_per_sow / total_cost_of_production_per_sow * 100) if total_cost_of_production_per_sow > 0 else Decimal('0.0')
    cost_per_kg = total_cost_of_production_per_sow / total_kg_sold_per_sow if total_kg_sold_per_sow > 0 else Decimal('0.0')

    return {
        'model_type': 'pig_production',
        'analysis_per_sow_per_year': {
            'total_cost_of_production': total_cost_of_production_per_sow,
            'total_revenue': total_revenue_per_sow,
            'net_profit': net_profit_per_sow,
            'roi_percent': roi,
            'cost_of_production_per_kg': cost_per_kg,
        },
        'total_farm_projection': {
            'total_revenue': total_revenue_per_sow * num_sows,
            'total_cost': total_cost_of_production_per_sow * num_sows,
            'net_profit': net_profit_per_sow * num_sows,
        }
    }

def get_generic_analysis_data(plan):
    plan_items = plan.plan_items.all()
    total_income = sum(item.projected_amount for item in plan_items if item.item_type == 'income')
    total_cost = sum(item.projected_amount for item in plan_items if item.item_type in ['variable', 'fixed'])
    net_profit = total_income - total_cost
    roi = (net_profit / total_cost * 100) if total_cost > 0 else Decimal('0.0')
    return {
        'model_type': 'generic',
        'p_and_l': {'total_revenue': total_income, 'total_cost': total_cost, 'net_profit': net_profit},
        'kpis': {'roi_percent': roi}
    }

# --- API VIEWS ---

class EnterpriseTemplateViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = EnterpriseTemplate.objects.all().order_by('name')
    serializer_class = EnterpriseTemplateSerializer
    permission_classes = [permissions.IsAuthenticated, IsExpertUser]

class EnterprisePlanViewSet(viewsets.ModelViewSet):
    serializer_class = EnterprisePlanSerializer
    permission_classes = [IsAuthenticated]  # Allow all authenticated users for testing

    def get_queryset(self):
        user = self.request.user
        print(f"🔍 EnterprisePlanViewSet: User {user.username} (role: {user.role}) requesting plan details")

        # For testing: if admin user, show all plans
        if user.role == 'admin':
            plans = EnterprisePlan.objects.all()
            print(f"🔍 Admin user - returning all {plans.count()} plans")
            return plans
        # If expert user, show plans they created
        elif user.role == 'expert':
            plans = EnterprisePlan.objects.filter(expert=user)
            print(f"🔍 Expert user - returning {plans.count()} plans where expert=user")
            return plans
        # If farmer user, show plans created for them
        elif user.role == 'farmer':
            plans = EnterprisePlan.objects.filter(farmer=user)
            print(f"🔍 Farmer user - returning {plans.count()} plans where farmer=user")
            return plans
        else:
            print(f"🔍 Unknown role - returning no plans")
            return EnterprisePlan.objects.none()
    
    @action(detail=False, methods=['post'], url_path='create-from-template')
    def create_from_template(self, request, *args, **kwargs):
        template_id = request.data.get('template_id')
        plan_name = request.data.get('plan_name')
        farmer_id = request.data.get('farmer_id')
        try:
            template = EnterpriseTemplate.objects.get(id=template_id)
            plan = EnterprisePlan.objects.create(
                name=plan_name, farmer_id=farmer_id, expert=request.user,
                tenant=request.user.tenant, based_on_template=template
            )
            for item in template.template_items.all():
                PlanItem.objects.create(
                    plan=plan, item_type=item.item_type,
                    category=item.category, item_name=item.item_name
                )
            return Response(EnterprisePlanSerializer(plan).data)
        except EnterpriseTemplate.DoesNotExist:
            return Response({"error": "Template not found."}, status=status.HTTP_404_NOT_FOUND)

class FarmerEnterprisePlanViewSet(viewsets.ReadOnlyModelViewSet):
    """
    An endpoint for a Farmer to view Enterprise Plans created for them.
    """
    serializer_class = EnterprisePlanSerializer
    permission_classes = [IsAuthenticated] # Allow all authenticated users for testing

    def list(self, request, *args, **kwargs):
        """
        Override list method to add debugging and potentially create test data
        """
        user = request.user
        print(f"🔍 FarmerEnterprisePlanViewSet: User {user.username} (role: {user.role}) requesting plans")

        # Get the queryset
        queryset = self.get_queryset()
        print(f"🔍 Queryset returned {queryset.count()} plans")

        # If no plans exist, let's check what we have in the database
        if queryset.count() == 0:
            print("🔍 No plans found, checking database...")
            all_plans = EnterprisePlan.objects.all()
            print(f"🔍 Total plans in database: {all_plans.count()}")

            from accounts.models import CustomUser
            all_farmers = CustomUser.objects.filter(role='farmer')
            print(f"🔍 Total farmers in database: {all_farmers.count()}")

            all_experts = CustomUser.objects.filter(role='expert')
            print(f"🔍 Total experts in database: {all_experts.count()}")

            templates = EnterpriseTemplate.objects.all()
            print(f"🔍 Total templates in database: {templates.count()}")

            # For testing: Create a test enterprise plan if none exist
            if all_plans.count() == 0 and all_farmers.count() > 0 and all_experts.count() > 0:
                print("🔍 Creating test enterprise plan...")
                try:
                    farmer = all_farmers.first()
                    expert = all_experts.first()

                    test_plan = EnterprisePlan.objects.create(
                        name="Test Enterprise Plan",
                        farmer=farmer,
                        expert=expert,
                        tenant=user.tenant,
                        description="This is a test enterprise plan created for testing purposes.",
                        status="draft"
                    )
                    print(f"✅ Created test plan: {test_plan.name} (ID: {test_plan.id})")

                    # Update queryset to include the new plan
                    queryset = self.get_queryset()
                    print(f"🔍 Updated queryset now has {queryset.count()} plans")

                except Exception as e:
                    print(f"❌ Error creating test plan: {e}")

        # Continue with normal serialization
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def get_queryset(self):
        """
        Filters plans to only those where the 'farmer' field
        matches the user making the request.
        """
        user = self.request.user

        # For testing: if admin user, show plans for any farmer; if farmer, show their plans
        if user.role == 'admin':
            # Show plans for any farmer user in the same tenant
            from accounts.models import CustomUser
            farmers = CustomUser.objects.filter(role='farmer', tenant=user.tenant)

            if farmers.exists():
                # Get plans for all farmers in the tenant
                plans = EnterprisePlan.objects.filter(farmer__in=farmers)
                return plans
            else:
                return EnterprisePlan.objects.none()

        return EnterprisePlan.objects.filter(farmer=user)

class PlanItemViewSet(viewsets.ModelViewSet):
    serializer_class = PlanItemSerializer
    permission_classes = [permissions.IsAuthenticated, IsExpertUser]
    def get_queryset(self):
        return PlanItem.objects.filter(plan__tenant=self.request.user.tenant)

class PigProductionAssumptionsView(generics.RetrieveUpdateAPIView):
    serializer_class = PigProductionAssumptionsSerializer
    permission_classes = [permissions.IsAuthenticated, IsExpertUser]
    lookup_field = 'plan_id'
    lookup_url_kwarg = 'plan_pk'
    def get_queryset(self):
        return PigProductionAssumptions.objects.filter(plan__tenant=self.request.user.tenant)

class PlanAnalysisView(APIView):
    permission_classes = [permissions.IsAuthenticated, IsExpertUser]
    def get(self, request, pk=None, *args, **kwargs):
        try:
            plan = EnterprisePlan.objects.get(pk=pk, tenant=request.user.tenant)
        except EnterprisePlan.DoesNotExist:
            return Response({"error": "Plan not found."}, status=404)
        if plan.based_on_template.model_type == 'pig_production':
            analysis_data = get_pig_production_analysis_data(plan)
        else:
            analysis_data = get_generic_analysis_data(plan)
        if "error" in analysis_data:
            return Response(analysis_data, status=400)
        return Response(analysis_data)



class PlanAssumptionViewSet(viewsets.ModelViewSet):
    """ An endpoint to get or update the single assumptions object for a plan. """
    serializer_class = PlanAssumptionSerializer
    permission_classes = [permissions.IsAuthenticated, IsExpertUser]
    
    def get_queryset(self):
        return PlanAssumption.objects.filter(plan__tenant=self.request.user.tenant)
        
    def get_object(self):
        plan_id = self.kwargs.get('plan_pk')
        try:
            assumptions, created = PlanAssumption.objects.get_or_create(plan_id=plan_id)
            return assumptions
        except EnterprisePlan.DoesNotExist:
            raise NotFound('A plan with this ID does not exist.')



class PartialBudgetViewSet(viewsets.ModelViewSet):
    """
    Endpoint for an Expert to manage Partial Budgets for their farmers.
    """
    serializer_class = PartialBudgetSerializer
    permission_classes = [permissions.IsAuthenticated, IsExpertUser]

    def get_queryset(self):
        # An expert can only see partial budgets within their own tenant
        return PartialBudget.objects.filter(tenant=self.request.user.tenant)

    def get_serializer_context(self):
        # Pass request to the serializer to help with filtering farmers
        return {'request': self.request}

    def perform_create(self, serializer):
        # Assign the expert and their tenant automatically when a new budget is created
        serializer.save(expert=self.request.user, tenant=self.request.user.tenant)

class PartialBudgetItemViewSet(viewsets.ModelViewSet):
    """
    Endpoint for managing line items (effects) within a Partial Budget.
    """
    serializer_class = PartialBudgetItemSerializer
    permission_classes = [permissions.IsAuthenticated, IsExpertUser]

    def get_queryset(self):
        # An expert can only see items belonging to budgets within their tenant
        return PartialBudgetItem.objects.filter(budget__tenant=self.request.user.tenant)

    def perform_create(self, serializer):
        # Ensure the budget belongs to the current user's tenant
        budget_id = serializer.validated_data['budget'].id
        try:
            budget = PartialBudget.objects.get(id=budget_id, tenant=self.request.user.tenant)
            serializer.save()
        except PartialBudget.DoesNotExist:
            raise NotFound('Budget not found or access denied.')
    




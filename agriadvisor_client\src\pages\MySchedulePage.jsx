// src/pages/MySchedulePage.jsx (or your equivalent file)

import React, { useState, useEffect } from "react";
import { Container, Spinner, <PERSON><PERSON> } from "react-bootstrap";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import { getMySchedule } from "../services/bookingService";

const MySchedulePage = () => {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  // --- THIS IS THE CORRECTED, CRASH-PROOF FUNCTION ---
  const formatEvents = (bookings) => {
    // Safety Check: If bookings is not an array, return an empty one to prevent crashes.
    if (!Array.isArray(bookings)) {
      return [];
    }

    return bookings.map((booking) => ({
      id: booking.id,
      // Use optional chaining (?.) and fallbacks (||) to safely access nested data
      title: `${booking.service?.name || "Service"} with ${booking.farmer?.first_name || "Farmer"}`,
      start: booking.booking_time,
      backgroundColor: booking.status === "completed" ? "#198754" : "#0d6efd", // Green for completed, blue for others
      borderColor: booking.status === "completed" ? "#198754" : "#0d6efd",
      extendedProps: {
        farmer: `${booking.farmer?.first_name || ""} ${booking.farmer?.last_name || ""}`,
        expert: `${booking.expert?.first_name || ""} ${booking.expert?.last_name || ""}`,
        service: booking.service?.name || "N/A",
        status: booking.status,
        pre_consultation_data: booking.pre_consultation_data,
        report: booking.report,
      },
    }));
  };
  // --- END OF CORRECTION ---

  useEffect(() => {
    const fetchSchedule = async () => {
      try {
        setLoading(true); // Set loading at the start
        const bookingsData = await getMySchedule();
        const formattedEvents = formatEvents(bookingsData);
        setEvents(formattedEvents);
      } catch (err) {
        console.error("MySchedulePage: Error fetching schedule", err);
        setError("Failed to load your schedule.");
      } finally {
        setLoading(false);
      }
    };
    fetchSchedule();
  }, []); // Empty array ensures this runs only once

  if (loading)
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" />
      </Container>
    );
  if (error)
    return (
      <Container className="mt-4">
        <Alert variant="danger">{error}</Alert>
      </Container>
    );

  return (
    <Container fluid>
      <h2 className="mb-4">My Full Schedule</h2>
      {/* It's good practice to wrap the calendar in a div to control its height */}
      <div>
        <FullCalendar
          plugins={[dayGridPlugin, timeGridPlugin]}
          initialView="timeGridWeek"
          headerToolbar={{
            left: "prev,next today",
            center: "title",
            right: "dayGridMonth,timeGridWeek,timeGridDay",
          }}
          events={events}
          height="auto" // 'auto' is often better for fitting into Bootstrap layouts
        />
      </div>
    </Container>
  );
};
export default MySchedulePage;



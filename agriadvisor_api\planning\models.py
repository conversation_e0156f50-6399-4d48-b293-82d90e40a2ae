# planning/models.py

from django.db import models
from accounts.models import CustomUser, Tenant

# --- MASTER TEMPLATE MODELS (Managed by Superadmin) ---
class EnterpriseTemplate(models.Model):
    MODEL_TYPE_CHOICES = (
        ('generic', 'Generic Budget'), ('pig_production', 'Pig Production Model'),
        ('egg_production', 'Egg Production Model'), ('dairy_livestock', 'Dairy Livestock Model'),
        ('beef_livestock', 'Beef Livestock Model'), ('broiler_production', 'Broiler Production Model'),
        ('tilapia_production', 'Tilapia Production Model'), ('sheep_production', 'Sheep Production Model'),
        ('forage_production', 'Forage Production Model'), ('horticulture_crop', 'Horticultural Crop Model'),
        ('field_crop', 'Field Crop Model'), ('conservancy', 'Wildlife Conservancy Model'),
        ('plantation_crop', 'Plantation Crop Model'), ('irrigation_investment', 'Irrigation Investment Model'),
        ('partial_budget', 'Partial Budget Analysis'),
    )
    name = models.Char<PERSON><PERSON>(max_length=100, unique=True)
    description = models.TextField(blank=True)
    icon = models.CharField(max_length=50, blank=True)
    model_type = models.CharField(max_length=50, choices=MODEL_TYPE_CHOICES, default='generic')
    def __str__(self): return self.name

class TemplateItem(models.Model):
    # This represents a predefined line item for GENERIC templates.
    ITEM_TYPE_CHOICES = (('income', 'Source of Income'), ('variable', 'Variable Cost'), ('fixed', 'Fixed Cost'))
    template = models.ForeignKey(EnterpriseTemplate, on_delete=models.CASCADE, related_name="template_items")
    item_type = models.CharField(max_length=10, choices=ITEM_TYPE_CHOICES)
    category = models.CharField(max_length=100)
    item_name = models.CharField(max_length=100)
    default_unit = models.CharField(max_length=20, default="Unit")
    def __str__(self): return f"{self.template.name} - {self.item_name}"

# --- USER-CREATED DATA MODELS ---
class EnterprisePlan(models.Model):
    name = models.CharField(max_length=255)
    farmer = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name="enterprise_plans", limit_choices_to={'role': 'farmer'})
    expert = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name="managed_plans", limit_choices_to={'role': 'expert'})
    tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE, related_name="enterprise_plans")
    based_on_template = models.ForeignKey(EnterpriseTemplate, on_delete=models.PROTECT)
    assessment_data = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    def __str__(self): return self.name

class PlanItem(models.Model):
    # This stores the user's actual financial data for a generic plan's line items.
    plan = models.ForeignKey(EnterprisePlan, on_delete=models.CASCADE, related_name="plan_items")
    template_item = models.ForeignKey(TemplateItem, on_delete=models.SET_NULL, null=True, blank=True)
    item_type = models.CharField(max_length=10)
    category = models.CharField(max_length=100)
    item_name = models.CharField(max_length=100)
    quantity = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    price_per_unit = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    actual_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0.00, null=True, blank=True)
    @property
    def projected_amount(self): return self.quantity * self.price_per_unit

class PlanAssumption(models.Model):
    """
    Sheet 1 for a 'generic' budget. Stores all the variables.
    """
    plan = models.OneToOneField(EnterprisePlan, on_delete=models.CASCADE, related_name="assumptions")
    expected_yield = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    yield_unit = models.CharField(max_length=20, default="Units", blank=True)
    selling_price_per_unit = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    
    
# --- NOTE: TemplateItem and PlanItem models have been REMOVED ---
# They are replaced by the specialized Assumption models below.

# --- Specialized Assumption Models (The "Brains") ---


class PigProductionAssumptions(models.Model):
    """
    Stores the detailed productivity and price assumptions for a pig enterprise plan.
    """
    plan = models.OneToOneField(EnterprisePlan, on_delete=models.CASCADE, related_name="pig_assumptions")

    # Herd Structure
    number_of_sows = models.PositiveIntegerField(default=10)
    
    # Production Parameters
    farrowing_rate_percent = models.DecimalField(max_digits=5, decimal_places=2, default=85.0)
    litters_per_sow_per_year = models.DecimalField(max_digits=4, decimal_places=1, default=2.4)
    pigs_born_alive_per_litter = models.DecimalField(max_digits=4, decimal_places=1, default=13.0)
    pre_weaning_mortality_percent = models.DecimalField(max_digits=5, decimal_places=2, default=12.0)
    post_weaning_mortality_percent = models.DecimalField(max_digits=5, decimal_places=2, default=4.0)
    
    # Growth Parameters
    sale_weight_kg = models.DecimalField(max_digits=5, decimal_places=1, default=90.0)
    avg_daily_gain_g = models.DecimalField(max_digits=6, decimal_places=1, default=600.0)
    feed_conversion_ratio = models.DecimalField(max_digits=4, decimal_places=2, default=2.8)
    
    # Price Assumptions
    sale_price_per_kg = models.DecimalField(max_digits=10, decimal_places=2, default=300.00)
    sow_feed_price_per_bag = models.DecimalField(max_digits=10, decimal_places=2, default=3500.00)
    weaner_feed_price_per_bag = models.DecimalField(max_digits=10, decimal_places=2, default=4200.00)
    finisher_feed_price_per_bag = models.DecimalField(max_digits=10, decimal_places=2, default=3800.00)
    feed_bag_weight_kg = models.DecimalField(max_digits=5, decimal_places=1, default=70.0)

    # Actuals
    actual_farrowing_rate_percent = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    actual_pigs_weaned_per_sow_per_year = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    actual_sale_weight_kg = models.DecimalField(max_digits=5, decimal_places=1, null=True, blank=True)
    actual_sale_price_per_kg = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_feed_conversion_ratio = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True)

    # --- KEY CALCULATED PROPERTIES ---
    @property
    def pigs_weaned_per_litter(self):
        return self.pigs_born_alive_per_litter * (1 - (self.pre_weaning_mortality_percent / 100))
    
    @property
    def pigs_weaned_per_sow_per_year(self):
        # The crucial PWSY metric
        return self.litters_per_sow_per_year * self.pigs_weaned_per_litter

    @property
    def pigs_sold_per_sow_per_year(self):
        return self.pigs_weaned_per_sow_per_year * (1 - (self.post_weaning_mortality_percent / 100))

    @property
    def total_kg_sold_per_sow_per_year(self):
        return self.pigs_sold_per_sow_per_year * self.sale_weight_kg
    
    @property
    def total_revenue_per_sow_per_year(self):
        return self.total_kg_sold_per_sow_per_year * self.sale_price_per_kg
    
    @property
    def total_feed_required_per_sow_per_year(self):
        return self.total_kg_sold_per_sow_per_year * self.feed_conversion_ratio
    
    @property
    def total_cost_per_sow_per_year(self):
        return (self.total_feed_required_per_sow_per_year * self.sow_feed_price_per_bag) / self.feed_bag_weight_kg
    
    @property
    def total_profit_per_sow_per_year(self):
        return self.total_revenue_per_sow_per_year - self.total_cost_per_sow_per_year
    
    @property
    def roi(self):
        if self.total_cost_per_sow_per_year == 0:
            return 0
        return (self.total_profit_per_sow_per_year / self.total_cost_per_sow_per_year) * 100


class EggProductionAssumptions(models.Model):
    """
    Stores the detailed productivity and price assumptions for an egg production (layer) plan.
    """
    plan = models.OneToOneField(EnterprisePlan, on_delete=models.CASCADE, related_name="egg_assumptions")

    # Flock Size
    flock_size = models.PositiveIntegerField(default=1000)
    
    # Production Cycle
    rearing_period_weeks = models.PositiveIntegerField(default=18)
    laying_period_weeks = models.PositiveIntegerField(default=52)

    # Performance Parameters
    avg_hen_housed_production_percent = models.DecimalField(max_digits=5, decimal_places=2, default=85.0)
    avg_egg_weight_g = models.DecimalField(max_digits=5, decimal_places=1, default=62.0)
    mortality_during_lay_percent = models.DecimalField(max_digits=5, decimal_places=2, default=5.0)
    avg_feed_intake_g_per_bird_day = models.DecimalField(max_digits=6, decimal_places=1, default=120.0)

    # Price Assumptions
    sale_price_per_tray = models.DecimalField(max_digits=10, decimal_places=2, default=300.00)
    day_old_chick_price = models.DecimalField(max_digits=10, decimal_places=2, default=120.00)
    starter_grower_feed_price_per_bag = models.DecimalField(max_digits=10, decimal_places=2, default=3800.00)
    layer_mash_price_per_bag = models.DecimalField(max_digits=10, decimal_places=2, default=3500.00)
    spent_hen_price = models.DecimalField(max_digits=10, decimal_places=2, default=200.00)
    feed_bag_weight_kg = models.DecimalField(max_digits=5, decimal_places=1, default=70.0)

    # Actuals
    actual_avg_hen_housed_production_percent = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    actual_mortality_during_lay_percent = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    actual_sale_price_per_tray = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_feed_conversion_ratio = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True)
    
    # You can add more detailed fields from the assessment here if needed
    # e.g., rearing_mortality_percent, vaccination_cost_per_bird, etc.

    # --- KEY CALCULATED PROPERTIES (for display and further calculation) ---
    @property
    def total_eggs_per_hen_housed(self):
        # (laying period in days / 7) * avg_production_rate
        return (self.laying_period_weeks * 7) * (self.avg_hen_housed_production_percent / 100)
    
    @property
    def total_trays_produced(self):
        return (self.flock_size * self.total_eggs_per_hen_housed) / 30

    @property
    def surviving_hens_for_sale(self):
        return self.flock_size * (1 - (self.mortality_during_lay_percent / 100))
    
    @property
    def feed_conversion_ratio(self):
        if self.avg_feed_intake_g_per_bird_day == 0:
            return 0
        return (self.avg_egg_weight_g * 1000) / self.avg_feed_intake_g_per_bird_day
    
    @property
    def total_feed_required_kg(self):
        return (self.flock_size * self.avg_feed_intake_g_per_bird_day * (self.rearing_period_weeks + self.laying_period_weeks)) / 1000
    
    @property
    def total_revenue_per_year(self):
        return self.total_trays_produced * self.sale_price_per_tray
    
    @property
    def total_cost_per_year(self):
        # Placeholder, actual cost calculation needs to be implemented
        return 0
    
    @property
    def total_profit_per_year(self):
        return self.total_revenue_per_year - self.total_cost_per_year
    
    @property
    def roi(self):
        if self.total_cost_per_year == 0:
            return 0
        return (self.total_profit_per_year / self.total_cost_per_year) * 100
    
    @property
    def break_even_feed_conversion_ratio(self):
        if self.avg_feed_intake_g_per_bird_day == 0:
            return 0
        return (self.avg_egg_weight_g * 1000) / self.avg_feed_intake_g_per_bird_day
    
    @property
    def break_even_avg_feed_intake_g_per_bird_day(self):
        if self.break_even_feed_conversion_ratio == 0:
            return 0
        return (self.avg_egg_weight_g * 1000) / self.break_even_feed_conversion_ratio
    
    @property
    def break_even_avg_egg_weight_g(self):
        if self.break_even_feed_conversion_ratio == 0:
            return 0
        return (self.break_even_feed_conversion_ratio * self.avg_feed_intake_g_per_bird_day) / 1000
    
    @property
    def break_even_sale_price_per_tray(self):
        if self.total_trays_produced == 0:
            return 0
        return self.total_cost_per_year / self.total_trays_produced
    
    @property
    def break_even_flock_size(self):
        if self.break_even_sale_price_per_tray == 0:
            return 0
        return self.total_cost_per_year / self.break_even_sale_price_per_tray
    
    @property
    def break_even_avg_hen_housed_production_percent(self):
        if self.total_eggs_per_hen_housed == 0:
            return 0
        return (self.total_eggs_per_hen_housed / (self.rearing_period_weeks + self.laying_period_weeks)) * 100
    
    @property
    def break_even_mortality_during_lay_percent(self):
        if self.surviving_hens_for_sale == 0:
            return 0
        return (self.flock_size - self.surviving_hens_for_sale) / self.flock_size * 100


    # Add more calculated properties for total feed consumption, etc.


class DairyLivestockAssumptions(models.Model):
    """
    Stores the detailed productivity and price assumptions for a Dairy enterprise plan.
    """
    plan = models.OneToOneField(EnterprisePlan, on_delete=models.CASCADE, related_name="dairy_assumptions")

    # Herd Structure
    number_of_milking_cows = models.PositiveIntegerField(default=10)
    
    # Production Parameters
    avg_milk_yield_liters_per_day = models.DecimalField(max_digits=5, decimal_places=1, default=15.0)
    lactation_days = models.PositiveIntegerField(default=305)
    calving_interval_months = models.DecimalField(max_digits=4, decimal_places=1, default=13.0)
    calf_mortality_percent = models.DecimalField(max_digits=5, decimal_places=2, default=5.0)
    
    # Price Assumptions
    price_per_liter_milk = models.DecimalField(max_digits=10, decimal_places=2, default=50.00)
    cull_cow_price_per_kg = models.DecimalField(max_digits=10, decimal_places=2, default=120.00)
    heifer_sale_price = models.DecimalField(max_digits=10, decimal_places=2, default=40000.00)
    
    # Input Costs (can be expanded)
    concentrate_price_per_bag = models.DecimalField(max_digits=10, decimal_places=2, default=2500.00)
    feed_bag_weight_kg = models.DecimalField(max_digits=5, decimal_places=1, default=70.0)

    # Actuals
    actual_avg_milk_yield_liters_per_day = models.DecimalField(max_digits=5, decimal_places=1, null=True, blank=True)
    actual_price_per_liter_milk = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_feed_conversion_ratio = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True)

    # --- KEY CALCULATED PROPERTIES ---
    @property
    def total_milk_production_liters(self):
        return self.number_of_milking_cows * self.avg_milk_yield_liters_per_day * self.lactation_days

    @property
    def total_feed_required_kg(self):
        return self.total_milk_production_liters * self.feed_conversion_ratio
    
    @property
    def total_revenue_per_year(self):
        return self.total_milk_production_liters * self.price_per_liter_milk
    
    @property
    def total_cost_per_year(self):
        # Placeholder, actual cost calculation needs to be implemented
        return 0
    
    @property
    def total_profit_per_year(self):
        return self.total_revenue_per_year - self.total_cost_per_year
    
    @property
    def roi(self):
        if self.total_cost_per_year == 0:
            return 0
        return (self.total_profit_per_year / self.total_cost_per_year) * 100
    
    @property
    def feed_conversion_ratio(self):
        if self.avg_daily_gain_g == 0:
            return 0
        return (self.weaning_weight_kg * 1000) / (self.avg_daily_gain_g * 365)
    
    @property
    def break_even_price_per_liter(self):
        if self.total_milk_production_liters == 0:
            return 0
        return self.total_cost_per_year / self.total_milk_production_liters
    
    @property
    def break_even_avg_milk_yield_liters_per_day(self):
        if self.total_milk_production_liters == 0:
            return 0
        return self.total_cost_per_year / (self.number_of_milking_cows * self.lactation_days)
    
    @property
    def break_even_feed_conversion_ratio(self):
        if self.avg_daily_gain_g == 0:
            return 0
        return (self.weaning_weight_kg * 1000) / (self.break_even_avg_milk_yield_liters_per_day * 365)


class BeefLivestockAssumptions(models.Model):
    """
    Stores detailed assumptions for a meat production enterprise (beef, goat, sheep).
    """
    plan = models.OneToOneField(EnterprisePlan, on_delete=models.CASCADE, related_name="beef_assumptions")

    # Herd Structure
    number_of_breeding_females = models.PositiveIntegerField(default=20)
    
    # Production Parameters
    birth_rate_percent = models.DecimalField(max_digits=5, decimal_places=2, default=90.0) # Calving/lambing/kidding rate
    weaning_weight_kg = models.DecimalField(max_digits=5, decimal_places=1, default=150.0)
    avg_daily_gain_g = models.DecimalField(max_digits=6, decimal_places=1, default=800.0)
    sale_weight_kg = models.DecimalField(max_digits=6, decimal_places=1, default=450.0)
    mortality_rate_percent = models.DecimalField(max_digits=5, decimal_places=2, default=3.0)
    
    # Price Assumptions
    price_per_kg_live_weight = models.DecimalField(max_digits=10, decimal_places=2, default=250.00)

    # Actuals
    actual_birth_rate_percent = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    actual_weaning_weight_kg = models.DecimalField(max_digits=5, decimal_places=1, null=True, blank=True)
    actual_sale_weight_kg = models.DecimalField(max_digits=5, decimal_places=1, null=True, blank=True)
    actual_price_per_kg_live_weight = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_feed_conversion_ratio = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True)
    
    # --- KEY CALCULATED PROPERTIES ---
    @property
    def total_kg_sold_per_year(self):
        return self.number_of_breeding_females * (self.birth_rate_percent / 100) * self.sale_weight_kg
    
    @property
    def feed_conversion_ratio(self):
        if self.avg_daily_gain_g == 0:
            return 0
        return (self.weaning_weight_kg * 1000) / (self.avg_daily_gain_g * 365)
    
    @property
    def total_revenue_per_year(self):
        return self.total_kg_sold_per_year * self.price_per_kg_live_weight
    
    @property
    def total_cost_per_year(self):
        # Placeholder, actual cost calculation needs to be implemented
        return 0
    
    @property
    def total_profit_per_year(self):
        return self.total_revenue_per_year - self.total_cost_per_year
    
    @property
    def roi(self):
        if self.total_cost_per_year == 0:
            return 0
        return (self.total_profit_per_year / self.total_cost_per_year) * 100
    
    @property
    def break_even_price_per_kg(self):
        if self.total_kg_sold_per_year == 0:
            return 0
        return self.total_cost_per_year / self.total_kg_sold_per_year
    
    @property
    def break_even_sale_weight_kg(self):
        if self.break_even_price_per_kg == 0:
            return 0
        return self.total_cost_per_year / self.price_per_kg_live_weight
    
    @property
    def break_even_birth_rate_percent(self):
        if self.break_even_sale_weight_kg == 0:
            return 0
        return (self.break_even_sale_weight_kg / self.sale_weight_kg) * 100
    
    @property
    def break_even_feed_conversion_ratio(self):
        if self.avg_daily_gain_g == 0:
            return 0
        return (self.weaning_weight_kg * 1000) / (self.break_even_sale_weight_kg * 365)
    
    @property
    def break_even_avg_daily_gain_g(self):
        if self.break_even_feed_conversion_ratio == 0:
            return 0
        return (self.weaning_weight_kg * 1000) / (self.break_even_feed_conversion_ratio * 365)
    
    @property
    def break_even_weaning_weight_kg(self):
        if self.break_even_avg_daily_gain_g == 0:
            return 0
        return (self.break_even_avg_daily_gain_g * 365) / 1000
    
    @property
    def break_even_mortality_rate_percent(self):
        if self.break_even_sale_weight_kg == 0:
            return 0
        return (self.sale_weight_kg - self.break_even_sale_weight_kg) / self.sale_weight_kg * 100
    
    @property
    def break_even_number_of_breeding_females(self):
        if self.break_even_sale_weight_kg == 0:
            return 0
        return self.total_kg_sold_per_year / self.break_even_sale_weight_kg


class BroilerProductionAssumptions(models.Model):
    """
    Stores the detailed productivity and price assumptions for a Broiler enterprise plan.
    This model is based on a PER BATCH analysis.
    """
    plan = models.OneToOneField(EnterprisePlan, on_delete=models.CASCADE, related_name="broiler_assumptions")

    # Batch Parameters
    batch_size = models.PositiveIntegerField(default=100)
    growing_period_days = models.PositiveIntegerField(default=42)
    target_sale_weight_kg = models.DecimalField(max_digits=4, decimal_places=2, default=2.8)
    expected_mortality_percent = models.DecimalField(max_digits=5, decimal_places=2, default=5.0)

    # Performance Parameters
    feed_conversion_ratio = models.DecimalField(max_digits=4, decimal_places=2, default=1.8)

    # Price Assumptions
    sale_price_per_kg_live_weight = models.DecimalField(max_digits=10, decimal_places=2, default=350.00)
    day_old_chick_price = models.DecimalField(max_digits=10, decimal_places=2, default=110.00)
    avg_feed_price_per_bag = models.DecimalField(max_digits=10, decimal_places=2, default=3900.00)
    feed_bag_weight_kg = models.DecimalField(max_digits=5, decimal_places=1, default=70.0)

    # Actuals
    actual_sale_price_per_kg = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_feed_conversion_ratio = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True)
    
    
    # --- KEY CALCULATED PROPERTIES (for display and further calculation) ---
    @property
    def expected_number_sold(self):
        return self.batch_size * (1 - (self.expected_mortality_percent / 100))

    @property
    def total_feed_per_bird_kg(self):
        return self.target_sale_weight_kg * self.feed_conversion_ratio

    @property
    def total_feed_for_batch_kg(self):
        return self.batch_size * self.total_feed_per_bird_kg

    @property
    def total_live_weight_sold_kg(self):
        return self.expected_number_sold * self.target_sale_weight_kg
    
    @property
    def avg_daily_gain_g(self):
        if self.growing_period_days == 0:
            return 0
        return (self.target_sale_weight_kg * 1000) / self.growing_period_days

    @property
    def european_production_efficiency_factor(self):
        liveability_percent = 100 - self.expected_mortality_percent
        if self.growing_period_days == 0 or self.feed_conversion_ratio == 0:
            return 0
        # Formula: (Liveability % * Live Weight (kg) * 100) / (Age (days) * FCR)
        epef = (liveability_percent * self.target_sale_weight_kg * 100) / (self.growing_period_days * self.feed_conversion_ratio)
        return epef


class TilapiaProductionAssumptions(models.Model):
    """
    Stores the detailed productivity and price assumptions for a Tilapia enterprise plan.
    This model is based on a PER BATCH/CYCLE analysis for a given pond size.
    """
    plan = models.OneToOneField(EnterprisePlan, on_delete=models.CASCADE, related_name="tilapia_assumptions")

    # Pond Parameters
    pond_size_m2 = models.DecimalField(max_digits=10, decimal_places=2, default=100.0)
    stocking_density_per_m2 = models.PositiveIntegerField(default=3)
    production_cycle_months = models.PositiveIntegerField(default=6)

    # Performance Parameters
    target_harvest_size_g = models.PositiveIntegerField(default=500)
    expected_survival_rate_percent = models.DecimalField(max_digits=5, decimal_places=2, default=85.0)
    feed_conversion_ratio = models.DecimalField(max_digits=4, decimal_places=2, default=1.7)

    # Price Assumptions
    sale_price_per_kg = models.DecimalField(max_digits=10, decimal_places=2, default=400.00)
    fingerling_price_each = models.DecimalField(max_digits=10, decimal_places=2, default=15.00)
    avg_feed_price_per_kg = models.DecimalField(max_digits=10, decimal_places=2, default=120.00)
    
    # Actuals
    actual_sale_price_per_kg = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_feed_conversion_ratio = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True)
    actual_productivity_kg_per_m2 = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_feed_cost_per_kg = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_cost_per_kg = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_roi = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_payback_period_years = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_feed_conversion_ratio = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True)
    actual_break_even_productivity_kg_per_m2 = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_feed_cost_per_kg = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_cost_per_kg = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_payback_period_years = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_roi = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_sale_price_per_kg = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_fingerling_price_each = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_avg_feed_price_per_kg = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_avg_feed_price_per_bag = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_avg_feed_bag_weight_kg = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    
    # --- KEY CALCULATED PROPERTIES ---
    @property
    def number_of_fingerlings_stocked(self):
        return self.pond_size_m2 * self.stocking_density_per_m2
    
    @property
    def expected_number_harvested(self):
        return self.number_of_fingerlings_stocked * (self.expected_survival_rate_percent / 100)

    @property
    def total_biomass_harvested_kg(self):
        return self.expected_number_harvested * (self.target_harvest_size_g / 1000)

    @property
    def total_feed_required_kg(self):
        # FCR is based on weight gain. Initial fingerling weight is negligible for this model.
        return self.total_biomass_harvested_kg * self.feed_conversion_ratio
        
    @property
    def productivity_kg_per_m2(self):
        if self.pond_size_m2 == 0:
            return 0
        return self.total_biomass_harvested_kg / self.pond_size_m2
    
    @property
    def total_revenue_per_cycle(self):
        return self.total_biomass_harvested_kg * self.sale_price_per_kg
    
    @property
    def total_cost_per_cycle(self):
        return (self.total_feed_required_kg * self.avg_feed_price_per_kg) + (self.number_of_fingerlings_stocked * self.fingerling_price_each)
    
    @property
    def total_profit_per_cycle(self):
        return self.total_revenue_per_cycle - self.total_cost_per_cycle
    
    @property
    def roi(self):
        if self.total_cost_per_cycle == 0:
            return 0
        return (self.total_profit_per_cycle / self.total_cost_per_cycle) * 100
    
    @property
    def payback_period_years(self):
        if self.total_profit_per_cycle == 0:
            return 0
        return self.total_cost_per_cycle / self.total_profit_per_cycle
    
    @property
    def break_even_feed_conversion_ratio(self):
        if self.total_biomass_harvested_kg == 0:
            return 0
        return self.total_feed_required_kg / self.total_biomass_harvested_kg
    
    @property
    def break_even_productivity_kg_per_m2(self):
        if self.pond_size_m2 == 0:
            return 0
        return self.total_biomass_harvested_kg / self.pond_size_m2
    
    @property
    def break_even_feed_cost_per_kg(self):
        if self.total_feed_required_kg == 0:
            return 0
        return self.total_cost_per_cycle / self.total_feed_required_kg
    
    @property
    def break_even_cost_per_kg(self):
        if self.total_biomass_harvested_kg == 0:
            return 0
        return self.total_cost_per_cycle / self.total_biomass_harvested_kg
    
    @property
    def break_even_payback_period_years(self):
        if self.total_profit_per_cycle == 0:
            return 0
        return self.total_cost_per_cycle / self.total_profit_per_cycle
    
    @property   
    def break_even_roi(self):
        if self.total_cost_per_cycle == 0:
            return 0
        return (self.total_profit_per_cycle / self.total_cost_per_cycle) * 100
    
    @property
    def break_even_sale_price_per_kg(self):
        if self.total_biomass_harvested_kg == 0:
            return 0
        return self.total_cost_per_cycle / self.total_biomass_harvested_kg
    
    @property
    def break_even_fingerling_price_each(self):
        if self.number_of_fingerlings_stocked == 0:
            return 0
        return self.total_cost_per_cycle / self.number_of_fingerlings_stocked   
    
    @property
    def break_even_avg_feed_price_per_kg(self):
        if self.total_feed_required_kg == 0:
            return 0
        return self.total_cost_per_cycle / self.total_feed_required_kg
    
    @property
    def break_even_avg_feed_price_per_bag(self):
        if self.total_feed_required_kg == 0:
            return 0
        return self.total_cost_per_cycle / (self.total_feed_required_kg / self.avg_feed_bag_weight_kg)  
    
    @property
    def break_even_avg_feed_bag_weight_kg(self):
        if self.total_feed_required_kg == 0:
            return 0
        return self.total_feed_required_kg / (self.total_cost_per_cycle / self.avg_feed_price_per_bag)
    
    @property
    def break_even_stocking_density_per_m2(self):
        if self.pond_size_m2 == 0:
            return 0
        return self.number_of_fingerlings_stocked / self.pond_size_m2
    
    @property
    def break_even_pond_size_m2(self):
        if self.number_of_fingerlings_stocked == 0:
            return 0
        return self.number_of_fingerlings_stocked / self.stocking_density_per_m2
    
    @property
    def break_even_expected_survival_rate_percent(self):
        if self.expected_number_harvested == 0:
            return 0
        return (self.expected_number_harvested / self.number_of_fingerlings_stocked) * 100
    
    @property
    def break_even_target_harvest_size_g(self):
        if self.expected_number_harvested == 0:
            return 0
        return (self.total_biomass_harvested_kg * 1000) / self.expected_number_harvested
    
    @property
    def break_even_feed_conversion_ratio(self):
        if self.total_biomass_harvested_kg == 0:
            return 0
        return self.total_feed_required_kg / self.total_biomass_harvested_kg
    
    @property
    def break_even_sale_price_per_kg(self):
        if self.total_biomass_harvested_kg == 0:
            return 0
        return self.total_cost_per_cycle / self.total_biomass_harvested_kg
    
    @property
    def break_even_fingerling_price_each(self):
        if self.number_of_fingerlings_stocked == 0:
            return 0
        return self.total_cost_per_cycle / self.number_of_fingerlings_stocked
    
    @property
    def break_even_avg_feed_price_per_kg(self):
        if self.total_feed_required_kg == 0:
            return 0
        return self.total_cost_per_cycle / self.total_feed_required_kg


class SheepProductionAssumptions(models.Model):
    """
    Stores the detailed productivity and price assumptions for a Sheep enterprise plan.
    This model is based on an annual analysis of a breeding ewe flock.
    """
    plan = models.OneToOneField(EnterprisePlan, on_delete=models.CASCADE, related_name="sheep_assumptions")

    # Flock Structure
    number_of_ewes = models.PositiveIntegerField(default=100)
    replacement_rate_percent = models.DecimalField(max_digits=5, decimal_places=2, default=20.0)
    rams_required = models.PositiveIntegerField(default=3)

    # Production Parameters
    lambing_percent = models.DecimalField(max_digits=5, decimal_places=1, default=130.0)
    pre_weaning_lamb_mortality_percent = models.DecimalField(max_digits=5, decimal_places=2, default=10.0)
    avg_sale_weight_lambs_kg = models.DecimalField(max_digits=5, decimal_places=1, default=35.0)
    avg_sale_weight_cull_ewes_kg = models.DecimalField(max_digits=5, decimal_places=1, default=45.0)

    # Price Assumptions
    sale_price_lambs_per_kg = models.DecimalField(max_digits=10, decimal_places=2, default=400.00)
    sale_price_cull_ewes_per_kg = models.DecimalField(max_digits=10, decimal_places=2, default=300.00)
    sale_price_wool_per_kg = models.DecimalField(max_digits=10, decimal_places=2, default=100.00, null=True, blank=True)
    
    # Actuals
    actual_sale_price_lambs_per_kg = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_sale_price_cull_ewes_per_kg = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_sale_price_wool_per_kg = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_lambing_percent = models.DecimalField(max_digits=5, decimal_places=1, null=True, blank=True)
    actual_pre_weaning_lamb_mortality_percent = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    actual_avg_sale_weight_lambs_kg = models.DecimalField(max_digits=5, decimal_places=1, null=True, blank=True)
    actual_avg_sale_weight_cull_ewes_kg = models.DecimalField(max_digits=5, decimal_places=1, null=True, blank=True)
    
    
    # --- KEY CALCULATED PROPERTIES ---
    @property
    def lambs_born(self):
        return self.number_of_ewes * (self.lambing_percent / 100)

    @property
    def lambs_weaned(self):
        return self.lambs_born * (1 - (self.pre_weaning_lamb_mortality_percent / 100))
        
    @property
    def lambs_weaned_per_ewe(self):
        if self.number_of_ewes == 0:
            return 0
        return self.lambs_weaned / self.number_of_ewes * 100
        
    @property
    def ewe_lambs_retained(self):
        return self.number_of_ewes * (self.replacement_rate_percent / 100)

    @property
    def lambs_sold(self):
        sold = self.lambs_weaned - self.ewe_lambs_retained
        return max(sold, 0) # Cannot sell negative lambs
        
    @property
    def cull_ewes_sold(self):
        # Same as number of replacements kept
        return self.ewe_lambs_retained

    @property
    def total_kg_lamb_sold(self):
        return self.lambs_sold * self.avg_sale_weight_lambs_kg
    
    @property
    def total_revenue_from_lambs(self):
        return self.total_kg_lamb_sold * self.sale_price_lambs_per_kg
    
    @property
    def total_revenue_from_cull_ewes(self):
        return self.cull_ewes_sold * self.avg_sale_weight_cull_ewes_kg * self.sale_price_cull_ewes_per_kg
    
    @property
    def total_revenue(self):
        return self.total_revenue_from_lambs + self.total_revenue_from_cull_ewes
    
    @property
    def total_cost_per_year(self):
        # Placeholder, actual cost calculation needs to be implemented
        return 0
    
    @property
    def total_profit_per_year(self):
        return self.total_revenue - self.total_cost_per_year
    
    @property
    def roi(self):
        if self.total_cost_per_year == 0:
            return 0
        return (self.total_profit_per_year / self.total_cost_per_year) * 100
    
    @property
    def break_even_lambing_percent(self):
        if self.total_revenue == 0:
            return 0
        return (self.total_revenue / self.number_of_ewes) * 100
    
    @property
    def break_even_sale_price_lambs_per_kg(self):
        if self.total_kg_lamb_sold == 0:
            return 0
        return self.total_cost_per_year / self.total_kg_lamb_sold
    
    @property
    def break_even_sale_price_cull_ewes_per_kg(self):
        if self.cull_ewes_sold == 0:
            return 0
        return self.total_cost_per_year / (self.cull_ewes_sold * self.avg_sale_weight_cull_ewes_kg)
    
    @property
    def break_even_sale_price_wool_per_kg(self):
        if self.total_revenue_from_wool == 0:
            return 0
        return self.total_cost_per_year / self.total_revenue_from_wool
    
    @property
    def break_even_number_of_ewes(self):
        if self.break_even_sale_price_lambs_per_kg == 0:
            return 0
        return self.total_cost_per_year / (self.break_even_sale_price_lambs_per_kg * self.avg_sale_weight_lambs_kg)
    
    @property
    def break_even_avg_sale_weight_lambs_kg(self):
        if self.break_even_sale_price_lambs_per_kg == 0:
            return 0
        return self.total_cost_per_year / (self.break_even_sale_price_lambs_per_kg * self.lambs_sold)
    
    @property
    def break_even_avg_sale_weight_cull_ewes_kg(self):
        if self.break_even_sale_price_cull_ewes_per_kg == 0:
            return 0
        return self.total_cost_per_year / (self.break_even_sale_price_cull_ewes_per_kg * self.cull_ewes_sold)
    
    @property
    def break_even_pre_weaning_lamb_mortality_percent(self):
        if self.lambs_born == 0:
            return 0
        return (self.lambs_born - self.lambs_weaned) / self.lambs_born * 100
    
    @property
    def break_even_lambing_percent(self):
        if self.lambs_born == 0:
            return 0
        return (self.lambs_born / self.number_of_ewes) * 100
    
    @property
    def break_even_replacement_rate_percent(self):
        if self.ewe_lambs_retained == 0:
            return 0
        return (self.ewe_lambs_retained / self.number_of_ewes) * 100
    
    @property
    def break_even_sale_price_wool_per_kg(self):
        if self.total_revenue_from_wool == 0:
            return 0
        return self.total_cost_per_year / self.total_revenue_from_wool
    
    @property
    def break_even_sale_price_lambs_per_kg(self):
        if self.total_kg_lamb_sold == 0:
            return 0
        return self.total_cost_per_year / self.total_kg_lamb_sold
    
    @property
    def break_even_sale_price_cull_ewes_per_kg(self):
        if self.cull_ewes_sold == 0:
            return 0
        return self.total_cost_per_year / (self.cull_ewes_sold * self.avg_sale_weight_cull_ewes_kg)
    
    @property
    def break_even_number_of_ewes(self):
        if self.break_even_sale_price_lambs_per_kg == 0:
            return 0
        return self.total_cost_per_year / (self.break_even_sale_price_lambs_per_kg * self.avg_sale_weight_lambs_kg)
    
    @property
    def break_even_avg_sale_weight_lambs_kg(self):
        if self.break_even_sale_price_lambs_per_kg == 0:
            return 0
        return self.total_cost_per_year / (self.break_even_sale_price_lambs_per_kg * self.lambs_sold)
    
    @property
    def break_even_avg_sale_weight_cull_ewes_kg(self):
        if self.break_even_sale_price_cull_ewes_per_kg == 0:
            return 0
        return self.total_cost_per_year / (self.break_even_sale_price_cull_ewes_per_kg * self.cull_ewes_sold)
    
    @property
    def break_even_pre_weaning_lamb_mortality_percent(self):
        if self.lambs_born == 0:
            return 0
        return (self.lambs_born - self.lambs_weaned) / self.lambs_born * 100


class ForageProductionAssumptions(models.Model):
    """
    Stores the detailed productivity and price assumptions for a Forage enterprise plan.
    This model accounts for the multi-year lifespan of the crop.
    """
    plan = models.OneToOneField(EnterprisePlan, on_delete=models.CASCADE, related_name="forage_assumptions")

    # Crop & Field Parameters
    field_size_hectares = models.DecimalField(max_digits=10, decimal_places=2, default=1.0)
    productive_lifespan_years = models.PositiveIntegerField(default=4)

    # Annual Production Parameters
    cuts_per_year = models.PositiveIntegerField(default=6)
    yield_bales_per_cut = models.DecimalField(max_digits=10, decimal_places=2, default=35.0)

    # Price Assumptions
    sale_price_per_bale = models.DecimalField(max_digits=10, decimal_places=2, default=250.00)

    # Actuals
    actual_sale_price_per_bale = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_yield_bales_per_cut = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_cuts_per_year = models.PositiveIntegerField(null=True, blank=True)
    actual_productive_lifespan_years = models.PositiveIntegerField(null=True, blank=True)
    
    
    # --- KEY CALCULATED PROPERTIES ---
    @property
    def total_annual_yield_bales(self):
        return self.cuts_per_year * self.yield_bales_per_cut
    
    @property
    def total_revenue_per_year(self):
        return self.total_annual_yield_bales * self.sale_price_per_bale
    
    @property
    def total_revenue(self):
        return self.total_revenue_per_year * self.productive_lifespan_years
    
    @property
    def total_cost_per_year(self):
        # Placeholder, actual cost calculation needs to be implemented
        return 0
    
    @property
    def total_profit_per_year(self):
        return self.total_revenue_per_year - self.total_cost_per_year
    
    @property
    def roi(self):
        if self.total_cost_per_year == 0:
            return 0
        return (self.total_profit_per_year / self.total_cost_per_year) * 100
    
    @property
    def break_even_sale_price_per_bale(self):
        if self.total_annual_yield_bales == 0:
            return 0
        return self.total_cost_per_year / self.total_annual_yield_bales
    
    @property
    def break_even_yield_bales_per_cut(self):
        if self.break_even_sale_price_per_bale == 0:
            return 0
        return self.total_cost_per_year / (self.break_even_sale_price_per_bale * self.cuts_per_year)
    
    @property
    def break_even_cuts_per_year(self):
        if self.break_even_sale_price_per_bale == 0:
            return 0
        return self.total_cost_per_year / (self.break_even_sale_price_per_bale * self.yield_bales_per_cut)
    
    @property
    def break_even_productive_lifespan_years(self):
        if self.break_even_sale_price_per_bale == 0:
            return 0
        return self.total_cost_per_year / (self.break_even_sale_price_per_bale * self.total_annual_yield_bales)
    
    @property
    def break_even_field_size_hectares(self):
        if self.break_even_sale_price_per_bale == 0:
            return 0
        return self.total_cost_per_year / (self.break_even_sale_price_per_bale * self.total_annual_yield_bales * self.cuts_per_year)
    
    @property
    def break_even_yield_bales_per_cut(self):
        if self.break_even_sale_price_per_bale == 0:
            return 0
        return self.total_cost_per_year / (self.break_even_sale_price_per_bale * self.cuts_per_year)
    
    @property
    def break_even_cuts_per_year(self):
        if self.break_even_sale_price_per_bale == 0:
            return 0
        return self.total_cost_per_year / (self.break_even_sale_price_per_bale * self.yield_bales_per_cut)
    
    @property
    def break_even_productive_lifespan_years(self):
        if self.break_even_sale_price_per_bale == 0:
            return 0
        return self.total_cost_per_year / (self.break_even_sale_price_per_bale * self.total_annual_yield_bales)


class HorticultureCropAssumptions(models.Model):
    """
    Stores the detailed productivity and price assumptions for a Horticultural Crop plan.
    """
    plan = models.OneToOneField(EnterprisePlan, on_delete=models.CASCADE, related_name="horticulture_assumptions")

    # Crop & Field Parameters
    field_size_acres = models.DecimalField(max_digits=10, decimal_places=2, default=1.0)
    crop_cycle_days = models.PositiveIntegerField(default=120)
    planting_density_per_acre = models.PositiveIntegerField(default=10000)

    # Production & Price Parameters
    expected_yield_kg_per_acre = models.DecimalField(max_digits=10, decimal_places=2, default=25000.0)
    
    # Grading Percentages
    percent_grade_a = models.DecimalField(max_digits=5, decimal_places=2, default=60.0)
    percent_grade_b = models.DecimalField(max_digits=5, decimal_places=2, default=25.0)
    percent_grade_c = models.DecimalField(max_digits=5, decimal_places=2, default=15.0)

    # Sale Prices
    sale_price_grade_a_per_kg = models.DecimalField(max_digits=10, decimal_places=2, default=50.00)
    sale_price_grade_b_per_kg = models.DecimalField(max_digits=10, decimal_places=2, default=30.00)
    sale_price_grade_c_per_kg = models.DecimalField(max_digits=10, decimal_places=2, default=10.00)

    # Input Costs (can be expanded)
    seedling_price_each = models.DecimalField(max_digits=10, decimal_places=2, default=15.00)
    
    # Marketing
    market_commission_percent = models.DecimalField(max_digits=5, decimal_places=2, default=10.0)

    # Actuals
    actual_sale_price_grade_a_per_kg = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_sale_price_grade_b_per_kg = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_sale_price_grade_c_per_kg = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_percent_grade_a = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    actual_percent_grade_b = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    actual_percent_grade_c = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    actual_expected_yield_kg_per_acre = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_seedling_price_each = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_market_commission_percent = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    
    
    # --- KEY CALCULATED PROPERTIES ---
    @property
    def weighted_average_price_per_kg(self):
        price_a = (self.percent_grade_a / 100) * self.sale_price_grade_a_per_kg
        price_b = (self.percent_grade_b / 100) * self.sale_price_grade_b_per_kg
        price_c = (self.percent_grade_c / 100) * self.sale_price_grade_c_per_kg
        return price_a + price_b + price_c
    
    @property
    def total_revenue_per_acre(self):
        return self.expected_yield_kg_per_acre * self.weighted_average_price_per_kg
    
    @property
    def total_revenue(self):
        return self.total_revenue_per_acre * self.field_size_acres
    
    @property
    def total_cost_per_acre(self):
        # Placeholder, actual cost calculation needs to be implemented
        return 0
    
    @property
    def total_profit_per_acre(self):
        return self.total_revenue_per_acre - self.total_cost_per_acre
    
    @property
    def roi(self):
        if self.total_cost_per_acre == 0:
            return 0
        return (self.total_profit_per_acre / self.total_cost_per_acre) * 100
    
    @property
    def break_even_sale_price_grade_a_per_kg(self):
        if self.percent_grade_a == 0:
            return 0
        return self.total_cost_per_acre / (self.expected_yield_kg_per_acre * (self.percent_grade_a / 100))
    
    @property
    def break_even_sale_price_grade_b_per_kg(self):
        if self.percent_grade_b == 0:
            return 0
        return self.total_cost_per_acre / (self.expected_yield_kg_per_acre * (self.percent_grade_b / 100))
    
    @property
    def break_even_sale_price_grade_c_per_kg(self):
        if self.percent_grade_c == 0:
            return 0
        return self.total_cost_per_acre / (self.expected_yield_kg_per_acre * (self.percent_grade_c / 100))
    
    @property
    def break_even_expected_yield_kg_per_acre(self):
        if self.weighted_average_price_per_kg == 0:
            return 0
        return self.total_cost_per_acre / self.weighted_average_price_per_kg
    
    @property
    def break_even_percent_grade_a(self):
        if self.break_even_sale_price_grade_a_per_kg == 0:
            return 0
        return (self.expected_yield_kg_per_acre * (self.percent_grade_a / 100)) / self.break_even_sale_price_grade_a_per_kg 
    
    @property
    def break_even_percent_grade_b(self):
        if self.break_even_sale_price_grade_b_per_kg == 0:
            return 0
        return (self.expected_yield_kg_per_acre * (self.percent_grade_b / 100)) / self.break_even_sale_price_grade_b_per_kg
    
    @property
    def break_even_percent_grade_c(self):
        if self.break_even_sale_price_grade_c_per_kg == 0:
            return 0
        return (self.expected_yield_kg_per_acre * (self.percent_grade_c / 100)) / self.break_even_sale_price_grade_c_per_kg 
    
    @property
    def break_even_seedling_price_each(self):
        if self.planting_density_per_acre == 0:
            return 0
        return self.total_cost_per_acre / self.planting_density_per_acre
    
    @property
    def break_even_market_commission_percent(self):
        if self.total_revenue == 0:
            return 0
        return (self.total_cost_per_acre / self.total_revenue) * 100
    
    @property
    def break_even_field_size_acres(self):
        if self.total_revenue_per_acre == 0:
            return 0
        return self.total_cost_per_acre / self.total_revenue_per_acre   
    
    @property
    def break_even_planting_density_per_acre(self):
        if self.seedling_price_each == 0:
            return 0
        return self.total_cost_per_acre / self.seedling_price_each
    
    @property
    def break_even_crop_cycle_days(self):
        if self.total_revenue_per_acre == 0:
            return 0
        return (self.total_cost_per_acre / self.total_revenue_per_acre) * 365   


class FieldCropAssumptions(models.Model):
    """
    Stores the detailed productivity and price assumptions for a Field Crop enterprise plan.
    """
    plan = models.OneToOneField(EnterprisePlan, on_delete=models.CASCADE, related_name="field_crop_assumptions")

    # Crop & Field Parameters
    field_size_hectares = models.DecimalField(max_digits=10, decimal_places=2, default=1.0)
    
    # Production & Price Parameters
    expected_yield_kg_per_hectare = models.DecimalField(max_digits=10, decimal_places=2, default=2700.0)
    sale_price_per_kg = models.DecimalField(max_digits=10, decimal_places=2, default=27.78) # (2500 KSH / 90kg bag)
    
    
    # Actuals
    actual_sale_price_per_kg = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_expected_yield_kg_per_hectare = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_field_size_hectares = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_expected_yield_in_bags = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_sale_price_per_bag = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_roi = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_payback_period_years = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_sale_price_per_kg = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_expected_yield_kg_per_hectare = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_field_size_hectares = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_sale_price_per_bag = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_roi = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_payback_period_years = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    
    
    # You could add other assumptions like seed price, fertilizer price here
    # to make the model even more dynamic. For now, they will be manual PlanItems.

    # --- KEY CALCULATED PROPERTIES ---
    @property
    def sale_price_per_90kg_bag(self):
        return self.sale_price_per_kg * 90
        
    @property
    def expected_yield_in_bags(self):
        # Assuming a standard 90kg bag for maize, can be made variable
        if self.expected_yield_kg_per_hectare == 0:
            return 0
        return self.expected_yield_kg_per_hectare / 90
    
    @property
    def total_revenue_per_hectare(self):
        return self.expected_yield_in_bags * self.sale_price_per_90kg_bag
    
    @property
    def total_revenue(self):
        return self.total_revenue_per_hectare * self.field_size_hectares
    
    @property
    def total_cost_per_hectare(self):
        # Placeholder, actual cost calculation needs to be implemented
        return 0
    
    @property
    def total_profit_per_hectare(self):
        return self.total_revenue_per_hectare - self.total_cost_per_hectare
    
    @property
    def roi(self):
        if self.total_cost_per_hectare == 0:
            return 0
        return (self.total_profit_per_hectare / self.total_cost_per_hectare) * 100
    
    @property
    def break_even_sale_price_per_kg(self):
        if self.expected_yield_kg_per_hectare == 0:
            return 0
        return self.total_cost_per_hectare / self.expected_yield_kg_per_hectare
    
    @property
    def break_even_sale_price_per_bag(self):
        if self.expected_yield_in_bags == 0:
            return 0
        return self.total_cost_per_hectare / self.expected_yield_in_bags
    
    @property
    def break_even_expected_yield_kg_per_hectare(self):
        if self.sale_price_per_kg == 0:
            return 0
        return self.total_cost_per_hectare / self.sale_price_per_kg
    
    @property
    def break_even_expected_yield_in_bags(self):
        if self.sale_price_per_90kg_bag == 0:
            return 0
        return self.total_cost_per_hectare / self.sale_price_per_90kg_bag
    
    @property
    def break_even_field_size_hectares(self):
        if self.total_revenue_per_hectare == 0:
            return 0
        return self.total_cost_per_hectare / self.total_revenue_per_hectare
    
    @property
    def break_even_roi(self):
        if self.total_cost_per_hectare == 0:
            return 0
        return (self.total_profit_per_hectare / self.total_cost_per_hectare) * 100
    
    @property
    def break_even_payback_period_years(self):
        if self.total_profit_per_hectare == 0:
            return 0
        return self.total_cost_per_hectare / self.total_profit_per_hectare



class ConservancyAssumptions(models.Model):
    """
    Stores the high-level operational and financial assumptions for a Wildlife Conservancy plan.
    """
    plan = models.OneToOneField(EnterprisePlan, on_delete=models.CASCADE, related_name="conservancy_assumptions")

    # Conservancy Parameters
    total_land_area_acres = models.DecimalField(max_digits=10, decimal_places=2, default=10000.0)
    
    # Revenue Stream Assumptions
    # Tourism
    lodge_rooms = models.PositiveIntegerField(default=5)
    target_occupancy_percent = models.DecimalField(max_digits=5, decimal_places=2, default=60.0)
    price_per_night = models.DecimalField(max_digits=10, decimal_places=2, default=250.00) # Assuming USD, converted later
    
    # Hunting
    trophy_hunting_packages_sold = models.PositiveIntegerField(default=5)
    avg_revenue_per_package = models.DecimalField(max_digits=12, decimal_places=2, default=15000.00)

    # Live Sales
    live_game_offtake_au = models.PositiveIntegerField(default=50) # Animal Units
    avg_price_per_au = models.DecimalField(max_digits=10, decimal_places=2, default=400.00) # Assuming USD, converted later

    # Actuals
    actual_price_per_night = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_avg_revenue_per_package = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    actual_avg_price_per_au = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_roi = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_payback_period_years = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_price_per_night = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_avg_revenue_per_package = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    actual_break_even_avg_price_per_au = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_roi = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_payback_period_years = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_lodge_rooms = models.PositiveIntegerField(null=True, blank=True)
    actual_break_even_target_occupancy_percent = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    actual_break_even_trophy_hunting_packages_sold = models.PositiveIntegerField(null=True, blank=True)
    actual_break_even_live_game_offtake_au = models.PositiveIntegerField(null=True, blank=True)
    actual_break_even_total_land_area_acres = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    
    # --- KEY CALCULATED PROPERTIES ---
    @property
    def total_revenue_from_tourism(self):
        return self.lodge_rooms * 365 * (self.target_occupancy_percent / 100) * self.price_per_night
    
    @property
    def total_revenue_from_hunting(self):
        return self.trophy_hunting_packages_sold * self.avg_revenue_per_package
    
    @property
    def total_revenue_from_sales(self):
        return self.live_game_offtake_au * self.avg_price_per_au
    
    @property
    def total_revenue(self):
        return self.total_revenue_from_tourism + self.total_revenue_from_hunting + self.total_revenue_from_sales
    
    @property
    def total_cost_per_acre(self):
        # Placeholder, actual cost calculation needs to be implemented
        return 0
    
    @property
    def total_profit_per_acre(self):
        return self.total_revenue_per_acre - self.total_cost_per_acre
    
    @property
    def roi(self):
        if self.total_cost_per_acre == 0:
            return 0
        return (self.total_profit_per_acre / self.total_cost_per_acre) * 100
    
    @property
    def break_even_price_per_night(self):
        if self.lodge_rooms == 0:
            return 0
        return self.total_cost_per_acre / (self.lodge_rooms * 365 * (self.target_occupancy_percent / 100))
    
    

class PlantationCropAssumptions(models.Model):
    """
    Stores the high-level financial and biological assumptions for a Plantation Crop plan.
    """
    plan = models.OneToOneField(EnterprisePlan, on_delete=models.CASCADE, related_name="plantation_assumptions")

    # Plantation Parameters
    total_area_hectares = models.DecimalField(max_digits=10, decimal_places=2, default=50.0)
    gestation_period_years = models.PositiveIntegerField(default=7)
    
    # Production & Price Parameters
    expected_mai_m3_ha_yr = models.DecimalField(
        max_digits=5, decimal_places=2, default=25.0, 
        verbose_name="Expected Mean Annual Increment (m³/Ha/Year)"
    )
    stumpage_price_m3_y7 = models.DecimalField(
        max_digits=10, decimal_places=2, default=4000.00,
        verbose_name="Stumpage Price per m³ at Harvest (Year 7)"
    )
    
    # Actuals
    actual_expected_mai_m3_ha_yr = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    actual_stumpage_price_m3_y7 = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_roi = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_payback_period_years = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_expected_mai_m3_ha_yr = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    actual_break_even_stumpage_price_m3_y7 = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_roi = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_payback_period_years = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_total_area_hectares = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_gestation_period_years = models.PositiveIntegerField(null=True, blank=True)
    
    
    # --- KEY CALCULATED PROPERTIES ---
    @property
    def expected_yield_at_harvest_m3(self):
        return self.total_area_hectares * self.expected_mai_m3_ha_yr * self.gestation_period_years
    
    @property
    def total_revenue(self):
        return self.expected_yield_at_harvest_m3 * self.stumpage_price_m3_y7
    
    @property
    def total_cost(self):
        # Placeholder, actual cost calculation needs to be implemented
        return 0
    
    @property
    def total_profit(self):
        return self.total_revenue - self.total_cost
    
    @property
    def roi(self):
        if self.total_cost == 0:
            return 0
        return (self.total_profit / self.total_cost) * 100
    
    @property
    def break_even_stumpage_price_m3_y7(self):
        if self.expected_yield_at_harvest_m3 == 0:
            return 0
        return self.total_cost / self.expected_yield_at_harvest_m3
    
    @property
    def break_even_expected_mai_m3_ha_yr(self):
        if self.stumpage_price_m3_y7 == 0:
            return 0
        return self.total_cost / (self.total_area_hectares * self.gestation_period_years)
    
    @property
    def break_even_total_area_hectares(self):
        if self.expected_mai_m3_ha_yr == 0:
            return 0
        return self.total_cost / (self.expected_mai_m3_ha_yr * self.gestation_period_years)
    
    @property
    def break_even_gestation_period_years(self):
        if self.expected_mai_m3_ha_yr == 0:
            return 0
        return self.total_cost / (self.total_area_hectares * self.expected_mai_m3_ha_yr)
    


class AgrotourismAssumptions(models.Model):
    """
    Stores the high-level operational and financial assumptions for an Agrotourism enterprise plan.
    """
    plan = models.OneToOneField(EnterprisePlan, on_delete=models.CASCADE, related_name="agrotourism_assumptions")

    # Tourism Parameters
    lodging_capacity_rooms = models.PositiveIntegerField(default=5)
    target_annual_occupancy_percent = models.DecimalField(max_digits=5, decimal_places=2, default=45.0)
    avg_revenue_per_guest_night = models.DecimalField(max_digits=12, decimal_places=2, default=6000.00)

    # Experience Parameters
    avg_tours_per_year = models.PositiveIntegerField(default=500) # Assuming 1000 people / 2 per group
    price_per_tour = models.DecimalField(max_digits=10, decimal_places=2, default=500.00)

    # Farm Sales Parameters
    annual_value_added_sales = models.DecimalField(max_digits=12, decimal_places=2, default=1500000.00)
    
    # Cost Assumptions
    cost_of_food_per_guest_day = models.DecimalField(max_digits=10, decimal_places=2, default=1200.00)

    # Actuals
    actual_avg_revenue_per_guest_night = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    actual_price_per_tour = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_annual_value_added_sales = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    actual_cost_of_food_per_guest_day = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_roi = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_payback_period_years = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_avg_revenue_per_guest_night = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    actual_break_even_price_per_tour = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_annual_value_added_sales = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    actual_break_even_cost_of_food_per_guest_day = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_roi = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_payback_period_years = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_lodging_capacity_rooms = models.PositiveIntegerField(null=True, blank=True)
    actual_break_even_target_annual_occupancy_percent = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    actual_break_even_avg_tours_per_year = models.PositiveIntegerField(null=True, blank=True)
    actual_break_even_total_land_area_acres = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_total_revenue = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    actual_break_even_total_cost = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    actual_break_even_total_profit = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    

    # --- KEY CALCULATED PROPERTIES ---
    @property
    def total_guest_nights_per_year(self):
        return self.lodging_capacity_rooms * 365 * (self.target_annual_occupancy_percent / 100)
    
    @property
    def total_revenue_from_tourism(self):
        return self.total_guest_nights_per_year * self.avg_revenue_per_guest_night
    
    @property
    def total_revenue_from_tours(self):
        return self.avg_tours_per_year * self.price_per_tour
    
    @property
    def total_revenue_from_sales(self):
        return self.annual_value_added_sales
    
    @property
    def total_revenue(self):
        return self.total_revenue_from_tourism + self.total_revenue_from_tours + self.total_revenue_from_sales
    
    @property
    def total_cost_per_year(self):
        # Placeholder, actual cost calculation needs to be implemented
        return 0
    
    @property
    def total_profit_per_year(self):
        return self.total_revenue - self.total_cost_per_year
    
    @property
    def roi(self):
        if self.total_cost_per_year == 0:
            return 0
        return (self.total_profit_per_year / self.total_cost_per_year) * 100
    
    @property
    def payback_period_years(self):
        if self.total_profit_per_year == 0:
            return 0
        return self.total_cost_per_year / self.total_profit_per_year
    
    @property
    def break_even_avg_revenue_per_guest_night(self):
        if self.total_guest_nights_per_year == 0:
            return 0
        return self.total_cost_per_year / self.total_guest_nights_per_year
    
    @property
    def break_even_price_per_tour(self):
        if self.avg_tours_per_year == 0:
            return 0
        return self.total_cost_per_year / self.avg_tours_per_year
    
    @property
    def break_even_annual_value_added_sales(self):
        if self.annual_value_added_sales == 0:
            return 0
        return self.total_cost_per_year / self.annual_value_added_sales
    
    @property
    def break_even_cost_of_food_per_guest_day(self):
        if self.total_guest_nights_per_year == 0:
            return 0
        return self.total_cost_per_year / (self.total_guest_nights_per_year / 365)
    


class IrrigationAssumptions(models.Model):
    """
    Stores the high-level financial and agronomic assumptions for an Irrigation Investment plan.
    """
    plan = models.OneToOneField(EnterprisePlan, on_delete=models.CASCADE, related_name="irrigation_assumptions")

    # Project Parameters
    area_to_irrigate_acres = models.DecimalField(max_digits=10, decimal_places=2, default=5.0)
    system_lifespan_years = models.PositiveIntegerField(default=7)

    # Agronomic Assumptions (Comparing 'Before' and 'After')
    # Rain-Fed Scenario
    rainfed_crop_name = models.CharField(max_length=100, default="Maize")
    rainfed_yield_units_per_acre = models.DecimalField(max_digits=10, decimal_places=2, default=15.0)
    rainfed_yield_unit_name = models.CharField(max_length=50, default="90kg bag")
    rainfed_price_per_unit = models.DecimalField(max_digits=10, decimal_places=2, default=2500.00)

    # Irrigated Scenario
    irrigated_crop_name = models.CharField(max_length=100, default="Tomatoes")
    irrigated_yield_units_per_acre = models.DecimalField(max_digits=10, decimal_places=2, default=220.0)
    irrigated_yield_unit_name = models.CharField(max_length=50, default="50kg crate")
    irrigated_price_per_unit = models.DecimalField(max_digits=10, decimal_places=2, default=2000.00)

    # Financial Assumptions
    discount_rate_percent = models.DecimalField(max_digits=5, decimal_places=2, default=10.0)

    # Actuals
    actual_rainfed_yield_units_per_acre = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_irrigated_yield_units_per_acre = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_roi = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_payback_period_years = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_rainfed_yield_units_per_acre = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_irrigated_yield_units_per_acre = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_roi = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_payback_period_years = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_break_even_area_to_irrigate_acres = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    
    # --- KEY CALCULATED PROPERTIES ---
    @property
    def total_rainfed_revenue(self):
        return self.area_to_irrigate_acres * self.rainfed_yield_units_per_acre * self.rainfed_price_per_unit

    @property
    def total_irrigated_revenue(self):
        return self.area_to_irrigate_acres * self.irrigated_yield_units_per_acre * self.irrigated_price_per_unit
    
    @property
    def total_cost(self):
        # Placeholder, actual cost calculation needs to be implemented
        return 0
    
    @property
    def total_profit(self):
        return self.total_irrigated_revenue - self.total_rainfed_revenue - self.total_cost
    
    @property
    def roi(self):
        if self.total_cost == 0:
            return 0
        return (self.total_profit / self.total_cost) * 100
    
    @property
    def payback_period_years(self):
        if self.total_profit == 0:
            return 0
        return self.total_cost / self.total_profit
    
    @property
    def break_even_rainfed_yield_units_per_acre(self):
        if self.area_to_irrigate_acres == 0:
            return 0
        return (self.total_cost / self.area_to_irrigate_acres) / self.rainfed_price_per_unit
    
    @property
    def break_even_irrigated_yield_units_per_acre(self):
        if self.area_to_irrigate_acres == 0:
            return 0
        return (self.total_cost / self.area_to_irrigate_acres) / self.irrigated_price_per_unit
    
    @property
    def break_even_area_to_irrigate_acres(self):
        if self.rainfed_yield_units_per_acre == 0:
            return 0
        return (self.total_cost / self.rainfed_price_per_unit) / self.rainfed_yield_units_per_acre
    
    @property
    def break_even_roi(self):
        if self.total_cost == 0:
            return 0
        return (self.total_profit / self.total_cost) * 100
    
    @property
    def break_even_payback_period_years(self):
        if self.total_profit == 0:
            return 0
        return self.total_cost / self.total_profit
    


class PartialBudget(models.Model):
    """
    The main container for a single Partial Budget analysis.
    This is a separate tool from the full EnterprisePlan.
    """
    name = models.CharField(max_length=255, help_text="e.g., Invest in new irrigation vs. continue with old system")
    
    # Links
    farmer = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name="partial_budgets", limit_choices_to={'role': 'farmer'})
    expert = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name="managed_partial_budgets", limit_choices_to={'role': 'expert'})
    tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE, related_name="partial_budgets")
    
    # Scenario descriptions
    current_situation_desc = models.CharField(max_length=255, blank=True)
    proposed_change_desc = models.CharField(max_length=255, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name



class PartialBudget(models.Model):
    """
    The main container for a spreadsheet-style Partial Budget analysis.
    """
    name = models.CharField(max_length=255, help_text="e.g., Feed lambs to heavier weights analysis")
    farmer = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name="partial_budgets")
    expert = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name="managed_partial_budgets")
    tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE, related_name="partial_budgets")
    
    # This section holds the "Brain" data from the top of the spreadsheet
    proposed_change_desc = models.TextField(blank=True)
    notes = models.TextField(blank=True, help_text="General notes or conclusions for the report")

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

class PartialBudgetItem(models.Model):
    """
    A single line item within one of the four quadrants.
    Matches the spreadsheet columns: Number x Amount x Price = Total
    """
    EFFECT_TYPE_CHOICES = (
        ('additional_income', 'Additional Income'),
        ('reduced_cost', 'Reduced Cost'),
        ('reduced_income', 'Reduced Income'),
        ('additional_cost', 'Additional Cost'),
    )

    budget = models.ForeignKey(PartialBudget, on_delete=models.CASCADE, related_name="items")
    effect_type = models.CharField(max_length=20, choices=EFFECT_TYPE_CHOICES)
    
    description = models.CharField(max_length=255)
    # The three core input fields for the calculation
    number = models.DecimalField(max_digits=12, decimal_places=2, default=1.0)
    amount = models.DecimalField(max_digits=12, decimal_places=2, default=0.00, help_text="e.g., Weight, Quantity per unit")
    price = models.DecimalField(max_digits=12, decimal_places=2, default=0.00)

    @property
    def total(self):
        # Replicates the spreadsheet calculation.
        # If amount or price is zero, the total will correctly be zero.
        return self.number * self.amount * self.price

    def __str__(self):
        return f"{self.get_effect_type_display()}: {self.description}"

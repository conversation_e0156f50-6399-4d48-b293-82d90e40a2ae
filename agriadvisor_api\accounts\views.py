# accounts/views.py

from django.contrib.auth.tokens import default_token_generator
from django.core.mail import send_mail
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.utils.encoding import force_bytes, force_str
from django.conf import settings
from rest_framework import generics, status, viewsets
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework_simplejwt.views import TokenObtainPairView
from .models import CustomUser
from .serializers import (
    UserCreateSerializer, CustomUserSerializer, ExpertReadSerializer,
    ExpertWriteSerializer
)
from core.views import BaseTenantViewSet
from core.permissions import Is<PERSON>armerUser, IsExpertUser

class RegisterUserView(generics.CreateAPIView):
    serializer_class = UserCreateSerializer
    permission_classes = [AllowAny]

class MyTokenObtainPairSerializer(TokenObtainPairSerializer):
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)
        token['username'] = user.username
        token['role'] = user.role
        token['tenant_id'] = user.tenant.id if user.tenant else None
        token['has_completed_onboarding'] = user.has_completed_onboarding
        token['first_name'] = user.first_name
        return token

class MyTokenObtainPairView(TokenObtainPairView):
    serializer_class = MyTokenObtainPairSerializer

class MeView(generics.RetrieveUpdateAPIView):
    permission_classes = [IsAuthenticated]
    def get_serializer_class(self):
        if self.request.user.role == 'expert':
            return ExpertReadSerializer
        return CustomUserSerializer
    def get_object(self):
        return self.request.user

class ChangePasswordView(generics.UpdateAPIView):
    permission_classes = [IsAuthenticated]
    def update(self, request, *args, **kwargs):
        user = request.user
        old_password = request.data.get("old_password")
        new_password = request.data.get("new_password")
        if not user.check_password(old_password):
            return Response({"old_password": ["Wrong password."]}, status=status.HTTP_400_BAD_REQUEST)
        user.set_password(new_password)
        user.save()
        return Response({"detail": "Password updated successfully"}, status=status.HTTP_200_OK)

class ForgotPasswordView(generics.GenericAPIView):
    permission_classes = [AllowAny]
    def post(self, request, *args, **kwargs):
        email = request.data.get('email')
        try:
            user = CustomUser.objects.get(email=email)
        except CustomUser.DoesNotExist:
            return Response({"detail": "If an account with this email exists, a password reset link has been sent."}, status=status.HTTP_200_OK)
        token = default_token_generator.make_token(user)
        uid = urlsafe_base64_encode(force_bytes(user.pk))
        reset_link = f"http://localhost:5173/reset-password?token={token}&uid={uid}"
        send_mail("Password Reset for AgriAdvisor", f"Click to reset: {reset_link}", settings.DEFAULT_FROM_EMAIL, [user.email])
        return Response({"detail": "If an account with this email exists, a password reset link has been sent."}, status=status.HTTP_200_OK)

class ResetPasswordView(generics.GenericAPIView):
    permission_classes = [AllowAny]
    def post(self, request, *args, **kwargs):
        token = request.data.get('token')
        uidb64 = request.data.get('uid')
        password = request.data.get('password')
        try:
            uid = force_str(urlsafe_base64_decode(uidb64))
            user = CustomUser.objects.get(pk=uid)
        except (TypeError, ValueError, OverflowError, CustomUser.DoesNotExist):
            user = None
        if user is not None and default_token_generator.check_token(user, token):
            user.set_password(password)
            user.save()
            return Response({"detail": "Password has been reset successfully."}, status=status.HTTP_200_OK)
        else:
            return Response({"error": "The reset link is invalid or has expired."}, status=status.HTTP_400_BAD_REQUEST)

class ExpertViewSet(BaseTenantViewSet):
    queryset = CustomUser.objects.filter(role='expert').prefetch_related('expert_profile', 'received_reviews')
    def get_serializer_class(self):
        if self.action in ['create', 'update', 'partial_update']:
            return ExpertWriteSerializer
        return ExpertReadSerializer

class FarmerViewSet(BaseTenantViewSet, viewsets.ReadOnlyModelViewSet):
    serializer_class = CustomUserSerializer
    queryset = CustomUser.objects.filter(role='farmer')

class FarmerExpertViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = ExpertReadSerializer
    permission_classes = [IsAuthenticated]  # Allow all authenticated users for testing
    def get_queryset(self):
        user = self.request.user
        if not user.tenant: return CustomUser.objects.none()
        return CustomUser.objects.filter(tenant=user.tenant, role='expert')



class ExpertFarmerListViewSet(viewsets.ReadOnlyModelViewSet):
    """
    An API endpoint for an EXPERT to get a list of farmers in their tenant.
    This is used for assigning budgets, etc.
    """
    serializer_class = CustomUserSerializer # We just need basic user info
    permission_classes = [IsAuthenticated, IsExpertUser] # Locked down to Experts

    def get_queryset(self):
        """
        Returns a list of all users with the 'farmer' role who belong
        to the currently authenticated expert's tenant.
        """
        user = self.request.user
        if not user.tenant:
            return CustomUser.objects.none()
        return CustomUser.objects.filter(tenant=user.tenant, role='farmer')
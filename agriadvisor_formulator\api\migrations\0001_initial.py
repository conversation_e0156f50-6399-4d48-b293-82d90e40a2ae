# Generated by Django 5.2.5 on 2025-08-29 18:31

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='GlobalIngredient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dry_matter_as_fed', models.DecimalField(decimal_places=2, help_text='Dry Matter (%) as fed', max_digits=5, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('crude_protein', models.DecimalField(blank=True, decimal_places=3, help_text='<PERSON><PERSON><PERSON> (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('crude_fiber', models.DecimalField(blank=True, decimal_places=3, help_text='<PERSON><PERSON><PERSON> (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('ndf', models.DecimalField(blank=True, decimal_places=3, help_text='Neutral Detergent Fiber (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('adf', models.DecimalField(blank=True, decimal_places=3, help_text='Acid Detergent Fiber (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('lignin', models.DecimalField(blank=True, decimal_places=3, help_text='Lignin (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('ether_extract', models.DecimalField(blank=True, decimal_places=3, help_text='Ether Extract / Crude Fat (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('ash', models.DecimalField(blank=True, decimal_places=3, help_text='Ash (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('insoluble_ash', models.DecimalField(blank=True, decimal_places=3, help_text='Acid Insoluble Ash (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('starch_pol', models.DecimalField(blank=True, decimal_places=3, help_text='Starch (Polarimetry) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('starch_enz', models.DecimalField(blank=True, decimal_places=3, help_text='Starch (Enzymatic) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('total_sugars', models.DecimalField(blank=True, decimal_places=3, help_text='Total Sugars (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('gross_energy', models.DecimalField(blank=True, decimal_places=3, help_text='Gross Energy (MJ/kg DM)', max_digits=8, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('lysine', models.DecimalField(blank=True, decimal_places=3, help_text='Lysine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('methionine', models.DecimalField(blank=True, decimal_places=3, help_text='Methionine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('cysteine', models.DecimalField(blank=True, decimal_places=3, help_text='Cysteine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('threonine', models.DecimalField(blank=True, decimal_places=3, help_text='Threonine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('tryptophan', models.DecimalField(blank=True, decimal_places=3, help_text='Tryptophan (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('arginine', models.DecimalField(blank=True, decimal_places=3, help_text='Arginine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('histidine', models.DecimalField(blank=True, decimal_places=3, help_text='Histidine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('isoleucine', models.DecimalField(blank=True, decimal_places=3, help_text='Isoleucine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('leucine', models.DecimalField(blank=True, decimal_places=3, help_text='Leucine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('valine', models.DecimalField(blank=True, decimal_places=3, help_text='Valine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('oleic_acid', models.DecimalField(blank=True, decimal_places=3, help_text='Oleic Acid (C18:1) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('linoleic_acid', models.DecimalField(blank=True, decimal_places=3, help_text='Linoleic Acid (C18:2) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('linolenic_acid', models.DecimalField(blank=True, decimal_places=3, help_text='Linolenic Acid (C18:3) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('palmitic_acid', models.DecimalField(blank=True, decimal_places=3, help_text='Palmitic Acid (C16:0) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('stearic_acid', models.DecimalField(blank=True, decimal_places=3, help_text='Stearic Acid (C18:0) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('ca', models.DecimalField(blank=True, decimal_places=3, help_text='Calcium (Ca) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('p', models.DecimalField(blank=True, decimal_places=3, help_text='Phosphorus (P) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('k', models.DecimalField(blank=True, decimal_places=3, help_text='Potassium (K) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('na', models.DecimalField(blank=True, decimal_places=3, help_text='Sodium (Na) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('cl', models.DecimalField(blank=True, decimal_places=3, help_text='Chlorine (Cl) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('mg', models.DecimalField(blank=True, decimal_places=3, help_text='Magnesium (Mg) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('s', models.DecimalField(blank=True, decimal_places=3, help_text='Sulphur (S) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('mn', models.DecimalField(blank=True, decimal_places=2, help_text='Manganese (Mn) (mg/kg DM)', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('zn', models.DecimalField(blank=True, decimal_places=2, help_text='Zinc (Zn) (mg/kg DM)', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('cu', models.DecimalField(blank=True, decimal_places=2, help_text='Copper (Cu) (mg/kg DM)', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('fe', models.DecimalField(blank=True, decimal_places=2, help_text='Iron (Fe) (mg/kg DM)', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('se', models.DecimalField(blank=True, decimal_places=2, help_text='Selenium (Se) (mg/kg DM)', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('name', models.CharField(max_length=255, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='NutrientRequirement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, unique=True)),
                ('min_crude_protein', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('max_crude_protein', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='FarmerIngredient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dry_matter_as_fed', models.DecimalField(decimal_places=2, help_text='Dry Matter (%) as fed', max_digits=5, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('crude_protein', models.DecimalField(blank=True, decimal_places=3, help_text='Crude Protein (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('crude_fiber', models.DecimalField(blank=True, decimal_places=3, help_text='Crude Fiber (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('ndf', models.DecimalField(blank=True, decimal_places=3, help_text='Neutral Detergent Fiber (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('adf', models.DecimalField(blank=True, decimal_places=3, help_text='Acid Detergent Fiber (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('lignin', models.DecimalField(blank=True, decimal_places=3, help_text='Lignin (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('ether_extract', models.DecimalField(blank=True, decimal_places=3, help_text='Ether Extract / Crude Fat (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('ash', models.DecimalField(blank=True, decimal_places=3, help_text='Ash (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('insoluble_ash', models.DecimalField(blank=True, decimal_places=3, help_text='Acid Insoluble Ash (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('starch_pol', models.DecimalField(blank=True, decimal_places=3, help_text='Starch (Polarimetry) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('starch_enz', models.DecimalField(blank=True, decimal_places=3, help_text='Starch (Enzymatic) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('total_sugars', models.DecimalField(blank=True, decimal_places=3, help_text='Total Sugars (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('gross_energy', models.DecimalField(blank=True, decimal_places=3, help_text='Gross Energy (MJ/kg DM)', max_digits=8, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('lysine', models.DecimalField(blank=True, decimal_places=3, help_text='Lysine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('methionine', models.DecimalField(blank=True, decimal_places=3, help_text='Methionine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('cysteine', models.DecimalField(blank=True, decimal_places=3, help_text='Cysteine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('threonine', models.DecimalField(blank=True, decimal_places=3, help_text='Threonine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('tryptophan', models.DecimalField(blank=True, decimal_places=3, help_text='Tryptophan (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('arginine', models.DecimalField(blank=True, decimal_places=3, help_text='Arginine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('histidine', models.DecimalField(blank=True, decimal_places=3, help_text='Histidine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('isoleucine', models.DecimalField(blank=True, decimal_places=3, help_text='Isoleucine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('leucine', models.DecimalField(blank=True, decimal_places=3, help_text='Leucine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('valine', models.DecimalField(blank=True, decimal_places=3, help_text='Valine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('oleic_acid', models.DecimalField(blank=True, decimal_places=3, help_text='Oleic Acid (C18:1) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('linoleic_acid', models.DecimalField(blank=True, decimal_places=3, help_text='Linoleic Acid (C18:2) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('linolenic_acid', models.DecimalField(blank=True, decimal_places=3, help_text='Linolenic Acid (C18:3) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('palmitic_acid', models.DecimalField(blank=True, decimal_places=3, help_text='Palmitic Acid (C16:0) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('stearic_acid', models.DecimalField(blank=True, decimal_places=3, help_text='Stearic Acid (C18:0) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('ca', models.DecimalField(blank=True, decimal_places=3, help_text='Calcium (Ca) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('p', models.DecimalField(blank=True, decimal_places=3, help_text='Phosphorus (P) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('k', models.DecimalField(blank=True, decimal_places=3, help_text='Potassium (K) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('na', models.DecimalField(blank=True, decimal_places=3, help_text='Sodium (Na) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('cl', models.DecimalField(blank=True, decimal_places=3, help_text='Chlorine (Cl) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('mg', models.DecimalField(blank=True, decimal_places=3, help_text='Magnesium (Mg) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('s', models.DecimalField(blank=True, decimal_places=3, help_text='Sulphur (S) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('mn', models.DecimalField(blank=True, decimal_places=2, help_text='Manganese (Mn) (mg/kg DM)', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('zn', models.DecimalField(blank=True, decimal_places=2, help_text='Zinc (Zn) (mg/kg DM)', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('cu', models.DecimalField(blank=True, decimal_places=2, help_text='Copper (Cu) (mg/kg DM)', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('fe', models.DecimalField(blank=True, decimal_places=2, help_text='Iron (Fe) (mg/kg DM)', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('se', models.DecimalField(blank=True, decimal_places=2, help_text='Selenium (Se) (mg/kg DM)', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('farmer_id', models.PositiveIntegerField(db_index=True)),
                ('tenant_id', models.PositiveIntegerField(db_index=True)),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
            ],
            options={
                'ordering': ['name'],
                'unique_together': {('farmer_id', 'name')},
            },
        ),
        migrations.CreateModel(
            name='Formulation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('farmer_id', models.PositiveIntegerField()),
                ('expert_id', models.PositiveIntegerField()),
                ('tenant_id', models.PositiveIntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('based_on_requirement', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='api.nutrientrequirement')),
            ],
        ),
        migrations.CreateModel(
            name='FormulationIngredient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dry_matter_as_fed', models.DecimalField(decimal_places=2, help_text='Dry Matter (%) as fed', max_digits=5, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('crude_protein', models.DecimalField(blank=True, decimal_places=3, help_text='Crude Protein (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('crude_fiber', models.DecimalField(blank=True, decimal_places=3, help_text='Crude Fiber (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('ndf', models.DecimalField(blank=True, decimal_places=3, help_text='Neutral Detergent Fiber (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('adf', models.DecimalField(blank=True, decimal_places=3, help_text='Acid Detergent Fiber (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('lignin', models.DecimalField(blank=True, decimal_places=3, help_text='Lignin (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('ether_extract', models.DecimalField(blank=True, decimal_places=3, help_text='Ether Extract / Crude Fat (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('ash', models.DecimalField(blank=True, decimal_places=3, help_text='Ash (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('insoluble_ash', models.DecimalField(blank=True, decimal_places=3, help_text='Acid Insoluble Ash (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('starch_pol', models.DecimalField(blank=True, decimal_places=3, help_text='Starch (Polarimetry) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('starch_enz', models.DecimalField(blank=True, decimal_places=3, help_text='Starch (Enzymatic) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('total_sugars', models.DecimalField(blank=True, decimal_places=3, help_text='Total Sugars (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('gross_energy', models.DecimalField(blank=True, decimal_places=3, help_text='Gross Energy (MJ/kg DM)', max_digits=8, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('lysine', models.DecimalField(blank=True, decimal_places=3, help_text='Lysine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('methionine', models.DecimalField(blank=True, decimal_places=3, help_text='Methionine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('cysteine', models.DecimalField(blank=True, decimal_places=3, help_text='Cysteine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('threonine', models.DecimalField(blank=True, decimal_places=3, help_text='Threonine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('tryptophan', models.DecimalField(blank=True, decimal_places=3, help_text='Tryptophan (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('arginine', models.DecimalField(blank=True, decimal_places=3, help_text='Arginine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('histidine', models.DecimalField(blank=True, decimal_places=3, help_text='Histidine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('isoleucine', models.DecimalField(blank=True, decimal_places=3, help_text='Isoleucine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('leucine', models.DecimalField(blank=True, decimal_places=3, help_text='Leucine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('valine', models.DecimalField(blank=True, decimal_places=3, help_text='Valine (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('oleic_acid', models.DecimalField(blank=True, decimal_places=3, help_text='Oleic Acid (C18:1) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('linoleic_acid', models.DecimalField(blank=True, decimal_places=3, help_text='Linoleic Acid (C18:2) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('linolenic_acid', models.DecimalField(blank=True, decimal_places=3, help_text='Linolenic Acid (C18:3) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('palmitic_acid', models.DecimalField(blank=True, decimal_places=3, help_text='Palmitic Acid (C16:0) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('stearic_acid', models.DecimalField(blank=True, decimal_places=3, help_text='Stearic Acid (C18:0) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('ca', models.DecimalField(blank=True, decimal_places=3, help_text='Calcium (Ca) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('p', models.DecimalField(blank=True, decimal_places=3, help_text='Phosphorus (P) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('k', models.DecimalField(blank=True, decimal_places=3, help_text='Potassium (K) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('na', models.DecimalField(blank=True, decimal_places=3, help_text='Sodium (Na) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('cl', models.DecimalField(blank=True, decimal_places=3, help_text='Chlorine (Cl) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('mg', models.DecimalField(blank=True, decimal_places=3, help_text='Magnesium (Mg) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('s', models.DecimalField(blank=True, decimal_places=3, help_text='Sulphur (S) (% DM)', max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('mn', models.DecimalField(blank=True, decimal_places=2, help_text='Manganese (Mn) (mg/kg DM)', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('zn', models.DecimalField(blank=True, decimal_places=2, help_text='Zinc (Zn) (mg/kg DM)', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('cu', models.DecimalField(blank=True, decimal_places=2, help_text='Copper (Cu) (mg/kg DM)', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('fe', models.DecimalField(blank=True, decimal_places=2, help_text='Iron (Fe) (mg/kg DM)', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('se', models.DecimalField(blank=True, decimal_places=2, help_text='Selenium (Se) (mg/kg DM)', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('ingredient_name', models.CharField(max_length=255)),
                ('ingredient_source_id', models.PositiveIntegerField()),
                ('ingredient_source_type', models.CharField(help_text="e.g., 'Global' or 'Farmer'", max_length=50)),
                ('amount_kg', models.DecimalField(decimal_places=3, max_digits=10)),
                ('formulation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ingredients', to='api.formulation')),
            ],
            options={
                'ordering': ['-amount_kg'],
                'unique_together': {('formulation', 'ingredient_name')},
            },
        ),
    ]

// src/pages/CategoryManagementPage.jsx
import React, { useState, useEffect } from "react";
import {
  Container,
  Table,
  <PERSON><PERSON>,
  Spinner,
  Alert,
  Modal,
  Form,
  Card,
  Row,
  Col,
  Badge,
  InputGroup,
  Pagination,
} from "react-bootstrap";
import { getAdminCategories, createCategory, updateCategory, deleteCategory } from "../services/knowledgeBaseService";

const CategoryManagementPage = () => {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [sortConfig, setSortConfig] = useState({ key: "name", direction: "asc" });
  const [retryCount, setRetryCount] = useState(0);

  // Modal state
  const [showModal, setShowModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    color_code: "#6c757d",
    icon: "folder",
    order: 0,
  });
  const [formErrors, setFormErrors] = useState({});
  const [submitting, setSubmitting] = useState(false);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      setError("");

      // The API now returns the array directly
      const categoriesData = await getAdminCategories();

      console.log("Categories data:", categoriesData); // Debug log

      if (Array.isArray(categoriesData)) {
        setCategories(categoriesData);
      } else {
        console.error("Unexpected categories data format:", categoriesData);
        setError("Invalid categories data format from server.");
        setCategories([]);
      }
    } catch (err) {
      console.error("Error fetching categories:", err);
      setError("Failed to fetch categories. Please check your connection and try again.");
      setCategories([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, [retryCount]);

  // Safe filtering and sorting
  const filteredCategories = categories.filter(
    (category) =>
      category &&
      ((category.name && category.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (category.description && category.description.toLowerCase().includes(searchTerm.toLowerCase())))
  );

  const sortedCategories = [...filteredCategories].sort((a, b) => {
    if (!a || !b) return 0;

    const aValue = a[sortConfig.key];
    const bValue = b[sortConfig.key];

    // Handle undefined/null values
    if (aValue == null) return sortConfig.direction === "asc" ? -1 : 1;
    if (bValue == null) return sortConfig.direction === "asc" ? 1 : -1;

    if (aValue < bValue) {
      return sortConfig.direction === "asc" ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortConfig.direction === "asc" ? 1 : -1;
    }
    return 0;
  });

  // Pagination
  const totalPages = Math.ceil(sortedCategories.length / itemsPerPage);
  const paginatedCategories = sortedCategories.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage);

  const handleSort = (key) => {
    setSortConfig((prev) => ({
      key,
      direction: prev.key === key && prev.direction === "asc" ? "desc" : "asc",
    }));
  };

  const handleShowModal = (category = null) => {
    setEditingCategory(category);
    setFormData(
      category
        ? {
            name: category.name || "",
            description: category.description || "",
            color_code: category.color_code || "#6c757d",
            icon: category.icon || "folder",
            order: category.order || 0,
          }
        : {
            name: "",
            description: "",
            color_code: "#6c757d",
            icon: "folder",
            order: 0,
          }
    );
    setFormErrors({});
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setEditingCategory(null);
    setFormErrors({});
  };

  const validateForm = () => {
    const errors = {};
    if (!formData.name.trim()) errors.name = "Name is required";
    if (formData.name.length > 100) errors.name = "Name must be less than 100 characters";
    if (formData.description && formData.description.length > 500) {
      errors.description = "Description must be less than 500 characters";
    }
    return errors;
  };

  const handleSave = async (e) => {
    e.preventDefault();
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    setSubmitting(true);
    try {
      if (editingCategory) {
        await updateCategory(editingCategory.id, formData);
        setSuccess("Category updated successfully");
      } else {
        await createCategory(formData);
        setSuccess("Category created successfully");
      }
      fetchCategories();
      handleCloseModal();
    } catch (err) {
      const errorMsg = err.response?.data?.message || "Failed to save category";
      setError(errorMsg);
      if (err.response?.data) {
        setFormErrors(err.response.data);
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (id) => {
    if (
      window.confirm(
        "Are you sure you want to delete this category? Articles in this category will not be deleted but will be moved to the default category."
      )
    ) {
      try {
        await deleteCategory(id);
        setSuccess("Category deleted successfully");
        fetchCategories();
      } catch (err) {
        setError("Failed to delete category");
      }
    }
  };

  const handleToggleActive = async (category) => {
    try {
      const updatedData = { ...category, is_active: !category.is_active };
      await updateCategory(category.id, updatedData);
      setSuccess(`Category ${category.is_active ? "deactivated" : "activated"} successfully`);
      fetchCategories();
    } catch (err) {
      setError("Failed to update category status");
    }
  };

  const SortableHeader = ({ label, sortKey }) => (
    <th style={{ cursor: "pointer" }} onClick={() => handleSort(sortKey)}>
      {label}
      {sortConfig.key === sortKey && <span className="ms-1">{sortConfig.direction === "asc" ? "↑" : "↓"}</span>}
    </th>
  );

  if (loading)
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" />
      </Container>
    );

  return (
    <Container fluid className="py-4">
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h3 className="mb-1">Manage Categories</h3>
              <p className="text-muted mb-0">
                {categories.length} categories • {filteredCategories.length} filtered
              </p>
            </div>
            <Button variant="primary" onClick={() => handleShowModal()}>
              <i className="fas fa-plus me-2"></i>New Category
            </Button>
          </div>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" onClose={() => setError("")} dismissible>
          {error}
          <div className="mt-2">
            <Button variant="outline-danger" size="sm" onClick={() => setRetryCount((prev) => prev + 1)}>
              <i className="fas fa-redo me-1"></i> Retry
            </Button>
          </div>
        </Alert>
      )}

      {success && (
        <Alert variant="success" onClose={() => setSuccess("")} dismissible>
          {success}
        </Alert>
      )}

      <Card>
        <Card.Header className="bg-light">
          <Row className="align-items-center">
            <Col md={6}>
              <InputGroup>
                <InputGroup.Text>
                  <i className="fas fa-search"></i>
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  placeholder="Search categories..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </InputGroup>
            </Col>
            <Col md={6} className="text-end">
              <Badge bg="secondary" className="me-2">
                Active: {categories.filter((c) => c && c.is_active).length}
              </Badge>
              <Badge bg="light" text="dark">
                Total: {categories.length}
              </Badge>
            </Col>
          </Row>
        </Card.Header>

        <div className="table-responsive">
          <Table striped hover className="mb-0">
            <thead className="bg-light">
              <tr>
                <SortableHeader label="Name" sortKey="name" />
                <th>Articles</th>
                <th>Description</th>
                <SortableHeader label="Status" sortKey="is_active" />
                <SortableHeader label="Order" sortKey="order" />
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {paginatedCategories.length > 0 ? (
                paginatedCategories.map(
                  (category) =>
                    category && (
                      <tr key={category.id}>
                        <td>
                          <div className="d-flex align-items-center">
                            {category.color_code && (
                              <span
                                className="me-2"
                                style={{
                                  width: "16px",
                                  height: "16px",
                                  backgroundColor: category.color_code,
                                  borderRadius: "3px",
                                  display: "inline-block",
                                }}
                              ></span>
                            )}
                            <strong>{category.name}</strong>
                          </div>
                        </td>
                        <td>
                          <Badge bg={category.article_count > 0 ? "primary" : "secondary"}>
                            {category.article_count || 0} articles
                          </Badge>
                        </td>
                        <td>
                          {category.description ? (
                            <span className="text-muted">{category.description}</span>
                          ) : (
                            <span className="text-muted fst-italic">No description</span>
                          )}
                        </td>
                        <td>
                          <Badge bg={category.is_active ? "success" : "secondary"}>
                            {category.is_active ? "Active" : "Inactive"}
                          </Badge>
                        </td>
                        <td>
                          <Badge bg="light" text="dark">
                            {category.order || 0}
                          </Badge>
                        </td>
                        <td>
                          <div className="d-flex gap-2">
                            <Button
                              variant="outline-primary"
                              size="sm"
                              onClick={() => handleShowModal(category)}
                              title="Edit category"
                            >
                              <i className="fas fa-edit"></i>
                            </Button>
                            <Button
                              variant={category.is_active ? "outline-warning" : "outline-success"}
                              size="sm"
                              onClick={() => handleToggleActive(category)}
                              title={category.is_active ? "Deactivate" : "Activate"}
                            >
                              <i className={`fas ${category.is_active ? "fa-eye-slash" : "fa-eye"}`}></i>
                            </Button>
                            <Button
                              variant="outline-danger"
                              size="sm"
                              onClick={() => handleDelete(category.id)}
                              title="Delete category"
                              disabled={category.article_count > 0}
                            >
                              <i className="fas fa-trash"></i>
                            </Button>
                          </div>
                        </td>
                      </tr>
                    )
                )
              ) : (
                <tr>
                  <td colSpan="6" className="text-center py-4">
                    <i className="fas fa-folder-open text-muted fa-2x mb-2"></i>
                    <p className="text-muted mb-0">No categories found</p>
                    <small className="text-muted">
                      {searchTerm ? "Try adjusting your search terms" : "Get started by creating your first category"}
                    </small>
                  </td>
                </tr>
              )}
            </tbody>
          </Table>
        </div>

        {totalPages > 1 && (
          <Card.Footer className="d-flex justify-content-center">
            <Pagination>
              <Pagination.Prev
                disabled={currentPage === 1}
                onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
              />
              {[...Array(totalPages)].map((_, index) => (
                <Pagination.Item
                  key={index + 1}
                  active={index + 1 === currentPage}
                  onClick={() => setCurrentPage(index + 1)}
                >
                  {index + 1}
                </Pagination.Item>
              ))}
              <Pagination.Next
                disabled={currentPage === totalPages}
                onClick={() => setCurrentPage((prev) => Math.min(totalPages, prev + 1))}
              />
            </Pagination>
          </Card.Footer>
        )}
      </Card>

      {/* Edit/Create Modal */}
      <Modal show={showModal} onHide={handleCloseModal} size="lg">
        <Form onSubmit={handleSave}>
          <Modal.Header closeButton>
            <Modal.Title>
              <i className="fas fa-folder me-2"></i>
              {editingCategory ? "Edit Category" : "Create New Category"}
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Row>
              <Col md={8}>
                <Form.Group className="mb-3">
                  <Form.Label>Name *</Form.Label>
                  <Form.Control
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    isInvalid={!!formErrors.name}
                    placeholder="Enter category name"
                    maxLength={100}
                  />
                  <Form.Control.Feedback type="invalid">{formErrors.name}</Form.Control.Feedback>
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Order</Form.Label>
                  <Form.Control
                    type="number"
                    value={formData.order}
                    onChange={(e) => setFormData({ ...formData, order: parseInt(e.target.value) || 0 })}
                    placeholder="Display order"
                  />
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                isInvalid={!!formErrors.description}
                placeholder="Enter category description (optional)"
                maxLength={500}
              />
              <Form.Text className="text-muted">{formData.description.length}/500 characters</Form.Text>
              <Form.Control.Feedback type="invalid">{formErrors.description}</Form.Control.Feedback>
            </Form.Group>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Color</Form.Label>
                  <Form.Control
                    type="color"
                    value={formData.color_code}
                    onChange={(e) => setFormData({ ...formData, color_code: e.target.value })}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Icon</Form.Label>
                  <Form.Select
                    value={formData.icon}
                    onChange={(e) => setFormData({ ...formData, icon: e.target.value })}
                  >
                    <option value="folder">📁 Folder</option>
                    <option value="book">📚 Book</option>
                    <option value="article">📄 Article</option>
                    <option value="knowledge">🧠 Knowledge</option>
                    <option value="help">❓ Help</option>
                    <option value="guide">📖 Guide</option>
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            {editingCategory && (
              <Alert variant="info" className="mb-0">
                <i className="fas fa-info-circle me-2"></i>
                This category contains {editingCategory.article_count || 0} articles
              </Alert>
            )}
          </Modal.Body>
          <Modal.Footer>
  
            <Button variant="secondary" onClick={handleCloseModal} disabled={submitting}>
              Cancel
            </Button>
            <Button variant="primary" type="submit" disabled={submitting}>
              {submitting ? (
                <>
                  <Spinner animation="border" size="sm" className="me-2" />
                  {editingCategory ? "Updating..." : "Creating..."}
                </>
              ) : (
                <>
                  <i className="fas fa-save me-2"></i>
                  {editingCategory ? "Update Category" : "Create Category"}
                </>
              )}
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>
    </Container>
  );
};

export default CategoryManagementPage;



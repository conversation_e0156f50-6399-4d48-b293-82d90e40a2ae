// src/layouts/FarmerLayout.jsx

import React from "react";
import { Outlet, NavLink } from "react-router-dom";
import { Navbar, Container, Nav, NavDropdown } from "react-bootstrap";
import useAuth from "../hooks/useAuth";
import AnnouncementBanner from "../components/AnnouncementBanner";


const FarmerLayout = () => {
  const { user, logout } = useAuth();

  return (
    <div>
      <Navbar bg="success" variant="dark" expand="lg">
        <Container>
          <Navbar.Brand as={NavLink} to="/">
            <i className="bi bi-tree-fill me-2"></i>AgriAdvisor
          </Navbar.Brand>
          <Navbar.Toggle aria-controls="farmer-navbar-nav" />
          <Navbar.Collapse id="farmer-navbar-nav">
            <Nav className="me-auto">
              <Nav.Link as={NavLink} to="/farmer/dashboard">
                My Dashboard
              </Nav.Link>

              <Nav.Link as={NavLink} to="/farmer/my-enterprise-plans">
                {" "}
                My Enterprise Plans
              </Nav.Link>

              <Nav.Link as={NavLink} to="/browse-services">
                Book a Service
              </Nav.Link>

              <Nav.Link as={NavLink} to="/my-bookings">
                My Bookings
              </Nav.Link>

              <Nav.Link as={NavLink} to="/farmer/my-farm">
                My Farm
              </Nav.Link>

              <Nav.Link as={NavLink} to="/farmer/my-action-plans">
                My Action Plans
              </Nav.Link>
              
            </Nav>
            <Nav>
              <NavDropdown title={`Welcome, ${user?.first_name || user?.username}`} id="basic-nav-dropdown" align="end">
                <NavDropdown.Item as={NavLink} to="/farmer/profile">
                  My Profile
                </NavDropdown.Item>
                <NavDropdown.Divider />
                <NavDropdown.Item onClick={logout}>Logout</NavDropdown.Item>
              </NavDropdown>
            </Nav>
          </Navbar.Collapse>
        </Container>
      </Navbar>
      <AnnouncementBanner />
      <Container className="mt-4">
        <Outlet />
      </Container>
    </div>
  );
};

export default FarmerLayout;



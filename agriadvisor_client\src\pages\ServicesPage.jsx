// src/pages/ServicesPage.jsx

import React, { useState, useEffect } from "react";
import { Container, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap"; // <-- Import Row and Col
import { getServices, createService, updateService, deleteService } from "../services/serviceService";
import ServiceModal from "../components/ServiceModal";
import AdminServiceCard from "../components/AdminServiceCard"; // <-- THE CRUCIAL IMPORT

const ServicesPage = () => {
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  const [showModal, setShowModal] = useState(false);
  const [editingService, setEditingService] = useState(null);

  const fetchServices = async () => {
    try {
      setLoading(true);
      console.log("ServicesPage: Fetching services...");
      const response = await getServices();
      console.log("ServicesPage: Services response", response);
      const data = response.data || response;
      console.log("ServicesPage: Services data", data);
      setServices(data);
    } catch (err) {
      console.error("ServicesPage: Error fetching services", err);
      setError("Failed to fetch services.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchServices();
  }, []);

  const handleShowAddModal = () => {
    setEditingService(null);
    setShowModal(true);
  };

  const handleShowEditModal = (service) => {
    setEditingService(service);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setEditingService(null);
  };

  const handleSaveService = async (serviceData) => {
    console.log("ServicesPage: Attempting to save service", serviceData);
    try {
      let result;
      if (editingService) {
        console.log("ServicesPage: Updating existing service", editingService.id);
        result = await updateService(editingService.id, serviceData);
      } else {
        console.log("ServicesPage: Creating new service");
        result = await createService(serviceData);
      }
      console.log("ServicesPage: Save successful", result);
      handleCloseModal();
      fetchServices();
    } catch (err) {
      console.error("ServicesPage: Save failed", err);
      console.error("ServicesPage: Error response", err.response?.data);

      const errorMessage = err.response?.data?.detail ||
                          err.response?.data?.message ||
                          err.message ||
                          "Failed to save service. Your login may have expired. Please try logging in again.";
      alert(errorMessage);
    }
  };

  const handleDeleteService = async (id) => {
    if (window.confirm("Are you sure you want to delete this service?")) {
      try {
        await deleteService(id);
        fetchServices();
      } catch (err) {
        alert("Failed to delete service. It might be in use in a booking.");
      }
    }
  };

  if (loading)
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" />
      </Container>
    );
  if (error)
    return (
      <Container className="mt-4">
        <Alert variant="danger">{error}</Alert>
      </Container>
    );

  return (
    <Container fluid>
      <div className="d-flex justify-content-end align-items-center mb-4">
        <Button variant="primary" onClick={handleShowAddModal}>
          <i className="bi bi-plus-lg me-2"></i>Add New Service
        </Button>
      </div>

      <Row>
        {console.log("ServicesPage: Rendering services", services, "Length:", services.length)}
        {services.length > 0 ? (
          services.map((service) => {
            console.log("ServicesPage: Rendering service", service);
            return (
              <Col key={service.id} sm={12} md={6} lg={4} xl={3} className="mb-4">
                <AdminServiceCard service={service} onEdit={handleShowEditModal} onDelete={handleDeleteService} />
              </Col>
            );
          })
        ) : (
          <Col>
            <Alert variant="info">No services found. Add one to get started!</Alert>
          </Col>
        )}
      </Row>

      <ServiceModal show={showModal} onHide={handleCloseModal} onSave={handleSaveService} service={editingService} />
    </Container>
  );
};
export default ServicesPage;




// // src/pages/ServicesPage.jsx
// import React, { useState, useEffect } from "react";
// import { Container, Table, Button, Spinner, Alert } from "react-bootstrap";
// import { getServices } from "../services/serviceService";
// // Modal and actions to be added later
// // import ServiceModal from '../components/ServiceModal';

// const ServicesPage = () => {
//   const [services, setServices] = useState([]);
//   const [loading, setLoading] = useState(true);
//   const [error, setError] = useState("");

//   useEffect(() => {
//     const fetchServices = async () => {
//       try {
//         const data = await getServices();
//         setServices(data);
//       } catch (err) {
//         setError("Failed to fetch services.");
//       } finally {
//         setLoading(false);
//       }
//     };
//     fetchServices();
//   }, []);

//   if (loading)
//     return (
//       <Container className="text-center mt-5">
//         <Spinner animation="border" />
//       </Container>
//     );
//   if (error)
//     return (
//       <Container className="mt-4">
//         <Alert variant="danger">{error}</Alert>
//       </Container>
//     );

//   return (
//     <Container fluid>
//       <div className="d-flex justify-content-between align-items-center mb-4">
//         <Button variant="primary" disabled>
//           <i className="bi bi-plus-lg me-2"></i>Add New Service
//         </Button>
//       </div>
//       <Table striped bordered hover responsive>
//         <thead>
//           <tr>
//             <th>Name</th>
//             <th>Description</th>
//             <th>Price</th>
//             <th>Duration</th>
//             <th>Actions</th>
//           </tr>
//         </thead>
//         <tbody>
//           {services.length > 0 ? (
//             services.map((service) => (
//               <tr key={service.id}>
//                 <td>{service.name}</td>
//                 <td>{service.description}</td>
//                 <td>
//                   ${service.price} {service.currency}
//                 </td>
//                 <td>{service.duration_minutes} mins</td>
//                 <td>
//                   <Button variant="outline-secondary" size="sm" className="me-2" disabled>
//                     Edit
//                   </Button>
//                   <Button variant="outline-danger" size="sm" disabled>
//                     Delete
//                   </Button>
//                 </td>
//               </tr>
//             ))
//           ) : (
//             <tr>
//               <td colSpan="5" className="text-center">
//                 No services found.
//               </td>
//             </tr>
//           )}
//         </tbody>
//       </Table>
//     </Container>
//   );
// };
// export default ServicesPage;

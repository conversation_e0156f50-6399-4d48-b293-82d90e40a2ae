// src/components/StatCard.jsx
import React from "react";
import { Card } from "react-bootstrap";

const cardStyle = {
  background: "linear-gradient(135deg, #1E90FF, #87CEEB)",
  color: "white",
  borderRadius: "15px",
  border: "none",
};
const iconStyle = { fontSize: "2.5rem", opacity: 0.8 };

const StatCard = ({ title, value, icon }) => {
  console.log(`StatCard: Rendering ${title} with value`, value);
  return (
    <Card style={cardStyle} className="h-100">
      <Card.Body>
        <div className="d-flex justify-content-between align-items-center">
          <div>
            <h5 className="card-title">{title}</h5>
            <h2>{value !== undefined && value !== null ? value : '0'}</h2>
          </div>
          <i className={icon} style={iconStyle}></i>
        </div>
      </Card.Body>
    </Card>
  );
};
export default StatCard;

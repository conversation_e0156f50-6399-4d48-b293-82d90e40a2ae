// src/contexts/AuthContext.jsx

import React, { createContext, useReducer, useEffect, useMemo } from "react";
import { jwtDecode } from "jwt-decode";
import { loginUser } from "../services/authService";
import api from "../services/api";

const initialState = {
  user: null,
  accessToken: null,
  loading: true,
};

const AuthContext = createContext(initialState);

function authReducer(state, action) {
  switch (action.type) {
    case "INITIALIZE":
      return {
        ...state,
        user: action.payload.user,
        accessToken: action.payload.accessToken,
        loading: false,
      };
    case "LOGIN_SUCCESS":
      return {
        ...state,
        user: action.payload.user,
        accessToken: action.payload.accessToken,
      };
    case "LOGOUT":
      return {
        ...state,
        user: null,
        accessToken: null,
      };
    default:
      return state;
  }
}

export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  useEffect(() => {
    const initialize = () => {
      try {
        const accessToken = localStorage.getItem("access_token");
        if (accessToken) {
          const user = jwtDecode(accessToken);
          api.defaults.headers.common["Authorization"] = `Bearer ${accessToken}`;
          dispatch({ type: "INITIALIZE", payload: { user, accessToken } });
        } else {
          dispatch({ type: "INITIALIZE", payload: { user: null, accessToken: null } });
        }
      } catch (error) {
        dispatch({ type: "INITIALIZE", payload: { user: null, accessToken: null } });
      }
    };
    initialize();
  }, []);

  const login = async (username, password) => {
    const response = await loginUser({ username, password });
    const { access, refresh } = response.data;
    const user = jwtDecode(access);

    localStorage.setItem("access_token", access);
    localStorage.setItem("refresh_token", refresh);
    api.defaults.headers.common["Authorization"] = `Bearer ${access}`;

    dispatch({ type: "LOGIN_SUCCESS", payload: { user, accessToken: access } });
  };

  const logout = () => {
    localStorage.removeItem("access_token");
    localStorage.removeItem("refresh_token");
    delete api.defaults.headers.common["Authorization"];
    dispatch({ type: "LOGOUT" });
    window.location.href = "/login";
  };

  const contextValue = useMemo(
    () => ({
      ...state,
      login,
      logout,
    }),
    [state]
  );

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>;
};

export default AuthContext;



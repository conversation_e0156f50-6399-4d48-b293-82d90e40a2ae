// // src/components/NewBudgetModal.jsx

// import React, { useState, useEffect } from "react";
// import { Modal, Button, Form, Spinner } from "react-bootstrap";
// import { getFarmers } from "../services/farmerService"; // Reuse the farmer service

// const NewBudgetModal = ({ show, onHide, onSave }) => {
//   const [farmers, setFarmers] = useState([]);
//   const [loading, setLoading] = useState(true);

//   // Form state
//   const [name, setName] = useState("");
//   const [description, setDescription] = useState("");
//   const [selectedFarmer, setSelectedFarmer] = useState("");

//   useEffect(() => {
//     // Fetch the list of farmers when the modal is about to be shown
//     if (show) {
//       const fetchFarmers = async () => {
//         try {
//           setLoading(true);
//           const response = await getFarmers();
//           setFarmers(response.data);
//         } catch (error) {
//           console.error("Failed to fetch farmers for modal", error);
//         } finally {
//           setLoading(false);
//         }
//       };
//       fetchFarmers();
//     }
//   }, [show]);

//   const handleSubmit = (e) => {
//     e.preventDefault();
//     onSave({ name, description, farmer: selectedFarmer });
//   };

//   return (
//     <Modal show={show} onHide={onHide}>
//       <Form onSubmit={handleSubmit}>
//         <Modal.Header closeButton>
//           <Modal.Title>Create New Budget</Modal.Title>
//         </Modal.Header>
//         <Modal.Body>
//           {loading ? (
//             <Spinner animation="border" />
//           ) : (
//             <>
//               <Form.Group className="mb-3">
//                 <Form.Label>Budget Name</Form.Label>
//                 <Form.Control
//                   type="text"
//                   value={name}
//                   onChange={(e) => setName(e.target.value)}
//                   placeholder="e.g., 2025 Harvest Season"
//                   required
//                 />
//               </Form.Group>
//               <Form.Group className="mb-3">
//                 <Form.Label>Assign to Farmer</Form.Label>
//                 <Form.Select value={selectedFarmer} onChange={(e) => setSelectedFarmer(e.target.value)} required>
//                   <option value="">Select a farmer...</option>
//                   {farmers.map((farmer) => (
//                     <option key={farmer.id} value={farmer.id}>
//                       {farmer.first_name} {farmer.last_name} ({farmer.username})
//                     </option>
//                   ))}
//                 </Form.Select>
//               </Form.Group>
//               <Form.Group className="mb-3">
//                 <Form.Label>Description (Optional)</Form.Label>
//                 <Form.Control
//                   as="textarea"
//                   rows={3}
//                   value={description}
//                   onChange={(e) => setDescription(e.target.value)}
//                 />
//               </Form.Group>
//             </>
//           )}
//         </Modal.Body>
//         <Modal.Footer>
//           <Button variant="secondary" onClick={onHide}>
//             Cancel
//           </Button>
//           <Button variant="primary" type="submit" disabled={loading}>
//             Save Budget
//           </Button>
//         </Modal.Footer>
//       </Form>
//     </Modal>
//   );
// };
// export default NewBudgetModal;



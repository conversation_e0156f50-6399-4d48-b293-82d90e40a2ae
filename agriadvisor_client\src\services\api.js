// src/services/api.js

import axios from "axios";

const api = axios.create({
  baseURL: "http://localhost:8001/api",
});

// Request Interceptor: Attaches the JWT token to every outgoing request.
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("access_token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response Interceptor: Handles token refresh on 401 errors.
api.interceptors.response.use(
  (response) => response, // If the response is successful, just pass it through.
  async (error) => {
    const originalRequest = error.config;

    // Check if the error is a 401 and we haven't already retried.
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem("refresh_token");
        if (!refreshToken) {
          // If there's no refresh token, we can't do anything.
          // Trigger a logout or redirect.
          window.location.href = "/login";
          return Promise.reject(error);
        }

        // Use a separate, clean axios instance for the refresh call to avoid looping interceptors.
        const response = await axios.post("http://localhost:8001/api/accounts/token/refresh/", {
          refresh: refreshToken,
        });

        const { access } = response.data;

        // Update storage and the default header for subsequent requests.
        localStorage.setItem("access_token", access);
        api.defaults.headers.common["Authorization"] = `Bearer ${access}`;

        // Update the header of the original request that failed.
        originalRequest.headers["Authorization"] = `Bearer ${access}`;

        // Retry the original request with the new token.
        return api(originalRequest);
      } catch (refreshError) {
        // If the refresh token itself is invalid, the user must log in again.
        console.error("Session expired. Please log in again.", refreshError);
        localStorage.removeItem("access_token");
        localStorage.removeItem("refresh_token");
        window.location.href = "/login";
        return Promise.reject(refreshError);
      }
    }

    // For all other errors, just reject.
    return Promise.reject(error);
  }
);

export default api;



# api/models.py

from django.db import models
from django.core.validators import MinValueValidator

# A helper function to create a standardized nutrient field
def nutrient_field(help_text="Percent (%) DM", max_digits=7, decimal_places=3):
    return models.DecimalField(
        max_digits=max_digits,
        decimal_places=decimal_places,
        validators=[MinValueValidator(0.0)],
        help_text=help_text,
        null=True, blank=True # Allow fields to be optional
    )

class NutrientProfile(models.Model):
    """
    An abstract base class to hold the comprehensive and consistent set of nutrients.
    All ingredient models will inherit from this to avoid repetition.
    """
    # --- PROXIMATES ---
    dry_matter_as_fed = models.DecimalField(max_digits=5, decimal_places=2, help_text="Dry Matter (%) as fed", validators=[MinValueValidator(0.0)])
    crude_protein = nutrient_field(help_text="Crude <PERSON>tein (% DM)")
    crude_fiber = nutrient_field(help_text="Crude <PERSON>ber (% DM)")
    ndf = nutrient_field(help_text="Neutral Detergent Fiber (% DM)")
    adf = nutrient_field(help_text="Acid Detergent Fiber (% DM)")
    lignin = nutrient_field(help_text="Lignin (% DM)")
    ether_extract = nutrient_field(help_text="Ether Extract / Crude Fat (% DM)")
    ash = nutrient_field(help_text="Ash (% DM)")
    insoluble_ash = nutrient_field(help_text="Acid Insoluble Ash (% DM)")
    starch_pol = nutrient_field(help_text="Starch (Polarimetry) (% DM)")
    starch_enz = nutrient_field(help_text="Starch (Enzymatic) (% DM)")
    total_sugars = nutrient_field(help_text="Total Sugars (% DM)")
    gross_energy = nutrient_field(help_text="Gross Energy (MJ/kg DM)", max_digits=8, decimal_places=3)
    
    # --- AMINO ACIDS ---
    lysine = nutrient_field(help_text="Lysine (% DM)")
    methionine = nutrient_field(help_text="Methionine (% DM)")
    cysteine = nutrient_field(help_text="Cysteine (% DM)")
    threonine = nutrient_field(help_text="Threonine (% DM)")
    tryptophan = nutrient_field(help_text="Tryptophan (% DM)")
    arginine = nutrient_field(help_text="Arginine (% DM)")
    histidine = nutrient_field(help_text="Histidine (% DM)")
    isoleucine = nutrient_field(help_text="Isoleucine (% DM)")
    leucine = nutrient_field(help_text="Leucine (% DM)")
    valine = nutrient_field(help_text="Valine (% DM)")

    # --- FATTY ACIDS ---
    oleic_acid = nutrient_field(help_text="Oleic Acid (C18:1) (% DM)")
    linoleic_acid = nutrient_field(help_text="Linoleic Acid (C18:2) (% DM)")
    linolenic_acid = nutrient_field(help_text="Linolenic Acid (C18:3) (% DM)")
    palmitic_acid = nutrient_field(help_text="Palmitic Acid (C16:0) (% DM)")
    stearic_acid = nutrient_field(help_text="Stearic Acid (C18:0) (% DM)")
    
    # --- MINERALS ---
    ca = nutrient_field(help_text="Calcium (Ca) (% DM)")
    p = nutrient_field(help_text="Phosphorus (P) (% DM)")
    k = nutrient_field(help_text="Potassium (K) (% DM)")
    na = nutrient_field(help_text="Sodium (Na) (% DM)")
    cl = nutrient_field(help_text="Chlorine (Cl) (% DM)")
    mg = nutrient_field(help_text="Magnesium (Mg) (% DM)")
    s = nutrient_field(help_text="Sulphur (S) (% DM)")
    mn = nutrient_field(help_text="Manganese (Mn) (mg/kg DM)", max_digits=10, decimal_places=2)
    zn = nutrient_field(help_text="Zinc (Zn) (mg/kg DM)", max_digits=10, decimal_places=2)
    cu = nutrient_field(help_text="Copper (Cu) (mg/kg DM)", max_digits=10, decimal_places=2)
    fe = nutrient_field(help_text="Iron (Fe) (mg/kg DM)", max_digits=10, decimal_places=2)
    se = nutrient_field(help_text="Selenium (Se) (mg/kg DM)", max_digits=10, decimal_places=2)
    
    class Meta:
        abstract = True # This makes it a base class, not a database table itself

class GlobalIngredient(NutrientProfile):
    """ The master list of common feed ingredients. Inherits all nutrient fields. """
    name = models.CharField(max_length=255, unique=True)
    description = models.TextField(blank=True, null=True)
    
    class Meta:
        ordering = ['name']
    def __str__(self): return self.name

class FarmerIngredient(NutrientProfile):
    """ An ingredient specific to a farmer's library. Inherits all nutrient fields. """
    farmer_id = models.PositiveIntegerField(db_index=True) 
    tenant_id = models.PositiveIntegerField(db_index=True)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    
    class Meta:
        ordering = ['name']
        unique_together = ('farmer_id', 'name')
    def __str__(self): return f"{self.name} (Farmer ID: {self.farmer_id})"

class FormulationIngredient(NutrientProfile):
    """ A line item in a formulation. Also inherits (copies) all nutrient fields. """
    formulation = models.ForeignKey('Formulation', on_delete=models.CASCADE, related_name="ingredients")
    ingredient_name = models.CharField(max_length=255)
    ingredient_source_id = models.PositiveIntegerField() # ID of the Global or Farmer ingredient
    ingredient_source_type = models.CharField(max_length=50, help_text="e.g., 'Global' or 'Farmer'")
    amount_kg = models.DecimalField(max_digits=10, decimal_places=3)
    
    class Meta:
        ordering = ['-amount_kg']
        unique_together = ('formulation', 'ingredient_name')

class NutrientRequirement(models.Model):
    """ Stores the nutritional requirement profile for an animal. """
    name = models.CharField(max_length=255, unique=True)
    min_crude_protein = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    max_crude_protein = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    
    # --- CORRECTED: ADDED MISSING ENERGY REQUIREMENT FIELD ---
    target_metabolizable_energy = models.DecimalField(max_digits=8, decimal_places=3, null=True, blank=True, help_text="Target Metabolizable Energy (MJ/kg DM)")

    class Meta:
        ordering = ['name']
    def __str__(self): return self.name
    

class Formulation(models.Model):
    """ The main object for a single, complete feed ration recipe. """
    name = models.CharField(max_length=255)
    farmer_id = models.PositiveIntegerField()
    expert_id = models.PositiveIntegerField()
    tenant_id = models.PositiveIntegerField()
    based_on_requirement = models.ForeignKey(NutrientRequirement, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} (Farmer ID: {self.farmer_id})"
    



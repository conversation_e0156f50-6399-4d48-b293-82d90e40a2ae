{"version": 3, "sources": ["../../react-router-dom/dist/index.js", "../../react-router-bootstrap/LinkContainer.js", "../../react-router-bootstrap/index.js"], "sourcesContent": ["/**\n * react-router-dom v7.8.2\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\n\"use strict\";\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// index.ts\nvar index_exports = {};\n__export(index_exports, {\n  HydratedRouter: () => import_dom.HydratedRouter,\n  RouterProvider: () => import_dom.RouterProvider\n});\nmodule.exports = __toCommonJS(index_exports);\nvar import_dom = require(\"react-router/dom\");\n__reExport(index_exports, require(\"react-router\"), module.exports);\n// Annotate the CommonJS export names for ESM import in node:\n0 && (module.exports = {\n  HydratedRouter,\n  RouterProvider,\n  ...require(\"react-router\")\n});\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _reactRouterDom = require(\"react-router-dom\");\n\nvar _excluded = [\"children\", \"onClick\", \"replace\", \"to\", \"state\", \"activeClassName\", \"className\", \"activeStyle\", \"style\", \"isActive\"];\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\nvar isModifiedEvent = function isModifiedEvent(event) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n};\n\nvar LinkContainer = function LinkContainer(_ref) {\n  var children = _ref.children,\n      onClick = _ref.onClick,\n      _ref$replace = _ref.replace,\n      replace = _ref$replace === void 0 ? false : _ref$replace,\n      to = _ref.to,\n      state = _ref.state,\n      _ref$activeClassName = _ref.activeClassName,\n      activeClassName = _ref$activeClassName === void 0 ? 'active' : _ref$activeClassName,\n      className = _ref.className,\n      activeStyle = _ref.activeStyle,\n      style = _ref.style,\n      getIsActive = _ref.isActive,\n      props = _objectWithoutProperties(_ref, _excluded);\n\n  var path = _typeof(to) === 'object' ? to.pathname || '' : to;\n  var navigate = (0, _reactRouterDom.useNavigate)();\n  var href = (0, _reactRouterDom.useHref)(typeof to === 'string' ? {\n    pathname: to\n  } : to);\n  var match = (0, _reactRouterDom.useMatch)(path);\n  var location = (0, _reactRouterDom.useLocation)();\n\n  var child = _react[\"default\"].Children.only(children);\n\n  var isActive = !!(getIsActive ? typeof getIsActive === 'function' ? getIsActive(match, location) : getIsActive : match);\n\n  var handleClick = function handleClick(event) {\n    if (children.props.onClick) {\n      children.props.onClick(event);\n    }\n\n    if (onClick) {\n      onClick(event);\n    }\n\n    if (!event.defaultPrevented && // onClick prevented default\n    event.button === 0 && // ignore right clicks\n    !isModifiedEvent(event) // ignore clicks with modifier keys\n    ) {\n      event.preventDefault();\n      navigate(to, {\n        replace: replace,\n        state: state\n      });\n    }\n  };\n\n  return /*#__PURE__*/_react[\"default\"].cloneElement(child, _objectSpread(_objectSpread({}, props), {}, {\n    className: [className, child.props.className, isActive ? activeClassName : null].join(' ').trim(),\n    style: isActive ? _objectSpread(_objectSpread({}, style), activeStyle) : style,\n    href: href,\n    onClick: handleClick\n  }));\n};\n\nLinkContainer.propTypes = {\n  children: _propTypes[\"default\"].element.isRequired,\n  onClick: _propTypes[\"default\"].func,\n  replace: _propTypes[\"default\"].bool,\n  to: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].object]).isRequired,\n  state: _propTypes[\"default\"].object,\n  className: _propTypes[\"default\"].string,\n  activeClassName: _propTypes[\"default\"].string,\n  style: _propTypes[\"default\"].objectOf(_propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number])),\n  activeStyle: _propTypes[\"default\"].objectOf(_propTypes[\"default\"].oneOfType([_propTypes[\"default\"].string, _propTypes[\"default\"].number])),\n  isActive: _propTypes[\"default\"].oneOfType([_propTypes[\"default\"].func, _propTypes[\"default\"].bool])\n};\nvar _default = LinkContainer;\nexports[\"default\"] = _default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nObject.defineProperty(exports, \"LinkContainer\", {\n  enumerable: true,\n  get: function get() {\n    return _LinkContainer[\"default\"];\n  }\n});\n\nvar _LinkContainer = _interopRequireDefault(require(\"./LinkContainer\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAWA,QAAI,YAAY,OAAO;AACvB,QAAI,mBAAmB,OAAO;AAC9B,QAAI,oBAAoB,OAAO;AAC/B,QAAI,eAAe,OAAO,UAAU;AACpC,QAAI,WAAW,CAAC,QAAQ,QAAQ;AAC9B,eAAS,QAAQ;AACf,kBAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAAA,IAChE;AACA,QAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,QAAI,aAAa,CAAC,QAAQ,KAAK,kBAAkB,YAAY,QAAQ,KAAK,SAAS,GAAG,gBAAgB,YAAY,cAAc,KAAK,SAAS;AAC9I,QAAIA,gBAAe,CAAC,QAAQ,YAAY,UAAU,CAAC,GAAG,cAAc,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG;AAGzF,QAAI,gBAAgB,CAAC;AACrB,aAAS,eAAe;AAAA,MACtB,gBAAgB,MAAM,WAAW;AAAA,MACjC,gBAAgB,MAAM,WAAW;AAAA,IACnC,CAAC;AACD,WAAO,UAAUA,cAAa,aAAa;AAC3C,QAAI,aAAa;AACjB,eAAW,eAAe,yDAAyB,OAAO,OAAO;AAAA;AAAA;;;ACtCjE;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,SAAS,IAAI;AAErB,QAAI,SAAS,uBAAuB,eAAgB;AAEpD,QAAI,aAAa,uBAAuB,oBAAqB;AAE7D,QAAI,kBAAkB;AAEtB,QAAI,YAAY,CAAC,YAAY,WAAW,WAAW,MAAM,SAAS,mBAAmB,aAAa,eAAe,SAAS,UAAU;AAEpI,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,WAAW,IAAI;AAAA,IAAG;AAEhG,aAAS,QAAQ,QAAQ,gBAAgB;AAAE,UAAI,OAAO,OAAO,KAAK,MAAM;AAAG,UAAI,OAAO,uBAAuB;AAAE,YAAI,UAAU,OAAO,sBAAsB,MAAM;AAAG,2BAAmB,UAAU,QAAQ,OAAO,SAAU,KAAK;AAAE,iBAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,QAAY,CAAC,IAAI,KAAK,KAAK,MAAM,MAAM,OAAO;AAAA,MAAG;AAAE,aAAO;AAAA,IAAM;AAEpV,aAAS,cAAc,QAAQ;AAAE,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,YAAI,SAAS,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AAAG,YAAI,IAAI,QAAQ,OAAO,MAAM,GAAG,IAAE,EAAE,QAAQ,SAAU,KAAK;AAAE,0BAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,QAAG,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC,IAAI,QAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAAE,iBAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,QAAG,CAAC;AAAA,MAAG;AAAE,aAAO;AAAA,IAAQ;AAEzf,aAAS,gBAAgB,KAAK,KAAK,OAAO;AAAE,UAAI,OAAO,KAAK;AAAE,eAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAK,CAAC;AAAA,MAAG,OAAO;AAAE,YAAI,GAAG,IAAI;AAAA,MAAO;AAAE,aAAO;AAAA,IAAK;AAEhN,aAAS,QAAQ,KAAK;AAAE;AAA2B,aAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUC,MAAK;AAAE,eAAO,OAAOA;AAAA,MAAK,IAAI,SAAUA,MAAK;AAAE,eAAOA,QAAO,cAAc,OAAO,UAAUA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,MAAK,GAAG,QAAQ,GAAG;AAAA,IAAG;AAE/U,aAAS,yBAAyB,QAAQ,UAAU;AAAE,UAAI,UAAU,KAAM,QAAO,CAAC;AAAG,UAAI,SAAS,8BAA8B,QAAQ,QAAQ;AAAG,UAAI,KAAK;AAAG,UAAI,OAAO,uBAAuB;AAAE,YAAI,mBAAmB,OAAO,sBAAsB,MAAM;AAAG,aAAK,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAAE,gBAAM,iBAAiB,CAAC;AAAG,cAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAAU,cAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,QAAQ,GAAG,EAAG;AAAU,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO;AAAA,IAAQ;AAE3e,aAAS,8BAA8B,QAAQ,UAAU;AAAE,UAAI,UAAU,KAAM,QAAO,CAAC;AAAG,UAAI,SAAS,CAAC;AAAG,UAAI,aAAa,OAAO,KAAK,MAAM;AAAG,UAAI,KAAK;AAAG,WAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAAE,cAAM,WAAW,CAAC;AAAG,YAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAAU,eAAO,GAAG,IAAI,OAAO,GAAG;AAAA,MAAG;AAAE,aAAO;AAAA,IAAQ;AAElT,QAAI,kBAAkB,SAASC,iBAAgB,OAAO;AACpD,aAAO,CAAC,EAAE,MAAM,WAAW,MAAM,UAAU,MAAM,WAAW,MAAM;AAAA,IACpE;AAEA,QAAI,gBAAgB,SAASC,eAAc,MAAM;AAC/C,UAAI,WAAW,KAAK,UAChB,UAAU,KAAK,SACf,eAAe,KAAK,SACpB,UAAU,iBAAiB,SAAS,QAAQ,cAC5C,KAAK,KAAK,IACV,QAAQ,KAAK,OACb,uBAAuB,KAAK,iBAC5B,kBAAkB,yBAAyB,SAAS,WAAW,sBAC/D,YAAY,KAAK,WACjB,cAAc,KAAK,aACnB,QAAQ,KAAK,OACb,cAAc,KAAK,UACnB,QAAQ,yBAAyB,MAAM,SAAS;AAEpD,UAAI,OAAO,QAAQ,EAAE,MAAM,WAAW,GAAG,YAAY,KAAK;AAC1D,UAAI,YAAY,GAAG,gBAAgB,aAAa;AAChD,UAAI,QAAQ,GAAG,gBAAgB,SAAS,OAAO,OAAO,WAAW;AAAA,QAC/D,UAAU;AAAA,MACZ,IAAI,EAAE;AACN,UAAI,SAAS,GAAG,gBAAgB,UAAU,IAAI;AAC9C,UAAI,YAAY,GAAG,gBAAgB,aAAa;AAEhD,UAAI,QAAQ,OAAO,SAAS,EAAE,SAAS,KAAK,QAAQ;AAEpD,UAAI,WAAW,CAAC,EAAE,cAAc,OAAO,gBAAgB,aAAa,YAAY,OAAO,QAAQ,IAAI,cAAc;AAEjH,UAAI,cAAc,SAASC,aAAY,OAAO;AAC5C,YAAI,SAAS,MAAM,SAAS;AAC1B,mBAAS,MAAM,QAAQ,KAAK;AAAA,QAC9B;AAEA,YAAI,SAAS;AACX,kBAAQ,KAAK;AAAA,QACf;AAEA,YAAI,CAAC,MAAM;AAAA,QACX,MAAM,WAAW;AAAA,QACjB,CAAC,gBAAgB,KAAK,GACpB;AACA,gBAAM,eAAe;AACrB,mBAAS,IAAI;AAAA,YACX;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAEA,aAAoB,OAAO,SAAS,EAAE,aAAa,OAAO,cAAc,cAAc,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,QACpG,WAAW,CAAC,WAAW,MAAM,MAAM,WAAW,WAAW,kBAAkB,IAAI,EAAE,KAAK,GAAG,EAAE,KAAK;AAAA,QAChG,OAAO,WAAW,cAAc,cAAc,CAAC,GAAG,KAAK,GAAG,WAAW,IAAI;AAAA,QACzE;AAAA,QACA,SAAS;AAAA,MACX,CAAC,CAAC;AAAA,IACJ;AAEA,kBAAc,YAAY;AAAA,MACxB,UAAU,WAAW,SAAS,EAAE,QAAQ;AAAA,MACxC,SAAS,WAAW,SAAS,EAAE;AAAA,MAC/B,SAAS,WAAW,SAAS,EAAE;AAAA,MAC/B,IAAI,WAAW,SAAS,EAAE,UAAU,CAAC,WAAW,SAAS,EAAE,QAAQ,WAAW,SAAS,EAAE,MAAM,CAAC,EAAE;AAAA,MAClG,OAAO,WAAW,SAAS,EAAE;AAAA,MAC7B,WAAW,WAAW,SAAS,EAAE;AAAA,MACjC,iBAAiB,WAAW,SAAS,EAAE;AAAA,MACvC,OAAO,WAAW,SAAS,EAAE,SAAS,WAAW,SAAS,EAAE,UAAU,CAAC,WAAW,SAAS,EAAE,QAAQ,WAAW,SAAS,EAAE,MAAM,CAAC,CAAC;AAAA,MACnI,aAAa,WAAW,SAAS,EAAE,SAAS,WAAW,SAAS,EAAE,UAAU,CAAC,WAAW,SAAS,EAAE,QAAQ,WAAW,SAAS,EAAE,MAAM,CAAC,CAAC;AAAA,MACzI,UAAU,WAAW,SAAS,EAAE,UAAU,CAAC,WAAW,SAAS,EAAE,MAAM,WAAW,SAAS,EAAE,IAAI,CAAC;AAAA,IACpG;AACA,QAAI,WAAW;AACf,YAAQ,SAAS,IAAI;AAAA;AAAA;;;ACtGrB;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,WAAO,eAAe,SAAS,iBAAiB;AAAA,MAC9C,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,eAAe,SAAS;AAAA,MACjC;AAAA,IACF,CAAC;AAED,QAAI,iBAAiB,uBAAuB,uBAA0B;AAEtE,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,WAAW,IAAI;AAAA,IAAG;AAAA;AAAA;", "names": ["__toCommonJS", "obj", "isModifiedEvent", "LinkContainer", "handleClick"]}
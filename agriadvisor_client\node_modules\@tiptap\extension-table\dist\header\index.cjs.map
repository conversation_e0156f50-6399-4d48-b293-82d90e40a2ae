{"version": 3, "sources": ["../../src/header/index.ts", "../../src/header/table-header.ts"], "sourcesContent": ["export * from './table-header.js'\n", "import '../types.js'\n\nimport { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface TableHeaderOptions {\n  /**\n   * The HTML attributes for a table header node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\n/**\n * This extension allows you to create table headers.\n * @see https://www.tiptap.dev/api/nodes/table-header\n */\nexport const TableHeader = Node.create<TableHeaderOptions>({\n  name: 'tableHeader',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  content: 'block+',\n\n  addAttributes() {\n    return {\n      colspan: {\n        default: 1,\n      },\n      rowspan: {\n        default: 1,\n      },\n      colwidth: {\n        default: null,\n        parseHTML: element => {\n          const colwidth = element.getAttribute('colwidth')\n          const value = colwidth ? colwidth.split(',').map(width => parseInt(width, 10)) : null\n\n          return value\n        },\n      },\n    }\n  },\n\n  tableRole: 'header_cell',\n\n  isolating: true,\n\n  parseHTML() {\n    return [{ tag: 'th' }]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['th', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACEA,kBAAsC;AAe/B,IAAM,cAAc,iBAAK,OAA2B;AAAA,EACzD,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,gBAAgB,CAAC;AAAA,IACnB;AAAA,EACF;AAAA,EAEA,SAAS;AAAA,EAET,gBAAgB;AACd,WAAO;AAAA,MACL,SAAS;AAAA,QACP,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,QACP,SAAS;AAAA,MACX;AAAA,MACA,UAAU;AAAA,QACR,SAAS;AAAA,QACT,WAAW,aAAW;AACpB,gBAAM,WAAW,QAAQ,aAAa,UAAU;AAChD,gBAAM,QAAQ,WAAW,SAAS,MAAM,GAAG,EAAE,IAAI,WAAS,SAAS,OAAO,EAAE,CAAC,IAAI;AAEjF,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW;AAAA,EAEX,WAAW;AAAA,EAEX,YAAY;AACV,WAAO,CAAC,EAAE,KAAK,KAAK,CAAC;AAAA,EACvB;AAAA,EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WAAO,CAAC,UAAM,6BAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;AAAA,EAC/E;AACF,CAAC;", "names": []}
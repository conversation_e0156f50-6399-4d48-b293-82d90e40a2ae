# api/admin.py

from django.contrib import admin
from .models import (
    GlobalIngredient, FarmerIngredient, NutrientRequirement, 
    Formulation, FormulationIngredient
)

@admin.register(GlobalIngredient)
class GlobalIngredientAdmin(admin.ModelAdmin):
    """
    Customizes the Admin interface for the GlobalIngredient model.
    """
    # Shows these columns in the main list view
    list_display = ('name', 'dry_matter_as_fed', 'crude_protein', 'metabolizable_energy')
    
    # Adds a search bar to search by name
    search_fields = ('name',)
    
    # Organizes the edit/add form into logical sections (fieldsets)
    fieldsets = (
        (None, {
            'fields': ('name', 'description')
        }),
        ('Proximate Analysis', {
            'fields': ('dry_matter_as_fed', 'crude_protein', 'crude_fiber', 'ether_extract', 'ash')
        }),
        ('Energy & Advanced Fiber', {
            'fields': ('ndf', 'adf', 'lignin', 'total_sugars', 'gross_energy', 'metabolizable_energy')
        }),
        ('Amino Acids', {
            'classes': ('collapse',), # Makes this section collapsible
            'fields': ('lysine', 'methionine', 'cysteine', 'threonine', 'tryptophan', 'arginine', 'histidine', 'isoleucine', 'leucine', 'valine')
        }),
        ('Fatty Acids', {
            'classes': ('collapse',),
            'fields': ('oleic_acid', 'linoleic_acid', 'linolenic_acid', 'palmitic_acid', 'stearic_acid')
        }),
        ('Minerals', {
            'classes': ('collapse',),
            'fields': (('ca', 'p', 'k'), ('na', 'cl', 'mg', 's'))
        }),
        ('Trace Minerals (mg/kg)', {
            'classes': ('collapse',),
            'fields': (('mn', 'zn', 'cu'), ('fe', 'se'))
        }),
    )

@admin.register(FarmerIngredient)
class FarmerIngredientAdmin(admin.ModelAdmin):
    """
    Customizes the Admin interface for FarmerIngredient model.
    Inherits the same structure as the GlobalIngredientAdmin for consistency.
    """
    list_display = ('name', 'farmer_id', 'tenant_id', 'crude_protein')
    search_fields = ('name', 'farmer_id')
    list_filter = ('tenant_id',) # Allows filtering by tenant
    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'farmer_id', 'tenant_id')
        }),
        # You can copy the detailed fieldsets from GlobalIngredientAdmin here
        ('Proximate Analysis', {
            'fields': ('dry_matter_as_fed', 'crude_protein', 'crude_fiber', 'ether_extract', 'ash')
        }),
    )

@admin.register(NutrientRequirement)
class NutrientRequirementAdmin(admin.ModelAdmin):
    list_display = ('name', 'min_crude_protein', 'max_crude_protein', 'target_metabolizable_energy')
    search_fields = ('name',)

class FormulationIngredientInline(admin.TabularInline):
    """
    Allows editing FormulationIngredients directly within the Formulation admin page.
    """
    model = FormulationIngredient
    extra = 1 # Show one empty slot to add a new ingredient
    # Make nutrient fields read-only as they are copied
    readonly_fields = [
        'dry_matter_as_fed', 'crude_protein', 'metabolizable_energy', # etc.
    ]


@admin.register(Formulation)
class FormulationAdmin(admin.ModelAdmin):
    list_display = ('name', 'farmer_id', 'expert_id', 'based_on_requirement', 'created_at')
    list_filter = ('tenant_id', 'expert_id')
    search_fields = ('name', 'farmer_id')
    inlines = [FormulationIngredientInline] # Embed the ingredient editor

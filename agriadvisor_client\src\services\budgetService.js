// src/services/budgetService.js

import api from "./api";

// --- Functions for the EXPERT ---

/**
 * Fetches the list of all budgets for an expert's tenant.
 */
export const getBudgets = () => api.get("/budgets/budgets/");

/**
 * Fetches the detailed view of a single budget, including its line items.
 * @param {number} id - The ID of the budget to fetch.
 */
export const getBudgetDetail = (id) => api.get(`/budgets/budgets/${id}/`);

/**
 * Creates a new, empty budget shell assigned to a farmer.
 * @param {object} data - The data for the new budget, e.g., { name, description, farmer }
 */
export const createBudget = (data) => api.post("/budgets/budgets/", data);

/**
 * Creates a new line item (income, var cost, or fix cost) for a budget.
 * @param {object} data - The data for the new item.
 */
export const createBudgetItem = (data) => api.post(`/budgets/items/`, data);

/**
 * Updates an existing budget line item.
 * @param {number} id - The ID of the item to update.
 * @param {object} data - The updated data for the item.
 */
export const updateBudgetItem = (id, data) => api.put(`/budgets/items/${id}/`, data);

/**
 * Deletes a budget line item.
 * @param {number} id - The ID of the item to delete.
 */
export const deleteBudgetItem = (id) => api.delete(`/budgets/items/${id}/`);

// --- Functions for the FARMER ---

/**
 * Fetches all budgets specifically assigned to the logged-in farmer.
 */
export const getMyBudgets = async () => {
  try {
    const response = await api.get("/budgets/farmer/my-budgets/");
    return response.data;
  } catch (error) {
    console.error("Error fetching farmer's budgets:", error);
    throw error;
  }
};



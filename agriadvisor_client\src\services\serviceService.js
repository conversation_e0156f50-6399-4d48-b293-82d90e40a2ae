// src/services/serviceService.js

import api from "./api";

// For Admin
export const getServices = () => api.get("/services/");

export const createService = async (serviceData) => {
  console.log("serviceService: Creating service with data", serviceData);
  try {
    const response = await api.post("/services/", serviceData);
    console.log("serviceService: Create service response", response);
    return response;
  } catch (error) {
    console.error("serviceService: Create service error", error);
    throw error;
  }
};

export const updateService = async (id, serviceData) => {
  console.log("serviceService: Updating service", id, "with data", serviceData);
  try {
    const response = await api.put(`/services/${id}/`, serviceData);
    console.log("serviceService: Update service response", response);
    return response;
  } catch (error) {
    console.error("serviceService: Update service error", error);
    throw error;
  }
};

export const deleteService = (id) => api.delete(`/services/${id}/`);

// For Farmer
export const getFarmerServices = async () => {
  console.log("serviceService: Calling /farmer/services/");
  try {
    const response = await api.get("/farmer/services/");
    console.log("serviceService: Farmer services response", response);
    return response;
  } catch (error) {
    console.error("serviceService: Error calling /farmer/services/", error);
    throw error;
  }
};

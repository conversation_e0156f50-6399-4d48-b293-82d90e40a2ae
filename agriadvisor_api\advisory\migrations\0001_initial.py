# Generated by Django 5.2.5 on 2025-08-26 21:50

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Service',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField()),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.CharField(default='USD', max_length=3)),
                ('duration_minutes', models.PositiveIntegerField(default=60)),
                ('tenant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='services', to='accounts.tenant')),
            ],
        ),
        migrations.CreateModel(
            name='Booking',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('booking_time', models.DateTimeField()),
                ('status', models.CharField(choices=[('pending_payment', 'Pending Payment'), ('confirmed', 'Confirmed'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='pending_payment', max_length=20)),
                ('stripe_payment_intent_id', models.CharField(blank=True, max_length=255, null=True)),
                ('expert', models.ForeignKey(limit_choices_to={'role': 'expert'}, on_delete=django.db.models.deletion.CASCADE, related_name='expert_bookings', to=settings.AUTH_USER_MODEL)),
                ('farmer', models.ForeignKey(limit_choices_to={'role': 'farmer'}, on_delete=django.db.models.deletion.CASCADE, related_name='farmer_bookings', to=settings.AUTH_USER_MODEL)),
                ('tenant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bookings', to='accounts.tenant')),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bookings', to='advisory.service')),
            ],
        ),
    ]

// src/components/AnnouncementModal.jsx

import React, { useState, useEffect } from "react";
import { Modal, Button, Form } from "react-bootstrap";

const AnnouncementModal = ({ show, onHide, onSave, announcement }) => {
  const [formData, setFormData] = useState({
    title: "",
    message: "",
    target_role: "farmer", // Default to farmer
    is_active: false,
  });

  const isEditing = announcement !== null;

  useEffect(() => {
    if (isEditing) {
      setFormData({
        title: announcement.title,
        message: announcement.message,
        target_role: announcement.target_role,
        is_active: announcement.is_active,
      });
    } else {
      setFormData({
        title: "",
        message: "",
        target_role: "farmer",
        is_active: false,
      });
    }
  }, [announcement, show]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <Modal show={show} onHide={onHide} size="lg">
      <Form onSubmit={handleSubmit}>
        <Modal.Header closeButton>
          <Modal.Title>{isEditing ? "Edit Announcement" : "New Announcement"}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form.Group className="mb-3">
            <Form.Label>Title</Form.Label>
            <Form.Control type="text" name="title" value={formData.title} onChange={handleChange} required />
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Message</Form.Label>
            <Form.Control
              as="textarea"
              rows={4}
              name="message"
              value={formData.message}
              onChange={handleChange}
              required
            />
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Target Audience</Form.Label>
            <Form.Select name="target_role" value={formData.target_role} onChange={handleChange}>
              <option value="farmer">Farmers Only</option>
              <option value="expert">Experts Only</option>
              <option value="all">All Users</option>
            </Form.Select>
          </Form.Group>
          <Form.Check
            type="switch"
            id="is-active-switch"
            label="Set this announcement as active"
            name="is_active"
            checked={formData.is_active}
            onChange={handleChange}
          />
          <Form.Text className="text-muted">
            Note: Setting this as active will automatically deactivate any other active announcement for the same
            audience.
          </Form.Text>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={onHide}>
            Cancel
          </Button>
          <Button variant="primary" type="submit">
            Save Announcement
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
};

export default AnnouncementModal;

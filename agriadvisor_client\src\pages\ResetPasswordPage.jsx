// src/pages/ResetPasswordPage.jsx
import React, { useState } from "react";
import { useSearchParams, useNavigate, Link } from "react-router-dom";
import { Container, Form, Button, Card, Alert } from "react-bootstrap";
import { resetPassword } from "../services/authService";

const ResetPasswordPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [password, setPassword] = useState("");
  const [message, setMessage] = useState("");
  const [error, setError] = useState("");

  const token = searchParams.get("token");
  const uid = searchParams.get("uid");

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await resetPassword({ token, uid, password });
      setMessage("Password has been reset successfully! Redirecting to login...");
      setError("");
      setTimeout(() => navigate("/login"), 3000);
    } catch (err) {
      setError("Invalid or expired link. Please request a new one.");
    }
  };

  if (!token || !uid)
    return (
      <Container className="mt-4">
        <Alert variant="danger">Invalid password reset link.</Alert>
      </Container>
    );

  return (
    <Container className="d-flex align-items-center justify-content-center" style={{ minHeight: "100vh" }}>
      <Card style={{ width: "400px" }}>
        <Card.Body>
          <h2 className="text-center mb-4">Set New Password</h2>
          {message && <Alert variant="success">{message}</Alert>}
          {error && <Alert variant="danger">{error}</Alert>}
          {!message && (
            <Form onSubmit={handleSubmit}>
              <Form.Group className="mb-3">
                <Form.Label>New Password</Form.Label>
                <Form.Control type="password" value={password} onChange={(e) => setPassword(e.target.value)} required />
              </Form.Group>
              <Button type="submit" className="w-100">
                Reset Password
              </Button>
            </Form>
          )}
          {message && (
            <div className="text-center mt-3">
              <Link to="/login">Back to Login</Link>
            </div>
          )}
        </Card.Body>
      </Card>
    </Container>
  );
};
export default ResetPasswordPage;

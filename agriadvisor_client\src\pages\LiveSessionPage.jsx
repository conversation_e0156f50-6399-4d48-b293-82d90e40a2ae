// src/pages/LiveSessionPage.jsx
import React from "react";
import { Container, Alert } from "react-bootstrap";

const LiveSessionPage = () => {
  return (
    <Container className="text-center mt-5">
      <Alert variant="info">
        <Alert.Heading>Live Session Feature</Alert.Heading>
        <p>This feature is under construction. When complete, the video chat interface will appear here.</p>
      </Alert>
    </Container>
  );
};
export default LiveSessionPage;

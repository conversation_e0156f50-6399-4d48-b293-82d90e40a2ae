from django.shortcuts import render

# Create your views here.
# announcements/views.py

from rest_framework import viewsets, permissions
from rest_framework.views import APIView
from rest_framework.response import Response
from .models import Announcement
from .serializers import AnnouncementSerializer
from core.views import BaseTenantViewSet

class AnnouncementAdminViewSet(BaseTenantViewSet):
    queryset = Announcement.objects.all()
    serializer_class = AnnouncementSerializer

class CurrentAnnouncementView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    def get(self, request, *args, **kwargs):
        user = request.user
        announcement = Announcement.objects.filter(
            tenant=user.tenant,
            target_role__in=[user.role, 'all'],
            is_active=True
        ).order_by('-created_at').first()
        if announcement:
            serializer = AnnouncementSerializer(announcement)
            return Response(serializer.data)
        else:
            return Response({})


